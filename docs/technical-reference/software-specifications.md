# Software Specifications

## 📋 System Identity
**Operating System**: Linux Mint 22.1 (Ubuntu 24.04 LTS base)
**Kernel**: 6.11.0-26-generic
**Architecture**: x86_64
**Last Updated**: June 7, 2025

---

# 💻 Software & Configuration Stack

## 🏗️ Software Architecture Layers

### 🐧 Operating System Layer
| Component | Version | Base | Architecture | Boot Mode |
|-----------|---------|------|--------------|-----------|
| **Distribution** | Linux Mint 22.1 | Ubuntu 24.04 LTS | x86_64 | UEFI Secure Boot |
| **Kernel** | 6.11.0-26-generic | Modern Linux | 64-bit | Hardware optimized |

### 🛡️ Security Framework Layer
```
Security Stack
├── 🔐 Authentication
│   ├── PAM Framework (system-wide auth)
│   ├── fprintd 1.94.3 (fingerprint service)
│   ├── libfprint 1.94.7+tod1 (Dell OEM drivers)
│   └── libpam-fprintd (PAM integration)
│
├── 🔑 Credential Management
│   ├── GNOME Keyring 46.1 (dual keyring setup)
│   └── libpam-gnome-keyring (auto-unlock)
│
├── 📦 Application Security
│   ├── AppArmor 4.0.1 (44 enforced, 6 complain)
│   └── Container isolation (Docker profiles)
│
└── 🌐 Network Security
    └── UFW Firewall 0.36.2 (enabled, active ✅)
```

### 🔐 Authentication Configuration
| Component | Version | Purpose | Integration | Status |
|-----------|---------|---------|-------------|--------|
| **PAM** | System | Authentication framework | Fingerprint + password | ✅ Active |
| **fprintd** | 1.94.3 | Fingerprint service | Dell Goodix sensor | ✅ Active |
| **GNOME Keyring** | 46.1 | Credential storage | Secure + convenience | ✅ Active |
| **libpam-fprintd** | Latest | PAM fingerprint module | System integration | ✅ Active |
| **libpam-gnome-keyring** | Latest | PAM keyring module | Auto-unlock | ✅ Active |

### 🔧 Service Management Layer
```
System Services
├── 🌐 Network & Connectivity
│   ├── NetworkManager (WiFi, Bluetooth management)
│   ├── bluetooth (Intel AX201 stack)
│   ├── wpa_supplicant (WiFi authentication)
│   ├── systemd-resolved (DNS resolution)
│   └── avahi-daemon (service discovery)
│
├── 🔋 Power & Thermal
│   ├── thermald (CPU temperature control)
│   ├── nvidia-powerd (GPU power management)
│   ├── nvidia-persistenced (GPU state)
│   └── upower (battery/AC monitoring)
│
├── 🖥️ Display & User Interface
│   ├── lightdm (display manager)
│   ├── switcheroo-control (GPU switching)
│   ├── colord (color management)
│   └── touchegg (touch gestures)
│
├── 🔐 Security & Authorization
│   ├── polkit (privilege management)
│   ├── bolt (Thunderbolt security)
│   └── AppArmor (application sandboxing)
│
├── 📦 Container & Virtualization
│   ├── docker (container runtime)
│   ├── containerd (Docker backend)
│   ├── virtlockd (virtualization locks)
│   └── virtlogd (virtualization logging)
│
├── 📊 System Management
│   ├── rsyslog (system logging)
│   ├── systemd-timesyncd (time sync)
│   ├── fwupd (firmware updates)
│   ├── accounts-daemon (user accounts)
│   ├── udisks2 (disk management)
│   ├── irqbalance (CPU load balancing)
│   ├── rtkit-daemon (realtime scheduling)
│   └── iio-sensor-proxy (hardware sensors)
```

### ⚡ Power Management Layer
```
Power Management Stack
├── 🔋 Primary Controller
│   └── TLP 1.6.1 (Dell Precision 5560 optimized)
│       ├── AC Mode: performance governor, CPU boost enabled
│       └── Battery Mode: powersave governor, CPU boost disabled
│
├── 🌡️ Thermal Management
│   ├── thermald (CPU temperature control)
│   └── nvidia-powerd (GPU power management)
│
└── 📊 Analysis Tools
    └── powertop 2.15 (analysis only, no service conflicts)
```

#### 🔧 TLP Power Profiles
| Power Mode | CPU Governor | CPU Boost | Performance Range | Power Optimization |
|------------|-------------|-----------|-------------------|-------------------|
| **🔌 AC Power** | performance | ✅ Enabled | 65-100% | Disabled |
| **🔋 Battery** | powersave | ❌ Disabled | 0-80% | Enabled |

> ⚠️ **Important**: TLP is the active power management solution. powertop is available as an analysis tool only (`sudo powertop --html=report.html`) but should NOT be enabled as a service (`powertop.service`) as it would conflict with TLP.

---

# 📊 Resource Monitoring & Status

## 💾 Current Resource Utilization
| Resource Type | Used | Total Capacity | Available | Usage % | Status |
|---------------|------|----------------|-----------|---------|--------|
| **🧠 Memory** | 15 GiB | 31 GiB | 15 GiB | 47% used | ✅ Healthy |
| **🔄 Swap** | 1.4 GiB | 31 GiB | 29.6 GiB | 4.5% used | ✅ Optimal |
| **💿 Primary Storage** | 791 GB | 916 GB | 79 GB | 91% used | ⚠️ Critical |
| **🔧 EFI Partition** | 74 MB | 96 MB | 22 MB | 77% used | ⚠️ High |

## 📈 Storage Analysis & Recommendations
| Storage Component | Status | Details | Action Required |
|-------------------|--------|---------|-----------------|
| **Primary Drive** | ⚠️ Critical | 91% full (79 GB remaining) | Immediate cleanup |
| **EFI Partition** | ⚠️ High | 77% used (22 MB remaining) | Monitor usage |
| **Cloud Storage** | ✅ Available | OneDrive: 77 GB used / 1.1 TB total | Backup option |
| **Secondary Drive** | ✅ Available | Windows partition available | Dual-boot ready |

---

## 🔧 Development Environment

### 🐍 Python Development Stack
| Component | Version | Purpose | Status |
|-----------|---------|---------|--------|
| **Python** | 3.12+ | Primary development language | ✅ Active |
| **pip** | Latest | Package management | ✅ Active |
| **venv** | Built-in | Virtual environments | ✅ Active |
| **Poetry** | Optional | Advanced dependency management | 📦 Available |

### 🛠️ Development Tools
| Tool | Purpose | Integration | Status |
|------|---------|-------------|--------|
| **Git** | Version control | GitHub integration | ✅ Active |
| **Docker** | Containerization | Development environments | ✅ Active |
| **VS Code** | Primary IDE | Extensions configured | ✅ Active |
| **System CLI** | System management | Custom Python CLI | ✅ Active |

### 📦 Container Environment
| Component | Version | Purpose | Status |
|-----------|---------|---------|--------|
| **Docker Engine** | Latest | Container runtime | ✅ Active |
| **Docker Compose** | Latest | Multi-container orchestration | ✅ Active |
| **containerd** | Latest | Container runtime backend | ✅ Active |

---

## 📦 Software Management & Analysis

### 🔍 Software Conflict Analysis
The system includes automated software conflict detection across multiple package management systems:

| Package Manager | Count | Purpose | Management |
|-----------------|-------|---------|------------|
| **🐧 Debian/APT** | 4,449 | System packages & libraries | `apt`, `dpkg` |
| **📦 Flatpak** | 32 | Sandboxed GUI applications | `flatpak` |
| **🖼️ AppImages** | 7 | Portable applications | Manual management |
| **🐳 Docker** | Various | Containerized services | `docker` |

### ⚠️ Identified Software Conflicts
| Category | Conflict Count | Severity | Recommendation |
|----------|----------------|----------|----------------|
| **Image Editors** | 4 sources | Low | Keep all (different purposes) |
| **Video Players** | 2 sources | Low | Choose preferred player |
| **Text Editors** | 2 sources | Low | Keep nano + vim |
| **CAD Software** | 5 sources | Medium | Remove duplicate AppImages |
| **Development IDEs** | 4 sources | Low | Keep specialized tools |
| **System Monitors** | 7 sources | Medium | Consolidate to 1-2 favorites |

### 🧹 Cleanup Opportunities
- **Orphaned Packages**: 92 packages in 'rc' state (removed but config remains)
- **Duplicate AppImages**: Old versions and backup files
- **Estimated Space Savings**: 2-3 GB from cleanup
- **Automated Cleanup**: Available via `system-cli system software-cleanup`

### 📊 Software Analysis Commands
```bash
# Analyze software conflicts
system-cli system software-conflicts --format table

# View summary only
system-cli system software-conflicts --format summary

# Clean up orphaned packages and duplicates
system-cli system software-cleanup --dry-run

# Execute cleanup interactively
system-cli system software-cleanup --execute --interactive
```

---

## 📋 Software Documentation Summary

This comprehensive software specification provides the complete software foundation for the Dell Precision 5560 system:

### 🏗️ **Software Coverage**
- **🐧 Operating System**: Linux Mint 22.1 with Ubuntu 24.04 LTS base
- **🛡️ Security Stack**: Multi-layered security with PAM, AppArmor, and UFW
- **🔧 Service Management**: Comprehensive systemd service configuration
- **⚡ Power Management**: TLP-optimized power profiles for mobile workstation

### 💻 **Integration Points**
- **🔐 Authentication**: Fingerprint + password with dual keyring architecture
- **📦 Development**: Python-based development environment with containerization
- **🌐 Network**: Modern WiFi 6 and Bluetooth 5.0+ connectivity
- **📊 Monitoring**: Real-time resource monitoring and alerting

> 📖 **Related Documentation**: [Hardware Specifications](hardware-specifications.md) | [System Overview](../system-overview.md) | [Authentication Guide](../user-guides/authentication.md)
