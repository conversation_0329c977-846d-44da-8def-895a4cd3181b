# Software Conflicts and Cleanup Recommendations

## Summary
- **Total Software**: 4,517 packages (4,477 Debian + 32 Flatpak + 8 AppImages)
- **Conflicts Found**: 6 categories with redundant applications
- **Orphaned Packages**: LibreOffice and GIMP remnants in 'rc' state

## Identified Conflicts

### 1. Image Editors (3 sources)
- **Debian**: darktable (RAW photo processor)
- **Flatpak**: GIMP (image manipulation), Inkscape (vector graphics)
- **Recommendation**: Keep all - they serve different purposes

### 2. Video Players (2 sources)
- **Debian**: VLC, Celluloid (MPV frontend)
- **Recommendation**: Choose one - VLC is more feature-complete

### 3. Text Editors (2 sources)
- **Debian**: nano, vim
- **Recommendation**: Keep both - nano for simple editing, vim for advanced

### 4. CAD Software (6 sources)
- **Debian**: Blender (3D modeling), OpenSCAD (parametric CAD)
- **Flatpak**: KiCad (PCB design)
- **AppImages**: CAD Assistant, AstroCAD (2 versions)
- **Recommendation**: Remove duplicate AstroCAD versions

### 5. Development IDEs (4 sources)
- **Flatpak**: Arduino IDE v2, Imaginer, Main Menu
- **AppImage**: Cursor (code editor)
- **Recommendation**: Keep specialized tools, consider consolidating

### 6. System Monitors (7 sources)
- **Debian**: htop, btop, gnome-system-monitor
- **Flatpak**: Tauno Monitor, Field Monitor, Monitorets
- **AppImage**: ActivityWatch
- **Recommendation**: Keep 1-2 favorites, remove others

## Immediate Cleanup Actions

### 1. Remove Orphaned Packages
```bash
# Clean up LibreOffice remnants
sudo apt purge libreoffice*

# Clean up GIMP remnants (you have Flatpak version)
sudo apt purge gimp*

# Clean up any other 'rc' packages
dpkg -l | grep '^rc' | awk '{print $2}' | xargs sudo apt purge
```

### 2. Remove Duplicate AppImages
```bash
# Remove older AstroCAD version
rm ~/.local/share/AppImages/astocad_4ffe1a.appimage

# Clean up old Cursor versions
rm ~/.local/share/AppImages/cursor.appimage.zs-old
rm ~/.local/share/AppImages/cursor.appimage.part
```

### 3. Consider Removing Redundant System Monitors
```bash
# If you prefer btop over htop
sudo apt remove htop

# Remove redundant Flatpak monitors (keep your favorite)
flatpak uninstall art.taunoerik.tauno-monitor
flatpak uninstall de.capypara.FieldMonitor
# or
flatpak uninstall io.github.jorchube.monitorets
```

## Library Conflicts (Normal)
The following library "conflicts" are actually normal:
- **Python 3.x**: Multiple components of Python 3.12 installation
- **Qt5**: Various Qt5 libraries for different applications
- **GTK-3**: Development and runtime components
- **GCC**: Multiple compiler versions for compatibility
- **Node.js**: Core and additional modules

**Action**: No cleanup needed - these are legitimate dependencies.

## Optimization Recommendations

### 1. Prefer Flatpak for GUI Applications
- Better sandboxing and security
- Easier updates and management
- No dependency conflicts with system

### 2. Keep System Tools as Debian Packages
- Better integration with system
- Smaller footprint
- Managed by system package manager

### 3. Use AppImages for Unavailable Software
- Software not in repositories
- Bleeding-edge versions
- Portable applications

## Storage Impact
- **Potential savings**: ~2-3 GB from removing duplicates and orphaned packages
- **AppImage cleanup**: ~1 GB from removing old versions
- **Flatpak cleanup**: ~500 MB from removing redundant monitors

## Next Steps
1. Review recommendations above
2. Test applications before removing to ensure you don't need them
3. Create backups of important configurations
4. Run cleanup commands when ready
5. Monitor system for any issues after cleanup

## Maintenance Tips
- Regularly run `sudo apt autoremove` to clean orphaned dependencies
- Use `flatpak uninstall --unused` to remove unused Flatpak runtimes
- Periodically review installed software with `dpkg -l | wc -l`
- Keep AppImages organized in dedicated directories
