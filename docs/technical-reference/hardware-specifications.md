# Hardware Specifications

## 📋 System Identity
**System**: Dell Precision 5560 (SKU: 0A62)
**Serial**: BCF48G3
**UUID**: 4c4c4544-0043-4610-8034-c2c04f384733
**Last Updated**: June 7, 2025

---

# 🔧 Complete Hardware Architecture

## 🏗️ Unified Hardware Connection Hierarchy

### 🖥️ Dell Precision 5560 - Complete System Tree
```
Dell Precision 5560 Motherboard (Intel Tiger Lake-H + WM590 Chipset)
│
├── 🧠 CPU Complex (Intel Core i7-11850H)
│   ├── 🖥️ Processor: 8 cores, 16 threads @ 2.5-4.8GHz
│   ├── 💾 Memory Controller → 32GB DDR4 (2x16GB SO-DIMM)
│   ├── 🧮 Cache Hierarchy → L1(640KB) → L2(10MB) → L3(24MB)
│   ├── 🖼️ Integrated Graphics (00:02.0)
│   │   ├── Intel TigerLake-H GT1 UHD Graphics
│   │   ├── Driver: i915
│   │   └── Memory: Shared system RAM
│   └── 📡 CPU PCIe Lanes (20 lanes direct)
│       ├── 00:01.0 🔗 PCIe Root Port #1 (Lanes 1-4) → [Available]
│       ├── 00:01.2 🔗 PCIe Root Port #2 (Lanes 5-8) → [Available]
│       ├── 00:06.0 🔗 PCIe Root Port #3 (Lanes 9-12) → [Connected]
│       └── 01:00.0 🎯 NVIDIA T1200 (Lanes 13-16)
│           ├── NVIDIA TU117GLM [T1200 Laptop GPU]
│           ├── Driver: nvidia (proprietary)
│           ├── Memory: 4GB GDDR6
│           ├── Interface: PCIe 4.0 x16 @ x8
│           └── Features: CUDA, Optimus switching
│
├── 🔌 Platform Controller Hub (PCH) - Tiger Lake-H
│   ├── 🏠 00:00.0 Host Bridge
│   │   └── Intel 11th Gen Core Host Bridge/DRAM Controller
│   │
│   ├── 🌡️ 00:04.0 Thermal Management
│   │   ├── Intel Dynamic Platform & Thermal Framework
│   │   ├── Driver: proc_thermal
│   │   └── Function: CPU thermal control
│   │
│   ├── ⚡ Thunderbolt 4 Subsystem
│   │   ├── 00:07.0 🔗 TB4 PCIe Root Port #1
│   │   │   ├── Driver: pcieport
│   │   │   ├── Lanes: 4x PCIe 4.0
│   │   │   ├── Power: USB-C PD (100W)
│   │   │   ├── Protocols: TB4, USB4, USB 3.2, DisplayPort 1.4
│   │   │   └── Security: Intel VT-d DMA protection
│   │   ├── 00:07.3 🔗 TB4 PCIe Root Port #4
│   │   │   ├── Driver: pcieport
│   │   │   ├── Lanes: 4x PCIe 4.0
│   │   │   ├── Power: USB-C PD (100W)
│   │   │   └── Protocols: TB4, USB4, USB 3.2, DisplayPort 1.4
│   │   ├── 00:0d.0 🔌 TB4 USB Controller
│   │   │   ├── Driver: xhci_hcd
│   │   │   └── Function: USB 3.2 via Thunderbolt
│   │   └── 00:0d.3 ⚡ TB4 NHI Controller
│   │       ├── Driver: thunderbolt
│   │       └── Function: Thunderbolt device management
│   │
│   ├── 🧠 00:08.0 AI/ML Accelerator
│   │   ├── Gaussian & Neural Accelerator (GNA)
│   │   └── Function: AI/ML workload acceleration
│   │
│   ├── 📊 00:0a.0 Telemetry Controller
│   │   ├── Intel Volume Management Device (VMD)
│   │   ├── Driver: intel_vsec
│   │   └── Function: System telemetry aggregation
│   │
│   ├── 📡 00:12.0 Sensor Hub
│   │   ├── Intel Integrated Sensor Hub (ISH)
│   │   ├── Driver: intel_ish_ipc
│   │   └── Function: Hardware sensor management
│   │
│   ├── 🔌 USB Subsystem
│   │   ├── 00:14.0 🔌 USB 3.2 xHCI Host Controller
│   │   │   ├── Driver: xhci_hcd
│   │   │   ├── Ports: 10 USB ports (mix of 2.0/3.0/3.2)
│   │   │   └── Connected Devices:
│   │   │       ├── Bus 003:002 👆 Goodix Fingerprint (27c6:63ac)
│   │   │       │   ├── Interface: USB 2.0
│   │   │       │   ├── Driver: libfprint (via fprintd)
│   │   │       │   ├── Integration: PAM authentication
│   │   │       │   └── Status: ✅ Fully functional
│   │   │       ├── Bus 003:003 📷 Microdia Webcam HD (0c45:672e)
│   │   │       │   ├── Interface: USB 2.0
│   │   │       │   ├── Driver: uvcvideo
│   │   │       │   ├── Resolution: 1280x720 (HD + IR)
│   │   │       │   └── Status: ✅ Fully functional
│   │   │       └── Bus 003:004 🔵 Intel AX201 Bluetooth (8087:0026)
│   │   │           ├── Interface: USB 2.0
│   │   │           ├── Driver: btusb
│   │   │           ├── Standard: Bluetooth 5.0+
│   │   │           └── Status: ✅ Active
│   │   └── 00:14.2 💾 Shared SRAM Controller
│   │       └── Function: Shared memory for integrated devices
│   │
│   ├── 🌐 Network Subsystem
│   │   └── 00:14.3 📡 WiFi 6 Controller
│   │       ├── Intel Wi-Fi 6 AX201 CNVi
│   │       ├── Driver: iwlwifi
│   │       ├── Standards: 802.11ax (WiFi 6), WPA3/WPA2
│   │       └── Integration: Combined with Bluetooth module
│   │
│   ├── 🔗 Serial Bus Controllers
│   │   ├── 00:15.0 🔗 I2C Controller #0
│   │   │   ├── Driver: intel-lpss
│   │   │   └── Function: Low-power sensor connectivity
│   │   └── 00:15.1 🔗 I2C Controller #1
│   │       ├── Driver: intel-lpss
│   │       └── Function: Low-power sensor connectivity
│   │
│   ├── 💬 00:16.0 Management Engine
│   │   ├── Tiger Lake-H Management Engine Interface
│   │   ├── Driver: mei_me
│   │   └── Function: Platform security & management
│   │
│   ├── 🔗 00:1c.0 PCIe Root Port #5
│   │   ├── Driver: pcieport
│   │   └── Connected to: Expansion devices
│   │
│   └── 🌉 Legacy I/O Bridge (00:1f.x)
│       ├── 00:1f.0 🌉 ISA Bridge
│       │   ├── Tiger Lake-H LPC/eSPI Controller (WM590)
│       │   └── Function: Legacy device support
│       ├── 00:1f.3 🔊 HD Audio Controller
│       │   ├── Tiger Lake-H cAVS (HD Audio)
│       │   ├── Driver: snd_hda_intel
│       │   └── Codec: Realtek ALC3254
│       ├── 00:1f.4 🔗 SMBus Controller
│       │   ├── Driver: i801_smbus
│       │   └── Function: System management bus
│       └── 00:1f.5 🔗 SPI Controller
│           ├── Driver: intel-spi
│           └── Function: BIOS/UEFI flash access
│
├── 💿 Storage Subsystem
│   ├── 02:00.0 🚀 Primary NVMe Controller
│   │   ├── Micron 2550 NVMe SSD (CT1000P3SSD8)
│   │   ├── Driver: nvme
│   │   ├── Capacity: 1TB (931.5 GB usable)
│   │   ├── Interface: PCIe 4.0 x4
│   │   ├── Usage: 791GB/916GB (91% full) ⚠️
│   │   ├── Partitions:
│   │   │   └── nvme0n1p1: 931.5GB ext4 → / (Linux root)
│   │   └── Performance: High-speed primary storage
│   └── 03:00.0 🔄 Secondary NVMe Controller
│       ├── SK Hynix PC601 NVMe SSD (HFS512GD9TNG-L5B0B)
│       ├── Driver: nvme
│       ├── Capacity: 512GB (476.9 GB usable)
│       ├── Interface: PCIe 3.0 x4
│       ├── Partitions:
│       │   ├── nvme1n1p1: 100MB FAT32 → /boot/efi (EFI System)
│       │   ├── nvme1n1p2: 16MB → Microsoft Reserved
│       │   ├── nvme1n1p3: 413.9GB NTFS → Windows system
│       │   └── nvme1n1p4: 900MB NTFS → Windows Recovery
│       └── Purpose: Dual-boot Windows system
│
└── 🔌 Peripheral Subsystem
    └── 06:00.0 💳 Card Reader Controller
        ├── Realtek RTS5260 PCI Express Card Reader
        ├── Driver: rtsx_pci
        ├── Interface: PCIe 2.0 x1
        ├── Supported: SD, SDHC, SDXC, MMC
        └── Status: Available for use
```

## 🔍 Detailed Hardware Specifications

### 🧠 CPU Complex
| Component | Specification | Details | Performance |
|-----------|---------------|---------|-------------|
| **Processor** | Intel Core i7-11850H | Tiger Lake-H architecture | 8 cores, 16 threads |
| **Base Clock** | 2.50 GHz | Power efficient baseline | TDP optimized |
| **Max Turbo** | 4.8 GHz | Single-core boost | High performance |
| **Cache Hierarchy** | L1: 640 KiB, L2: 10 MiB, L3: 24 MiB | Multi-level caching | Fast access |
| **Memory** | 32 GB DDR4 | High capacity | 15 GiB used / 31 GiB total |
| **Swap** | 32 GB | Virtual memory | 1.4 GiB used / 31 GiB total |

### 🎮 Graphics Subsystem
| GPU Type | Model | PCI Address | Driver | Purpose | Status |
|----------|-------|-------------|--------|---------|--------|
| **Integrated** | Intel TigerLake-H GT1 UHD | 00:02.0 | i915 | Desktop, efficiency | ✅ Active |
| **Discrete** | NVIDIA TU117GLM T1200 | 01:00.0 | nvidia | Professional workloads | ✅ Available |

### 💿 Storage Subsystem
| Drive | Model | Capacity | Technology | Usage | Mount Points |
|-------|-------|----------|------------|-------|--------------|
| **Primary** | Micron CT1000P3SSD8 | 1TB | NVMe PCIe | 791GB/916GB (91%) | / (Linux) |
| **Secondary** | SK Hynix HFS512GD9TNG-L5B0B | 512GB | NVMe PCIe | Dual-boot | /boot/efi, Windows |

#### 📂 Partition Layout
| Device | Size | Type | File System | Mount Point | Usage | Purpose |
|--------|------|------|-------------|-------------|-------|---------|
| **nvme0n1p1** | 931.5 GB | Linux | ext4 | / | 91% full | Linux root |
| **nvme1n1p1** | 100 MB | EFI System | FAT32 | /boot/efi | 77% full | Boot loader |
| **nvme1n1p2** | 16 MB | Microsoft Reserved | - | - | System | Windows reserved |
| **nvme1n1p3** | 413.9 GB | Windows | NTFS | - | Dual-boot | Windows system |
| **nvme1n1p4** | 900 MB | Recovery | NTFS | - | Recovery | Windows recovery |

### 🌐 I/O & Connectivity
| Component | Model | Interface | Driver | Capability | Status |
|-----------|-------|-----------|--------|------------|--------|
| **WiFi** | Intel Tiger Lake PCH CNVi | 00:14.3 | iwlwifi | WiFi 6 (802.11ax) | ✅ Active |
| **Bluetooth** | Intel AX201 | USB 8087:0026 | btusb | Bluetooth 5.0+ | ✅ Active |
| **Thunderbolt** | Tiger Lake-H TB4 | 00:07.0/00:07.3 | thunderbolt | 40 Gbps | ✅ Available |
| **Audio** | Intel Tiger Lake-H HD | 00:1f.3 | snd_hda_intel | Professional audio | ✅ Active |
| **Card Reader** | Realtek RTS5260 | 06:00.0 | rtsx_pci | SD/microSD | ✅ Available |

### 🔐 Security & Sensors
| Device | Model | Interface | Driver | Capability | Status |
|--------|-------|-----------|--------|------------|--------|
| **Fingerprint** | Goodix 27c6:63ac MOC | USB | libfprint | Biometric auth | ✅ Functional |
| **Camera** | Microdia Integrated HD | USB 0c45:672e | uvcvideo | HD + IR | ✅ Functional |
| **Sensor Hub** | Intel ISH | 00:12.0 | intel_ish_ipc | Hardware sensors | ✅ Active |

### 🌡️ Temperature Monitoring & Thermal Management

#### **Sensor Detection Results**
```
Intel Digital Thermal Sensor (coretemp): ✅ Active
Dell SMM Interface (dell_smm): ✅ Active
NVMe Temperature Sensors: ✅ Active
WiFi Module Sensor: ✅ Active
Battery/Power Sensors: ✅ Active
USB-C Power Delivery: ✅ Active
```

#### **Current Thermal Status**
| Component | Current Temp | Normal Range | Critical | Status |
|-----------|--------------|--------------|----------|--------|
| **CPU Package** | 60-64°C | 35-45°C | 100°C | ⚠️ Elevated |
| **CPU Cores** | 56-64°C | 35-45°C | 100°C | ⚠️ Elevated |
| **GPU/Video** | 54°C | 40-50°C | 85°C | ⚠️ Warm |
| **NVMe 1TB** | 42.9°C | <50°C | 84.8°C | ✅ Normal |
| **NVMe 512GB** | 41.9°C | <50°C | 86.8°C | ✅ Normal |
| **Memory** | 46°C | <60°C | 85°C | ✅ Normal |
| **WiFi Module** | 48°C | <60°C | 85°C | ✅ Normal |

#### **Fan Performance**
| Fan | Current Speed | Max Speed | Load % | Status |
|-----|---------------|-----------|--------|--------|
| **CPU Fan** | 4340-4423 RPM | 5400 RPM | ~80% | ⚠️ High |
| **Video Fan** | 4577-4653 RPM | 5700 RPM | ~82% | ⚠️ High |

#### **Thermal Issues Identified**
- **🔥 CPU running hot**: 60-64°C at idle (should be 35-45°C)
- **🌪️ Fans compensating**: Running at 80%+ speed
- **⚡ Performance impact**: CPU locked at 2.5GHz (max 4.8GHz)
- **🔧 Thermal throttling**: Likely occurring to prevent overheating

#### **Sensor Configuration Files**
- **System Config**: `/etc/sensors3.conf`
- **Custom Config**: `sensors_config.conf` (provided)
- **Monitoring Script**: `thermal_monitor.sh` (provided)

---

## 📋 Hardware Documentation Summary

This comprehensive hardware specification provides the complete hardware foundation for the Dell Precision 5560 system:

### 🏗️ **Architecture Coverage**
- **🖥️ Motherboard Layout**: Intel Tiger Lake-H platform with WM590 chipset
- **🚌 Bus Topology**: Complete PCI and USB hierarchy mapping
- **🔗 Interconnects**: PCIe 4.0, USB 3.2, Thunderbolt 4 fabric
- **⚡ Power & Thermal**: Integrated management controllers

### 💻 **Integration Points**
- **🔌 Hardware-Software**: Driver mappings and kernel modules
- **🛡️ Security**: Hardware security features and access control
- **📊 Monitoring**: Resource utilization and performance metrics
- **🔧 Management**: Service integration and power optimization

> 📖 **Related Documentation**: [Software Specifications](software-specifications.md) | [System Overview](../system-overview.md) | [Security Audit](security-audit.md)
