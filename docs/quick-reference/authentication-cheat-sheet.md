# Authentication Quick Reference

**Dell Precision 5560** - Consolidated Authentication System  
**Updated**: December 9, 2024 - Post-Consolidation

---

## 🎯 System Overview

| Component | Purpose | Status |
|-----------|---------|--------|
| **GNOME Keyring** | System authentication only | ✅ Minimal (2 processes) |
| **1Password** | Primary credential store | ✅ Fully integrated |
| **SSH Keys** | Development authentication | ✅ 1Password managed |
| **Fingerprint** | System login | ✅ Active |

---

## 🚀 Quick Commands

### Status Checks
```bash
# Overall authentication status
system-cli keyring status

# 1Password status
op account list

# SSH integration check
ssh-add -l && echo $SSH_AUTH_SOCK

# Git configuration
git config --global credential.helper
```

### Authentication Operations
```bash
# Unlock 1Password
op signin

# Test SSH authentication
ssh-add -l

# Test Git credentials
git credential fill <<< "protocol=https
host=github.com"

# Check fingerprint
fprintd-verify $USER
```

### Troubleshooting
```bash
# Restart authentication services
sudo systemctl restart fprintd
systemctl --user restart gnome-keyring-daemon

# Reset 1Password SSH agent
# (Restart 1Password app)

# Check logs
journalctl -u fprintd -f
journalctl --user -u gnome-keyring-daemon -f
```

---

## 🔄 Authentication Flow

### System Login
```
Fingerprint/Password → PAM → GNOME Keyring Auto-unlock → Desktop Ready
```

### Application Authentication
```
App Request → Check Type → GNOME Keyring (system) OR 1Password (apps)
```

### SSH/Git Operations
```
SSH/Git → 1Password SSH Agent → Biometric/Password → Key Access
```

---

## 📁 Key File Locations

```
~/.ssh/config                           # SSH configuration
~/.1password/agent.sock                 # SSH agent socket
~/.local/share/keyrings/login.keyring   # System keyring
~/.config/keyring-manager/              # Application mapping
~/.config/environment.d/1password.conf  # Environment variables
```

---

## 🔧 Configuration

### SSH Configuration
```bash
# ~/.ssh/config
Host *
    IdentityAgent ~/.1password/agent.sock
    Include ~/.ssh/1Password/config
```

### Git Configuration
```bash
git config --global credential.helper 1password
```

### Environment Variables
```bash
export SSH_AUTH_SOCK=~/.1password/agent.sock
export OP_BIOMETRIC_UNLOCK_ENABLED=true
```

---

## 🛡️ Security Model

| Layer | Technology | Purpose |
|-------|------------|---------|
| **System** | GNOME Keyring + Fingerprint | Login, WiFi, System auth |
| **Applications** | 1Password | SSH, Git, Browsers, APIs |
| **Hardware** | TPM 2.0 + Fingerprint sensor | Hardware-backed security |

---

## 📊 Performance Metrics

| Metric | Value |
|--------|-------|
| **Keyring Processes** | 2 (optimized) |
| **Security Score** | 5/10 (improved) |
| **Vulnerabilities** | 0 (eliminated) |
| **SSH Key Access** | <2s first unlock |
| **Git Operations** | <0.5s cached |

---

## 🆘 Common Issues

| Problem | Quick Fix |
|---------|-----------|
| SSH keys not found | Restart 1Password app |
| Git asks for password | Check `git config credential.helper` |
| Fingerprint fails | `sudo systemctl restart fprintd` |
| WiFi won't connect | Re-login to unlock GNOME keyring |
| 1Password CLI errors | Run `op signin` |

---

## 📋 Maintenance Schedule

| Frequency | Task |
|-----------|------|
| **Weekly** | Verify 1Password + SSH functionality |
| **Monthly** | Check fingerprint + review 1Password security |
| **Quarterly** | Audit credentials + rotate SSH keys |
| **Annually** | Update fingerprint enrollment |

---

## 🔗 Related Documentation

- **[Authentication System Guide](../user-guides/authentication.md)** - Complete user guide
- **[Authentication Flow Reference](../technical-reference/authentication-flow.md)** - Technical details
- **[Security Reference](../security/security-reference.md)** - Security configuration
- **[System CLI Guide](../user-guides/system-cli.md)** - Command reference

---

*Quick reference for the consolidated authentication system. For detailed information, see the full documentation.*
