# System Monitoring Guide

**Dell Precision 5560** - Comprehensive system monitoring including thermal, security, and performance monitoring
**Setup Time**: 10 minutes | **Maintenance**: Fully automated

## 🌡️ Thermal Monitoring (CRITICAL)

### ⚠️ Current Thermal Issues
**URGENT**: System experiencing thermal problems requiring immediate attention
- **CPU Temperature**: 60-64°C (should be 35-45°C at idle)
- **Fan Speed**: 80%+ indicating thermal stress
- **Performance Impact**: CPU capped at 2.5GHz instead of 4.8GHz boost

### Quick Thermal Check
```bash
# View current temperatures
sensors | grep -E "Package|Core|Fan|Video"

# Monitor in real-time
watch -n 2 'sensors | grep -E "Package|Core|Fan|Video"'

# Run comprehensive thermal monitor
./thermal_monitor.sh
```

### Thermal Alert Setup
```bash
# Create thermal alert script
cat > thermal_alert.sh << 'EOF'
#!/bin/bash
TEMP_THRESHOLD=70
CPU_TEMP=$(sensors | grep "Package id 0" | awk '{print $4}' | sed 's/+//;s/°C//')

if (( $(echo "$CPU_TEMP > $TEMP_THRESHOLD" | bc -l) )); then
    notify-send "🔥 Thermal Alert" "CPU temperature: ${CPU_TEMP}°C"
    echo "$(date): THERMAL ALERT - CPU: ${CPU_TEMP}°C" >> ~/logs/thermal_alerts.log
fi
EOF

chmod +x thermal_alert.sh

# Add to crontab for 5-minute checks
(crontab -l 2>/dev/null; echo "*/5 * * * * /path/to/thermal_alert.sh") | crontab -
```

**Complete Guide**: [Thermal Management Guide](thermal-management.md)

---

## 🔒 Automated Security Monitoring

**Comprehensive automated security monitoring system**
**Setup Time**: 5 minutes | **Maintenance**: Fully automated

## 🚀 Quick Setup

### 1. Install Automated Monitoring
```bash
# Install the monitoring system (Python CLI)
system-cli monitor install
```

### 2. Configure Email Alerts (Optional)
```bash
# Setup email notifications
./configure-email-alerts.sh setup

# Test email configuration
./configure-email-alerts.sh test
```

### 3. Verify Installation
```bash
# Check status (Python CLI)
system-cli monitor status

# View scheduled cron jobs
crontab -l | grep security
```

## 📅 Monitoring Schedule

### Daily Monitoring (8:00 AM)
**Automated checks for:**
- ✅ Keyring status and functionality
- ✅ Storage usage (alerts at 85% and 95%)
- ✅ Memory usage (alerts at 80%)
- ✅ Firewall status
- ✅ fail2ban jail status and banned IPs
- ✅ Failed login attempts (alerts at 5+ attempts)
- ✅ Fingerprint service health

**Report Location**: `~/security-audits/daily/`

### Weekly Audits (Sunday 9:00 AM)
**Comprehensive analysis:**
- 🔍 Full security audit
- 🔍 Keyring content analysis
- 🔍 fail2ban activity and ban statistics
- 🔍 Power management analysis (60-minute powertop report)
- 🔍 System service health check
- 🔍 AppArmor profile status
- 🔍 Available package updates

**Report Location**: `~/security-audits/weekly/`

### Monthly Comprehensive Audit (1st of month, 10:00 AM)
**Complete system assessment:**
- 📊 Full security audit with all components
- 📊 System health summary
- 📊 Performance analysis
- 📊 Configuration backup
- 📊 Cleanup of old reports (based on retention policy)

**Report Location**: `~/security-audits/monthly/`

## ⚙️ Configuration

### Main Configuration File
**Location**: `scripts/cron-config.conf`

### Key Settings You Can Customize

#### Output and Storage
```bash
# Base directory for reports
AUDIT_BASE_DIR="/home/<USER>/security-audits"

# Report retention (days)
DAILY_RETENTION=30
WEEKLY_RETENTION=90
MONTHLY_RETENTION=365
```

#### Email Notifications
```bash
# Enable/disable email alerts
ENABLE_EMAIL=false
EMAIL_RECIPIENT="<EMAIL>"

# Notification triggers
NOTIFY_ON_WARNING=true
NOTIFY_ON_ERROR=true
NOTIFY_ON_CRITICAL=true
```

#### Alert Thresholds
```bash
# Storage alerts
STORAGE_WARNING_THRESHOLD=85
STORAGE_CRITICAL_THRESHOLD=95

# Memory alerts
MEMORY_WARNING_THRESHOLD=80

# Security alerts
FAILED_LOGIN_THRESHOLD=5
```

#### Schedule Customization
```bash
# Cron time formats (minute hour day month weekday)
DAILY_CRON_TIME="0 8 * * *"        # 8:00 AM daily
WEEKLY_CRON_TIME="0 9 * * 0"       # 9:00 AM Sunday
MONTHLY_CRON_TIME="0 10 1 * *"     # 10:00 AM 1st of month
```

## 📊 Report Structure

### Directory Layout
```
~/security-audits/
├── daily/
│   ├── daily_report_20250607_080001.txt
│   ├── daily_report_20250608_080001.txt
│   └── ...
├── weekly/
│   ├── weekly_report_20250607_090001.txt
│   ├── power_analysis_20250607_090015.html
│   └── ...
├── monthly/
│   ├── monthly_report_20250601_100001.txt
│   ├── config_backup_20250601/
│   └── ...
└── logs/
    ├── cron-execution.log
    ├── security-alerts.log
    └── crontab_backup_*.txt
```

### Report Content Examples

#### Daily Report Sections
- System information and timestamp
- Keyring status check
- Storage usage analysis
- Memory utilization
- Firewall status
- Failed login attempts summary
- Overall status (PASS/WARNING/CRITICAL)

#### Weekly Report Sections
- Complete security audit results
- Power management analysis
- System service health
- Available package updates
- AppArmor profile status

#### Monthly Report Sections
- Comprehensive security assessment
- System health metrics
- Performance analysis
- Configuration backups
- Cleanup summary

## 🔔 Notification System

### Desktop Notifications
- **Success**: Optional (disabled by default)
- **Warnings**: Enabled (storage, memory, failed logins)
- **Errors**: Enabled (script failures, service issues)
- **Critical**: Enabled (security breaches, system failures)

### Email Notifications
- **Setup required**: Run `./configure-email-alerts.sh setup`
- **Gmail support**: Advanced SMTP configuration available
- **Test function**: Verify email delivery before deployment

### Log Files
- **Execution log**: All cron job executions logged
- **Alert log**: Security alerts and warnings
- **Crontab backups**: Automatic backup before changes

## 🛠️ Management Commands

### Installation Management
```bash
# Install monitoring system
./setup-cron-monitoring.sh install

# Remove monitoring system
./setup-cron-monitoring.sh uninstall

# Check current status
./setup-cron-monitoring.sh status

# Test configuration
./setup-cron-monitoring.sh test
```

### Email Configuration
```bash
# Setup email alerts
./configure-email-alerts.sh setup

# Test email delivery
./configure-email-alerts.sh test

# Disable email alerts
./configure-email-alerts.sh disable

# Check email status
./configure-email-alerts.sh status

# Setup Gmail SMTP (advanced)
./configure-email-alerts.sh gmail
```

### Manual Report Generation
```bash
# Generate daily report manually
./cron-security-wrapper.sh daily

# Generate weekly report manually
./cron-security-wrapper.sh weekly

# Generate monthly report manually
./cron-security-wrapper.sh monthly
```

## 🔧 Troubleshooting

### Common Issues

#### Cron Jobs Not Running
```bash
# Check cron service
sudo systemctl status cron

# Check cron logs
sudo journalctl -u cron

# Verify crontab entries
crontab -l | grep security
```

#### Email Not Working
```bash
# Test mail command
echo "Test" | mail -s "Test" <EMAIL>

# Check mail logs
sudo journalctl | grep mail

# Verify configuration
./configure-email-alerts.sh status
```

#### Reports Not Generated
```bash
# Check permissions
ls -la ~/security-audits/

# Test wrapper script
./cron-security-wrapper.sh daily

# Check execution logs
cat ~/security-audits/logs/cron-execution.log
```

### Manual Cleanup
```bash
# Clean old daily reports (older than 30 days)
find ~/security-audits/daily -name "*.txt" -mtime +30 -delete

# Clean old weekly reports (older than 90 days)
find ~/security-audits/weekly -name "*.txt" -mtime +90 -delete

# Clean old monthly reports (older than 365 days)
find ~/security-audits/monthly -name "*.txt" -mtime +365 -delete
```

## 🎯 Benefits

### Proactive Security
- **Early warning**: Detect issues before they become critical
- **Trend analysis**: Track system health over time
- **Automated response**: Immediate alerts for security events

### Compliance & Auditing
- **Audit trail**: Complete record of system security status
- **Compliance reporting**: Regular security assessments
- **Configuration tracking**: Backup and change detection

### Operational Efficiency
- **Hands-off monitoring**: Fully automated operation
- **Centralized reporting**: All security data in one location
- **Intelligent alerting**: Only notify when action is needed

## 📞 Support

### Getting Help
- **Configuration issues**: Edit `scripts/cron-config.conf`
- **Script problems**: Check `~/security-audits/logs/`
- **Email issues**: Run `./configure-email-alerts.sh status`

### Advanced Configuration
- **Custom thresholds**: Modify alert levels in config file
- **Additional checks**: Add custom scripts to wrapper
- **Integration**: Connect with external monitoring systems

This automated monitoring system provides enterprise-grade security monitoring for your Dell Precision 5560, ensuring continuous oversight of your system's security posture with minimal manual intervention.
