# System CLI

A comprehensive Python CLI for managing all aspects of Linux system administration including keyrings, security, networking, storage, and performance on Dell Precision 5560.

## 🚀 Features

### 🔐 Keyring Management
- **Dual Keyring Setup**: Secure (encrypted) and convenience (passwordless) keyrings
- **Status Monitoring**: Real-time keyring status and health checks
- **Item Management**: Move items between keyrings, list contents
- **Application Mapping**: Configure which apps use which keyring

### 🔒 Security Auditing
- **Comprehensive Audits**: Full system security assessment
- **Keyring Content Analysis**: Safe analysis of stored credentials
- **Security Cleanup**: Remove guest networks and legacy files
- **Automated Monitoring**: Scheduled security checks

### 📊 Monitoring & Automation
- **Cron Integration**: Automated daily/weekly/monthly reports
- **Email Alerts**: Configurable email notifications
- **Dashboard**: Rich terminal dashboard with system overview
- **Report Generation**: Multiple output formats (table, JSON, YAML, markdown)

### ⚙️ System Health
- **SystemD Monitoring**: Check failed units and timers
- **Conflict Detection**: Identify scheduling conflicts
- **Performance Metrics**: System resource monitoring
- **Service Management**: View and manage system services

### 🛠️ Setup & Configuration
- **Interactive Setup**: Guided configuration wizards
- **SSH Key Management**: Generate and configure SSH keys
- **Network Configuration**: NetworkManager keyring integration
- **Validation Testing**: Comprehensive system testing

## 📦 Installation

### Prerequisites

```bash
# Install system dependencies
sudo apt update && sudo apt install -y \
    libsecret-tools \
    gnome-keyring \
    python3-pip \
    python3-venv
```

### Install System CLI

```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install the package
pip install -e .

# Or install from requirements
pip install -r requirements.txt
```

### Quick Setup

```bash
# Set up dual keyring configuration
system-cli setup dual-keyring

# Test the setup
system-cli test dual-keyring

# View system status
system-cli status
```

## 🎯 Quick Start

### Basic Commands

```bash
# Show main dashboard
system-cli status

# Check keyring status
system-cli keyring status

# Unlock secure keyring
system-cli keyring unlock --secure

# Run security audit
system-cli security audit

# Test configuration
system-cli test dual-keyring
```

### Setup Commands

```bash
# Set up dual keyring
system-cli setup dual-keyring

# Configure convenience keyring
system-cli setup convenience-keyring

# Set up SSH keys
system-cli setup ssh-keys

# Configure network manager
system-cli setup network-manager
```

### Security Commands

```bash
# Quick security status
system-cli security status

# Comprehensive audit
system-cli security audit --comprehensive

# Clean up security issues
system-cli security cleanup

# Content audit
system-cli security audit --keyring-only
```

### Monitoring Commands

```bash
# Install automated monitoring
system-cli monitor install --email <EMAIL>

# Check monitoring status
system-cli monitor status

# View recent reports
system-cli monitor reports

# Test configuration
system-cli monitor test
```

### System Commands

```bash
# System health check
system-cli system health

# Check systemd status
system-cli system systemd

# Check for conflicts
system-cli system conflicts

# View system logs
system-cli system logs --lines 50

# Analyze software conflicts
system-cli system software-conflicts

# Clean up redundant software
system-cli system software-cleanup --dry-run
```

### Log Analysis Commands

```bash
# Analyze logs for errors and warnings
system-cli logs analyze --hours 24

# Check error trends
system-cli logs trends --days 7

# Investigate specific patterns
system-cli logs investigate "authentication failure"

# Show analysis summary
system-cli logs summary --format dashboard

# Monitor logs in real-time
system-cli logs monitor --follow --level warning
```

### Software Management Commands

```bash
# Analyze software conflicts across package managers
system-cli system software-conflicts --format table

# View summary of software conflicts
system-cli system software-conflicts --format summary

# Filter conflicts by category
system-cli system software-conflicts --category "System Monitors"

# Show cleanup plan (dry run)
system-cli system software-cleanup --dry-run

# Execute cleanup interactively
system-cli system software-cleanup --execute --interactive

# Batch cleanup of orphaned packages only
system-cli system software-cleanup --execute --orphaned --no-duplicates
```

## 📋 Command Reference

### Keyring Management
- `keyring status` - Show keyring status
- `keyring unlock` - Unlock keyrings
- `keyring lock` - Lock keyrings
- `keyring list-items` - List keyring contents
- `keyring move-item` - Move items between keyrings
- `keyring configure` - Configure application mapping

### Security Auditing
- `security status` - Quick security overview
- `security audit` - Run security audit
- `security cleanup` - Clean up security issues
- `security monitor` - Show monitoring status

### Setup & Configuration
- `setup dual-keyring` - Set up dual keyring
- `setup convenience-keyring` - Configure convenience keyring
- `setup network-manager` - Configure NetworkManager
- `setup ssh-keys` - Set up SSH keys
- `setup reset` - Reset configuration

### Monitoring
- `monitor install` - Install automated monitoring
- `monitor uninstall` - Remove monitoring
- `monitor status` - Show monitoring status
- `monitor configure` - Update configuration
- `monitor reports` - View reports
- `monitor logs` - View monitoring logs

### System Health
- `system health` - Check system health
- `system systemd` - Check systemd status
- `system conflicts` - Check scheduling conflicts
- `system services` - View system services
- `system performance` - Show performance metrics
- `system logs` - View system logs
- `system optimize` - Optimize system
- `system software-conflicts` - Analyze software conflicts and redundancies
- `system software-cleanup` - Clean up orphaned packages and duplicates

### Log Analysis
- `logs analyze` - Analyze logs for errors and warnings
- `logs trends` - Show error trends over time
- `logs investigate` - Investigate specific error patterns
- `logs patterns` - Show available analysis patterns
- `logs monitor` - Monitor logs in real-time
- `logs summary` - Show analysis summary dashboard

### Testing
- `test dual-keyring` - Test dual keyring setup
- `test configuration` - Test configuration
- `test dependencies` - Test system dependencies
- `test permissions` - Test file permissions

## ⚙️ Configuration

### Configuration File

The CLI uses a YAML configuration file located at `~/.config/system-cli/config.yaml`:

```yaml
keyring:
  secure_keyring: "login"
  convenience_keyring: "convenience"
  auto_unlock_convenience: true
  session_timeout: 3600

security:
  audit_frequency: "weekly"
  comprehensive_audit_frequency: "monthly"
  auto_fix_issues: false
  email_alerts: false
  security_score_threshold: 8

monitoring:
  enabled: false
  daily_reports: true
  weekly_reports: true
  monthly_reports: true
  output_directory: "~/security-audits"
  email_notifications: false

system:
  check_systemd: true
  check_conflicts: true
  auto_optimize: false
  performance_monitoring: true
```

## 🔧 Development

### Project Structure

```
system_cli/
├── __init__.py
├── main.py              # Main CLI entry point
├── commands/            # Command modules
│   ├── keyring.py       # Keyring management
│   ├── security.py      # Security auditing
│   ├── setup.py         # Setup commands
│   ├── monitor.py       # Monitoring
│   ├── logs.py          # Log analysis
│   ├── system.py        # System health
│   └── test.py          # Testing
├── core/                # Core functionality
│   ├── keyring_manager.py
│   ├── security_audit.py
│   ├── log_analyzer.py  # Log analysis engine
│   ├── log_patterns.py  # Error pattern definitions
│   ├── software_analyzer.py  # Software conflict analysis
│   ├── config.py
│   └── dashboard.py
└── utils/               # Utilities
    ├── display.py       # Rich formatting
    └── helpers.py       # System helpers
```

## 🎉 Migration from Bash Scripts

This Python CLI replaces the following bash scripts:

- `keyring-manager.sh` → `system-cli keyring`
- `test-dual-keyring.sh` → `system-cli test dual-keyring`
- `security-audit.sh` → `system-cli security audit`
- `comprehensive-security-audit.sh` → `system-cli security audit --comprehensive`
- `setup-secure-keyrings.sh` → `system-cli setup dual-keyring`
- `setup-cron-monitoring.sh` → `system-cli monitor install`
- `systemd_health_check.sh` → `system-cli system health`

## 📞 Support

For issues or questions, please refer to the main project documentation or create an issue in the repository.
