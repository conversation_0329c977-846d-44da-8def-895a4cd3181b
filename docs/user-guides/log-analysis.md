# Log Analysis and Error Detection Guide

**Dell Precision 5560** - Comprehensive log analysis for proactive system monitoring  
**Analysis Time**: Real-time to historical | **Coverage**: All system logs

## 🚀 Quick Start

### 1. Basic Log Analysis
```bash
# Analyze last 24 hours for errors and warnings
system-cli logs analyze

# Analyze specific time period
system-cli logs analyze --hours 48

# Analyze specific service
system-cli logs analyze --service ssh --hours 12
```

### 2. View Analysis Summary
```bash
# Dashboard view with overview
system-cli logs summary

# Quick summary table
system-cli logs summary --format table

# JSON output for automation
system-cli logs summary --format json
```

### 3. Investigate Specific Issues
```bash
# Search for authentication failures
system-cli logs investigate "authentication failure"

# Look for memory issues
system-cli logs investigate "out of memory" --hours 72

# Check for service failures
system-cli logs investigate "service.*failed" --hours 24
```

## 📊 Analysis Features

### Comprehensive Log Coverage
- **SystemD Journal**: All systemd service logs via journalctl
- **Traditional Logs**: /var/log/auth.log, syslog, kern.log, etc.
- **Application Logs**: Service-specific log files
- **Security Logs**: Authentication and access logs

### Error Detection Categories
- 🔴 **Critical System Errors**: Kernel panics, OOM, segfaults
- 🟠 **Security Issues**: Authentication failures, unauthorized access
- 🟡 **Hardware Problems**: Temperature, disk, memory issues
- 🔵 **Network Issues**: Connectivity, DNS, WiFi problems
- 🟢 **Service Failures**: SystemD service and dependency issues
- 🟣 **Performance Warnings**: High load, memory, swap usage

### Analysis Capabilities
- **Pattern Matching**: Advanced regex patterns for error detection
- **Trend Analysis**: Error frequency over time
- **Context Investigation**: Detailed analysis of specific patterns
- **Automated Recommendations**: Suggested fixes for detected issues
- **Real-time Monitoring**: Live log monitoring with alerts

## 🔍 Analysis Commands

### Core Analysis
```bash
# Full system analysis
system-cli logs analyze
  --hours 24              # Time period (default: 24)
  --service ssh           # Specific service
  --level warning         # Minimum log level
  --format table          # Output format: table, json, detailed
  --save                  # Save report to file

# Examples
system-cli logs analyze --hours 48 --format detailed
system-cli logs analyze --service NetworkManager --save
system-cli logs analyze --level error --format json
```

### Trend Analysis
```bash
# Error trends over time
system-cli logs trends
  --days 7                # Days to analyze (default: 7)
  --format table          # Output format: table, json

# Examples
system-cli logs trends --days 14
system-cli logs trends --format json
```

### Pattern Investigation
```bash
# Investigate specific patterns
system-cli logs investigate "pattern"
  --hours 24              # Time period (default: 24)
  --context 5             # Context lines (default: 5)

# Examples
system-cli logs investigate "failed password"
system-cli logs investigate "temperature.*high" --hours 48
system-cli logs investigate "disk.*error" --context 10
```

### Pattern Reference
```bash
# Show all available patterns
system-cli logs patterns

# Show patterns by category
system-cli logs patterns --category security_issues
system-cli logs patterns --category hardware_issues

# Search patterns
system-cli logs patterns --search "authentication"
system-cli logs patterns --search "memory"
```

### Real-time Monitoring
```bash
# Monitor logs in real-time
system-cli logs monitor --follow
  --service ssh           # Monitor specific service
  --level warning         # Minimum level to show

# Examples
system-cli logs monitor --follow --level error
system-cli logs monitor --follow --service ssh
```

## 📋 Analysis Output Formats

### Table Format (Default)
```
┌──────────┬─────────────────┬──────────────────────┬───────────┬───────────┐
│ Severity │ Category        │ Issue                │ Frequency │ Last Seen │
├──────────┼─────────────────┼──────────────────────┼───────────┼───────────┤
│ ERROR    │ Security Issues │ ssh: Authentication  │ 15        │ 14:32:15  │
│          │                 │ failure (15 occur... │           │           │
│ WARNING  │ Hardware Issues │ thermal: High temp   │ 3         │ 14:28:42  │
│          │                 │ warning (3 occur...  │           │           │
└──────────┴─────────────────┴──────────────────────┴───────────┴───────────┘
```

### Detailed Format
```
📊 Log Analysis Report
🕐 Period: Last 24 hours
📁 Files: 8
⚠️  Issues: 12

Issue #1
Severity: ERROR
Category: Security Issues
Title: ssh: Authentication failure (15 occurrences)
Description: Pattern 'authentication failure' detected in ssh...
Frequency: 15 occurrences
Time Range: 2025-06-08 10:15:23 - 2025-06-08 14:32:15

Recommendations:
  • Monitor for brute force attacks
  • Review user account security
  • Consider implementing fail2ban

Investigation Steps:
  • grep 'authentication failure' /var/log/auth.log
  • journalctl -u ssh --since '24 hours ago'
  • Check failed login attempts by IP
```

### JSON Format
```json
{
  "timestamp": "2025-06-08T14:35:12",
  "analysis_period": "Last 24 hours",
  "issues": [
    {
      "severity": "error",
      "category": "security_issues",
      "title": "ssh: Authentication failure",
      "frequency": 15,
      "recommendations": ["Monitor for brute force attacks"],
      "investigation_steps": ["grep 'authentication failure' /var/log/auth.log"]
    }
  ],
  "summary": {
    "total_issues": 12,
    "by_severity": {"error": 5, "warning": 7}
  }
}
```

## 🎯 Error Categories and Patterns

### Critical System Errors
- **Kernel Panic**: `kernel panic|Kernel panic`
- **Out of Memory**: `out of memory|OOM|oom-killer`
- **Segmentation Fault**: `segmentation fault|segfault|SIGSEGV`
- **Filesystem Error**: `filesystem.*error|ext[234].*error`

### Security Issues
- **Authentication Failure**: `authentication failure|auth.*fail`
- **Invalid User**: `invalid.*user|unknown.*user`
- **Sudo Failure**: `sudo.*incorrect password`
- **PAM Error**: `pam_.*authentication failure`

### Hardware Issues
- **High Temperature**: `temperature.*high|thermal.*critical`
- **Disk Error**: `disk.*error|ata.*error|nvme.*error`
- **Memory Error**: `memory.*error|ecc.*error`

### Network Problems
- **Connection Refused**: `connection.*refused|network.*unreachable`
- **WiFi Error**: `wifi.*error|wireless.*error`
- **DNS Issues**: `dns.*error|resolution.*failed`

### Service Failures
- **Service Failed**: `service.*failed|systemd.*failed`
- **Dependency Failed**: `dependency.*failed|wants.*failed`

## 🔧 Investigation Workflows

### Security Incident Investigation
```bash
# 1. Check for authentication failures
system-cli logs investigate "authentication failure" --hours 48

# 2. Look for invalid user attempts
system-cli logs investigate "invalid.*user" --hours 24

# 3. Check sudo failures
system-cli logs investigate "sudo.*incorrect" --hours 24

# 4. Review overall security status
system-cli logs analyze --level error --format detailed
```

### System Performance Investigation
```bash
# 1. Check for memory issues
system-cli logs investigate "out of memory|oom" --hours 72

# 2. Look for high load warnings
system-cli logs investigate "high.*load|cpu.*overload" --hours 48

# 3. Check disk space issues
system-cli logs investigate "disk.*full|no space" --hours 24

# 4. Review performance trends
system-cli logs trends --days 7
```

### Hardware Problem Investigation
```bash
# 1. Check temperature warnings
system-cli logs investigate "temperature.*high" --hours 48

# 2. Look for disk errors
system-cli logs investigate "disk.*error|ata.*error" --hours 72

# 3. Check memory errors
system-cli logs investigate "memory.*error" --hours 48

# 4. Review hardware patterns
system-cli logs patterns --category hardware_issues
```

### Service Failure Investigation
```bash
# 1. Check failed services
system-cli logs investigate "service.*failed" --hours 24

# 2. Look for dependency issues
system-cli logs investigate "dependency.*failed" --hours 24

# 3. Analyze specific service
system-cli logs analyze --service problematic-service --hours 48

# 4. Check systemd status
systemctl --failed
```

## 📈 Automated Recommendations

### Common Issue Resolutions

#### Authentication Failures
- Monitor for brute force attacks
- Review user account security
- Consider implementing fail2ban
- Check for compromised credentials

#### Memory Issues
- Identify memory-intensive processes
- Add swap space if needed
- Consider increasing system RAM
- Review application memory usage

#### Disk Errors
- Run SMART diagnostics
- Check disk health
- Backup critical data immediately
- Consider disk replacement

#### Service Failures
- Check service status and logs
- Restart failed service
- Review service configuration
- Check dependencies

## 🔄 Integration with Monitoring

### Automated Analysis
The log analysis integrates with your existing monitoring system:

```bash
# Add to daily monitoring
system-cli logs analyze --hours 24 --save

# Weekly trend analysis
system-cli logs trends --days 7 --format json

# Critical error alerts
system-cli logs analyze --level critical --format json
```

### Report Storage
Analysis reports are automatically saved to:
```
~/security-audits/logs/
├── log_analysis_all_20250608_143512.json
├── log_analysis_ssh_20250608_140215.json
└── trends_analysis_20250608_135045.json
```

## 🚨 Alert Integration

### Critical Error Detection
The system automatically detects and can alert on:
- Kernel panics and system crashes
- Out of memory conditions
- Security breaches and unauthorized access
- Hardware failures and warnings
- Service failures and dependency issues

### Notification Setup
```bash
# Configure alerts (future enhancement)
system-cli logs monitor --follow --level critical --alert email
system-cli logs monitor --follow --level error --alert desktop
```

## 📞 Troubleshooting

### Common Issues

#### No Issues Detected
```bash
# Check if logs are accessible
sudo ls -la /var/log/

# Verify journalctl access
journalctl --no-pager -n 10

# Check analysis period
system-cli logs analyze --hours 72
```

#### Permission Errors
```bash
# Check log file permissions
ls -la /var/log/auth.log

# Verify user groups
groups $USER

# Use sudo if needed for system logs
sudo system-cli logs analyze
```

#### Pattern Not Found
```bash
# List available patterns
system-cli logs patterns

# Search for similar patterns
system-cli logs patterns --search "keyword"

# Use custom pattern
system-cli logs investigate "your.*custom.*pattern"
```

## 🎯 Best Practices

### Regular Analysis Schedule
- **Daily**: Quick error check for last 24 hours
- **Weekly**: Comprehensive analysis and trend review
- **Monthly**: Full system log analysis and pattern review

### Investigation Approach
1. Start with summary dashboard
2. Identify high-frequency or critical issues
3. Investigate specific patterns in detail
4. Follow recommended investigation steps
5. Implement suggested fixes
6. Monitor for resolution

### Pattern Development
- Create custom patterns for your environment
- Test patterns with known log entries
- Document pattern meanings and fixes
- Share patterns across team members

This log analysis system provides comprehensive visibility into your system's health and security posture, enabling proactive issue detection and resolution.
