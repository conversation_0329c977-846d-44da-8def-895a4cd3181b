# Quick Start Guide

## 🚀 Get Started in 5 Minutes

This guide will get you up and running with System CLI quickly on your Dell Precision 5560.

## 📋 Prerequisites Check

Before starting, verify your system meets the requirements:

```bash
# Check Python version (3.8+ required)
python3 --version

# Check if you have sudo access
sudo -v

# Check if you're on a supported system
lsb_release -a
```

## ⚡ Quick Installation

### 1. <PERSON><PERSON> and <PERSON>stall

```bash
# Clone the repository
git clone https://github.com/bcherrington/cursor-system.git
cd cursor-system

# Quick setup (installs everything)
python3 scripts/dev.py dev-setup
```

### 2. Verify Installation

```bash
# Check if system-cli is working
system-cli --version
system-cli status
```

## 🎯 Essential First Steps

### 1. System Status Check

```bash
# Get overall system status
system-cli status

# Check hardware information
system-cli hardware summary

# Check security status
system-cli security status
```

### 2. Setup Authentication

```bash
# Setup dual keyring architecture
system-cli setup dual-keyring

# Configure fingerprint authentication
system-cli setup fingerprint

# Test authentication
system-cli keyring status
```

### 3. Basic Security Hardening

```bash
# Run security audit
system-cli security audit

# Apply automatic fixes
system-cli security fix --auto

# Enable firewall
system-cli security firewall --enable
```

## 🔧 Common Tasks

### Keyring Management

```bash
# Check keyring status
system-cli keyring status

# Unlock convenience keyring
system-cli keyring unlock convenience

# Lock all keyrings
system-cli keyring lock --all

# Backup keyrings
system-cli keyring backup
```

### System Monitoring

```bash
# View system dashboard
system-cli monitor dashboard

# Check resource usage
system-cli monitor resources

# View system health
system-cli monitor health

# Setup automated monitoring
system-cli setup monitoring
```

### Security Operations

```bash
# Quick security check
system-cli security check

# Full security audit
system-cli security audit

# View security summary
system-cli security summary

# Check for security updates
system-cli security updates
```

## 🎛️ Development Workflow

If you're contributing to the project:

```bash
# Install development dependencies
pip install -e ".[dev,docs]"

# Run tests
python3 scripts/dev.py test all

# Run linting
python3 scripts/dev.py lint all

# Build documentation
python3 scripts/dev.py docs serve

# Full development cycle
python3 scripts/dev.py dev-cycle
```

## 📊 Understanding the Output

### Status Indicators

- ✅ **Green**: Everything working correctly
- ⚠️ **Yellow**: Warning, attention needed
- ❌ **Red**: Error, immediate action required
- 🔄 **Blue**: In progress or informational

### Common Status Messages

```bash
# System Status Examples
✅ Security: 9.5/10 (Excellent)
⚠️ Storage: 91% full (Cleanup needed)
✅ Authentication: Fingerprint + Password
✅ Network: WiFi 6 connected
```

## 🔍 Troubleshooting Quick Fixes

### Installation Issues

```bash
# If installation fails
pip install --upgrade pip setuptools wheel
python3 scripts/dev.py dev-setup --force

# If permissions are wrong
sudo chown -R $USER:$USER ~/.local/share/system-cli
```

### Authentication Issues

```bash
# If fingerprint doesn't work
system-cli setup fingerprint --reset
sudo systemctl restart fprintd

# If keyring issues
system-cli keyring reset --backup
```

### Performance Issues

```bash
# Check system resources
system-cli monitor resources

# Clean up storage
system-cli maintenance cleanup

# Check for updates
system-cli maintenance updates
```

## 📚 What's Next?

Now that you have System CLI running, explore these guides:

### For End Users
- **[Authentication Guide](../user-guides/authentication.md)**: Master secure authentication
- **[System CLI Guide](../user-guides/system-cli.md)**: Complete command reference
- **[Monitoring Guide](../user-guides/monitoring.md)**: Setup comprehensive monitoring

### For System Administrators
- **[Security Audit](../technical-reference/security-audit.md)**: Detailed security assessment
- **[Hardware Specs](../technical-reference/hardware-specifications.md)**: Complete hardware documentation
- **[Software Specs](../technical-reference/software-specifications.md)**: Software stack details

### For Developers
- **[Development Guide](../development/contributing.md)**: Contributing to the project
- **[API Reference](../development/api-reference.md)**: Code documentation
- **[Architecture](../development/architecture.md)**: System design

## 🆘 Getting Help

- **Built-in Help**: `system-cli --help` or `system-cli <command> --help`
- **Documentation**: Browse the complete documentation site
- **Issues**: Report problems on [GitHub Issues](https://github.com/bcherrington/cursor-system/issues)
- **Discussions**: Ask questions on [GitHub Discussions](https://github.com/bcherrington/cursor-system/discussions)

---

*You're now ready to use System CLI! Start with `system-cli status` to see your system overview.*
