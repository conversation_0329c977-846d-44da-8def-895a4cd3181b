# Installation Guide

## 🎯 Overview

This guide covers the complete installation and setup process for the System CLI on Dell Precision 5560 systems running Linux Mint 22.1 or Ubuntu 24.04 LTS.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Ubuntu 20.04+ or Linux Mint 20+ (optimized for Dell Precision 5560)
- **Python**: 3.8 or higher
- **Memory**: 4GB RAM minimum (32GB recommended)
- **Storage**: 2GB free space for installation
- **Privileges**: Sudo access required for system configuration

### Hardware Requirements
- **Dell Precision 5560** (primary target)
- **Fingerprint Reader**: Goodix sensor (for biometric authentication)
- **Network**: WiFi or Ethernet connection
- **Storage**: NVMe SSD recommended for optimal performance

## 🚀 Quick Installation

### Method 1: From Source (Recommended)

```bash
# Clone the repository
git clone https://github.com/bcherrington/cursor-system.git
cd cursor-system

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install in development mode
pip install -e ".[dev]"

# Setup development environment
python3 scripts/dev.py dev-setup

# Verify installation
system-cli --version
system-cli status
```

### Method 2: Using pip (Future)

```bash
# Install from PyPI (when available)
pip install system-cli

# Or install with all features
pip install "system-cli[all]"
```

## 🔧 Configuration

### Initial Setup

```bash
# Run initial system setup
system-cli setup init

# Configure dual keyring architecture
system-cli setup dual-keyring

# Setup fingerprint authentication
system-cli setup fingerprint

# Configure monitoring
system-cli setup monitoring
```

### Security Hardening

```bash
# Run security audit
system-cli security audit

# Apply recommended fixes (includes fail2ban setup)
system-cli security fix --auto

# Setup firewall and intrusion prevention
system-cli security firewall --enable
system-cli security fix-critical --fail2ban

# Verify security status
sudo fail2ban-client status
sudo ufw status
```

## 🧪 Verification

### Test Installation

```bash
# Check system status
system-cli status

# Test keyring functionality
system-cli keyring status

# Run basic security check
system-cli security check

# Test monitoring
system-cli monitor dashboard
```

### Development Setup

```bash
# Install development dependencies
pip install -e ".[dev,docs]"

# Setup pre-commit hooks
pre-commit install

# Run test suite
python3 scripts/dev.py test all

# Build documentation
python3 scripts/dev.py docs serve
```

## 🔍 Troubleshooting

### Common Issues

#### Permission Errors
```bash
# Fix permission issues
sudo chown -R $USER:$USER ~/.local/share/system-cli
sudo usermod -a -G docker $USER
```

#### Fingerprint Setup Issues
```bash
# Check fingerprint device
system-cli hardware fingerprint

# Reset fingerprint configuration
system-cli setup fingerprint --reset
```

#### Keyring Issues
```bash
# Reset keyring configuration
system-cli keyring reset --backup

# Check keyring status
system-cli keyring status --verbose
```

## 📚 Next Steps

After successful installation:

1. **[Getting Started](../getting-started/quick-start.md)**: Learn basic usage
2. **[Authentication Guide](../user-guides/authentication.md)**: Setup secure authentication
3. **[System CLI Guide](../user-guides/system-cli.md)**: Complete CLI reference
4. **[Monitoring Guide](../user-guides/monitoring.md)**: Setup system monitoring

## 🆘 Support

- **Documentation**: Browse the complete [documentation](../index.md)
- **Issues**: Report bugs on [GitHub Issues](https://github.com/bcherrington/cursor-system/issues)
- **Discussions**: Join [GitHub Discussions](https://github.com/bcherrington/cursor-system/discussions)
