# Contributing Guide

## 🤝 Welcome Contributors!

Thank you for your interest in contributing to System CLI! This guide will help you get started with development and contribution workflows.

## 🚀 Quick Start for Contributors

### 1. Development Environment Setup

```bash
# Fork and clone the repository
git clone https://github.com/YOUR_USERNAME/cursor-system.git
cd cursor-system

# Set up development environment
python3 scripts/dev.py dev-setup

# Install pre-commit hooks
pre-commit install

# Verify setup
python3 scripts/dev.py test all
```

### 2. Development Workflow

```bash
# Create a feature branch
git checkout -b feature/your-feature-name

# Make your changes
# ... edit code ...

# Run quality checks
python3 scripts/dev.py lint all
python3 scripts/dev.py test all

# Build documentation
python3 scripts/dev.py docs build

# Commit your changes
git add .
git commit -m "feat: add your feature description"

# Push and create PR
git push origin feature/your-feature-name
```

## 📋 Development Guidelines

### Code Style

We use automated code formatting and linting:

```bash
# Format code
python3 scripts/dev.py format

# Run linting
python3 scripts/dev.py lint all

# Type checking
python3 scripts/dev.py lint mypy
```

### Testing

All contributions must include tests:

```bash
# Run all tests
python3 scripts/dev.py test all

# Run specific test categories
python3 scripts/dev.py test unit
python3 scripts/dev.py test integration

# Run with coverage
python3 scripts/dev.py test coverage
```

### Documentation

Update documentation for any user-facing changes:

```bash
# Build and serve documentation locally
python3 scripts/dev.py docs serve

# Create new documentation pages
python3 scripts/dev.py docs new page-name --section user-guides

# Validate documentation
python3 scripts/dev.py docs check
```

## 🏗️ Project Structure

```
cursor-system/
├── src/system_cli/           # Main package source
│   ├── commands/             # CLI command implementations
│   ├── core/                 # Core functionality
│   ├── utils/                # Utility functions
│   └── models/               # Data models
├── tests/                    # Test suite
│   ├── unit/                 # Unit tests
│   ├── integration/          # Integration tests
│   └── e2e/                  # End-to-end tests
├── docs/                     # Documentation
├── scripts/                  # Development scripts
└── pyproject.toml           # Project configuration
```

## 🎯 Contribution Areas

### 🐛 Bug Fixes
- Check existing issues on GitHub
- Include reproduction steps
- Add regression tests
- Update documentation if needed

### ✨ New Features
- Discuss in GitHub Discussions first
- Follow existing patterns
- Include comprehensive tests
- Update user documentation

### 📚 Documentation
- Fix typos and improve clarity
- Add examples and use cases
- Update API documentation
- Improve getting started guides

### 🧪 Testing
- Increase test coverage
- Add integration tests
- Improve test reliability
- Add performance tests

## 🔧 Development Commands

### Using the Dev CLI

```bash
# Get help for all commands
python3 scripts/dev.py --help

# Development workflow
python3 scripts/dev.py dev-cycle        # Full development cycle
python3 scripts/dev.py dev-setup        # Setup development environment

# Code quality
python3 scripts/dev.py format           # Format code
python3 scripts/dev.py lint all         # Run all linting
python3 scripts/dev.py test all         # Run all tests

# Documentation
python3 scripts/dev.py docs serve       # Serve documentation
python3 scripts/dev.py docs build       # Build documentation
python3 scripts/dev.py docs deploy      # Deploy to GitHub Pages

# Environment info
python3 scripts/dev.py env-info         # Show environment information
```

## 📝 Commit Guidelines

### Commit Message Format

```
type(scope): description

[optional body]

[optional footer]
```

### Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Examples
```
feat(keyring): add dual keyring support
fix(auth): resolve fingerprint authentication issue
docs(api): update CLI command documentation
test(security): add security audit test coverage
```

## 🔍 Code Review Process

### Before Submitting PR
1. ✅ All tests pass
2. ✅ Code is formatted and linted
3. ✅ Documentation is updated
4. ✅ Commit messages follow guidelines
5. ✅ PR description is clear and complete

### PR Requirements
- Clear description of changes
- Link to related issues
- Screenshots for UI changes
- Test coverage for new code
- Documentation updates

## 🧪 Testing Guidelines

### Test Categories

```bash
# Unit tests - Fast, isolated tests
tests/unit/

# Integration tests - Component interaction tests
tests/integration/

# End-to-end tests - Full workflow tests
tests/e2e/
```

### Writing Tests

```python
# Example unit test
def test_keyring_status():
    """Test keyring status functionality."""
    keyring = KeyringManager()
    status = keyring.get_status()
    assert status.is_available
    assert status.keyring_count >= 0

# Example integration test
def test_authentication_flow():
    """Test complete authentication workflow."""
    auth = AuthenticationManager()
    result = auth.authenticate_user("test_user")
    assert result.success
    assert result.method in ["fingerprint", "password"]
```

## 🚀 Release Process

### Version Management
- Follow semantic versioning (SemVer)
- Update version in `pyproject.toml`
- Create release notes
- Tag releases in Git

### Release Checklist
1. ✅ All tests pass
2. ✅ Documentation is up to date
3. ✅ Version is bumped
4. ✅ Release notes are prepared
5. ✅ Security audit passes

## 🆘 Getting Help

### Development Support
- **GitHub Discussions**: Ask questions and discuss ideas
- **GitHub Issues**: Report bugs and request features
- **Dev CLI Help**: `python3 scripts/dev.py --help`
- **Documentation**: Browse the complete documentation

### Community Guidelines
- Be respectful and inclusive
- Help others learn and grow
- Share knowledge and best practices
- Follow the code of conduct

---

*Thank you for contributing to System CLI! Your contributions help make Linux system administration more accessible and secure.*
