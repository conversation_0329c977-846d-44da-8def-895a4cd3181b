# API Reference

## 📚 Overview

This section provides comprehensive API documentation for System CLI, automatically generated from code docstrings using mkdocstrings.

## 🏗️ Core Modules

### System CLI Main Module

::: system_cli
    options:
      show_root_heading: true
      show_source: false
      heading_level: 3

### Core Modules

#### Configuration Management

::: system_cli.core.config
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

#### Keyring Management

::: system_cli.core.keyring_manager
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

#### Security Auditing

::: system_cli.core.security_auditor
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

#### System Health

::: system_cli.core.system_health
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

### Command Modules

#### Keyring Commands

::: system_cli.commands.keyring
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

#### Security Commands

::: system_cli.commands.security
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

#### System Commands

::: system_cli.commands.system
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

#### Monitor Commands

::: system_cli.commands.monitor
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

### Data Models

#### Configuration Models

::: system_cli.models.config
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

#### Security Models

::: system_cli.models.security
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

#### System Models

::: system_cli.models.system
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

### Utility Modules

#### Display Utilities

::: system_cli.utils.display
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

#### Helper Functions

::: system_cli.utils.helpers
    options:
      show_root_heading: true
      show_source: true
      heading_level: 4

## 🛡️ Security Components

### Authentication System
- **Fingerprint Authentication**: Integration with fprintd and PAM
- **Keyring Management**: Dual keyring architecture (secure/convenience)
- **Password Management**: Secure credential storage and retrieval

### Security Auditing
- **System Hardening**: Automated security configuration checks
- **Vulnerability Assessment**: Security score calculation and reporting
- **Compliance Monitoring**: Ongoing security status monitoring

## 📊 Monitoring Components

### System Health Monitoring
- **Resource Monitoring**: CPU, memory, storage, and network usage
- **Performance Metrics**: System performance tracking and alerting
- **Health Checks**: Automated system health verification

### Security Monitoring
- **Authentication Events**: Login and authentication monitoring
- **System Changes**: Configuration and file system monitoring
- **Network Activity**: Network connection and firewall monitoring

## 🔌 Hardware Integration

### Hardware Detection
- **Device Enumeration**: Automatic hardware detection and cataloging
- **Driver Status**: Hardware driver status and compatibility checking
- **Capability Assessment**: Hardware feature detection and validation

### Device Management
- **Power Management**: TLP integration and power optimization
- **Thermal Management**: Temperature monitoring and thermal control
- **Peripheral Control**: USB, Thunderbolt, and other peripheral management

## 📝 Usage Examples

### Basic API Usage

```python
from system_cli.core import SystemManager
from system_cli.core.keyring import KeyringManager
from system_cli.core.security import SecurityAuditor

# Initialize system manager
system = SystemManager()

# Get system status
status = system.get_status()
print(f"System health: {status.health_score}/10")

# Manage keyrings
keyring = KeyringManager()
keyring_status = keyring.get_status()
print(f"Keyrings available: {keyring_status.keyring_count}")

# Run security audit
auditor = SecurityAuditor()
audit_result = auditor.run_audit()
print(f"Security score: {audit_result.score}/10")
```

### Advanced Configuration

```python
from system_cli.config import ConfigManager
from system_cli.models import SystemConfig

# Load configuration
config_manager = ConfigManager()
config = config_manager.load_config()

# Modify configuration
config.monitoring.enabled = True
config.security.auto_fix = False

# Save configuration
config_manager.save_config(config)
```

### Custom Commands

```python
from system_cli.commands.base import BaseCommand
from system_cli.models import CommandResult

class CustomCommand(BaseCommand):
    """Custom command implementation."""
    
    def execute(self, args) -> CommandResult:
        """Execute the custom command."""
        # Implementation here
        return CommandResult(
            success=True,
            message="Custom command executed successfully"
        )
```

## 🧪 Testing Framework

### Test Organization
The project uses pytest for testing with the following structure:

- **Unit Tests**: Fast, isolated tests for individual functions and classes
- **Integration Tests**: Tests for component interactions and workflows
- **End-to-End Tests**: Complete system workflow testing

### Test Utilities
- **Mock Objects**: Standardized mocks for external dependencies
- **Test Fixtures**: Reusable test data and system states
- **Test Helpers**: Common testing utilities and assertions

## 📋 Development Standards

### Code Quality
- **Type Hints**: All public APIs include comprehensive type annotations
- **Docstrings**: Google-style docstrings for all public functions and classes
- **Error Handling**: Consistent exception handling and error reporting
- **Logging**: Structured logging throughout the application

### Exception Handling
- **Custom Exceptions**: Domain-specific exception classes for clear error reporting
- **Error Recovery**: Graceful error handling with recovery suggestions
- **User-Friendly Messages**: Clear, actionable error messages for end users

## 🔍 Development Notes

### Code Documentation Standards

All public APIs should include:

- **Docstrings**: Google-style docstrings for all public functions and classes
- **Type Hints**: Complete type annotations for all parameters and return values
- **Examples**: Usage examples in docstrings where appropriate
- **Error Handling**: Documented exceptions and error conditions

### Contributing to API Documentation

When adding new APIs:

1. **Add comprehensive docstrings** with examples
2. **Include type hints** for all parameters and returns
3. **Document exceptions** that may be raised
4. **Add usage examples** in the docstring
5. **Update this reference** if adding new modules

### Docstring Format

```python
def example_function(param1: str, param2: int = 0) -> bool:
    """Brief description of the function.
    
    Longer description with more details about what the function does,
    its purpose, and any important implementation notes.
    
    Args:
        param1: Description of the first parameter.
        param2: Description of the second parameter with default value.
        
    Returns:
        Description of the return value and its type.
        
    Raises:
        ValueError: When param1 is empty.
        RuntimeError: When the operation fails.
        
    Example:
        Basic usage example:
        
        ```python
        result = example_function("test", 42)
        if result:
            print("Success!")
        ```
    """
    # Implementation here
    pass
```

---

*This API reference is automatically generated from code docstrings. For the most up-to-date information, refer to the source code and inline documentation.*
