# Development CLI for System CLI Project

This Python CLI script (`scripts/dev.py`) replaces the traditional Makefile with a more powerful, user-friendly, and cross-platform development tool.

**Location**: `scripts/dev.py`

## Features

- **Rich Console Output**: Beautiful colored output with progress indicators
- **Better Error Handling**: Clear error messages and graceful failure handling
- **Cross-Platform**: Works on Linux, macOS, and Windows
- **Organized Commands**: Logical grouping of related commands
- **Interactive Help**: Comprehensive help system with detailed command information
- **Dependency Checking**: Automatic verification of required tools

## Installation

### Prerequisites

Install the minimal dependencies needed for the dev script:

```bash
pip install -r scripts/dev-requirements.txt
```

Or install manually:

```bash
pip install typer rich
```

### Make Script Executable

```bash
chmod +x scripts/dev.py
```

## Usage

### Basic Usage

```bash
# Show main help
python3 scripts/dev.py --help

# Show version
python3 scripts/dev.py --version

# Run without arguments to see organized command overview
python3 scripts/dev.py
```

### Command Groups

#### Installation Commands

```bash
# Install package in development mode
python3 dev.py install base

# Install with development dependencies
python3 dev.py install dev

# Install with all dependencies
python3 dev.py install all
```

#### Testing Commands

```bash
# Run full test suite with coverage
python3 dev.py test all

# Run tests quickly without coverage
python3 dev.py test fast

# Run specific test types
python3 dev.py test unit
python3 dev.py test integration
python3 dev.py test e2e
```

#### Linting and Formatting Commands

```bash
# Run all linting tools
python3 dev.py lint all

# Format code with black and isort
python3 dev.py lint format

# Run type checking with mypy
python3 dev.py lint type-check

# Run security checks
python3 dev.py lint security
```

#### Build Commands

```bash
# Build distribution packages
python3 dev.py build dist

# Clean build artifacts
python3 dev.py build clean

# Check package metadata
python3 dev.py build check
```

#### Documentation Commands

```bash
# Build documentation
python3 dev.py docs build

# Serve documentation locally
python3 dev.py docs serve
```

#### CLI Testing Commands

```bash
# Test CLI installation and basic commands
python3 dev.py cli test

# Run CLI demo commands
python3 dev.py cli demo
```

#### Dependency Management Commands

```bash
# Update all dependencies
python3 dev.py deps update

# Check dependencies for issues
python3 dev.py deps check

# Generate requirements files
python3 dev.py deps requirements
```

#### Version Management Commands

```bash
# Bump version numbers
python3 dev.py version patch
python3 dev.py version minor
python3 dev.py version major
```

#### Coverage Commands

```bash
# Generate coverage report
python3 dev.py coverage report

# Generate HTML coverage report
python3 dev.py coverage html
```

### Standalone Commands

```bash
# Set up development environment
python3 dev.py dev-setup

# Run pre-commit hooks
python3 dev.py pre-commit

# Run all quality assurance checks
python3 dev.py qa

# Run full development cycle
python3 dev.py dev-cycle

# Show environment information
python3 dev.py env-info

# Verify installation
python3 dev.py verify-install

# Run performance benchmarks
python3 dev.py benchmark
```

### Help Commands

```bash
# Get detailed help for specific command groups
python3 dev.py help test
python3 dev.py help lint
python3 dev.py help build
```

## Comparison with Makefile

### Advantages of the Python CLI

1. **Better Error Handling**: Clear error messages with colored output
2. **Cross-Platform**: Works on Windows, macOS, and Linux
3. **Rich Output**: Progress indicators, tables, and colored text
4. **Dependency Checking**: Automatic verification of required tools
5. **Interactive Help**: Comprehensive help system
6. **Extensibility**: Easy to add new commands and features
7. **Type Safety**: Python type hints for better code quality

### Migration from Makefile

The dev script provides equivalent functionality to all Makefile targets:

| Makefile Target | Dev CLI Command |
|----------------|-----------------|
| `make install` | `python3 dev.py install base` |
| `make install-dev` | `python3 dev.py install dev` |
| `make test` | `python3 dev.py test all` |
| `make test-fast` | `python3 dev.py test fast` |
| `make lint` | `python3 dev.py lint all` |
| `make format` | `python3 dev.py lint format` |
| `make build` | `python3 dev.py build dist` |
| `make clean` | `python3 dev.py build clean` |
| `make docs` | `python3 dev.py docs build` |
| `make serve-docs` | `python3 dev.py docs serve` |
| `make qa` | `python3 dev.py qa` |
| `make dev-cycle` | `python3 dev.py dev-cycle` |

## Development Workflow

### Typical Development Cycle

```bash
# 1. Set up development environment (first time only)
python3 dev.py dev-setup

# 2. Make code changes...

# 3. Format and lint code
python3 dev.py lint format

# 4. Run tests
python3 dev.py test all

# 5. Build package
python3 dev.py build dist

# Or run the full cycle at once
python3 dev.py dev-cycle
```

### Quality Assurance

```bash
# Run comprehensive QA checks
python3 dev.py qa
```

This runs:
- All linting tools
- Full test suite
- Security checks

## Customization

The dev script can be easily extended by:

1. Adding new commands to existing groups
2. Creating new command groups
3. Modifying the `run_command` function for different behavior
4. Adding new utility functions

## Troubleshooting

### Common Issues

1. **Command not found**: Ensure the required tool is installed
2. **Permission denied**: Make sure the script is executable (`chmod +x dev.py`)
3. **Import errors**: Install the required dependencies (`pip install typer rich`)

### Environment Information

```bash
# Check your development environment
python3 dev.py env-info
```

This shows:
- Python version and path
- Pip version and path
- Virtual environment status
- Key package versions

## Future Enhancements

Planned improvements:
- Configuration file support
- Plugin system for custom commands
- Integration with CI/CD systems
- Performance monitoring
- Automated dependency updates
