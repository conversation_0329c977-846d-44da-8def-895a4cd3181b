# TPM Emergency Reference Card

## 🚨 EMER<PERSON>NCY UNLOCK PROCEDURES

### If You're Locked Out RIGHT NOW:

```bash
# Method 1: Direct password unlock (ALWAYS WORKS)
gnome-keyring-daemon --unlock

# Method 2: System-CLI emergency
system-cli keyring unlock-manual

# Method 3: Service restart + unlock
systemctl --user restart gnome-keyring-daemon
gnome-keyring-daemon --unlock
```

## 🔥 CRITICAL EMERGENCY COMMANDS

### Complete TPM Failure Recovery
```bash
# Nuclear option - disables TPM completely
system-cli keyring emergency-recovery --action emergency --force
```

### TPM Hanging/Frozen
```bash
# Kill TPM processes
pkill -f tpm-unlock-keyring
pkill -f clevis

# Restart keyring
systemctl --user restart gnome-keyring-daemon
gnome-keyring-daemon --unlock
```

### Service Not Responding
```bash
# Force restart everything
pkill -f keyring
systemctl --user restart gnome-keyring-daemon
gnome-keyring-daemon --unlock
```

## 📞 QUICK DIAGNOSTICS

### Check Status
```bash
# Overall status
system-cli keyring emergency-recovery --action status

# Test fallbacks
system-cli keyring emergency-recovery --action test

# Keyring daemon status
systemctl --user status gnome-keyring-daemon
```

### Check if Keyring is Unlocked
```bash
# Test access
secret-tool search keyring login

# If this works, keyring is unlocked
# If this fails, keyring is locked
```

## 🛠️ DISABLE TPM INTEGRATION

### Graceful Disable (Recommended)
```bash
system-cli keyring emergency-recovery --action disable
```

### Manual Disable
```bash
# Stop services
systemctl --user stop tpm-keyring-unlock-enhanced.service
systemctl --user disable tpm-keyring-unlock-enhanced.service

# Backup config
mv ~/.config/tmp-keyring ~/.config/tpm-keyring.disabled.$(date +%Y%m%d)

# Restart keyring
systemctl --user restart gnome-keyring-daemon
```

## 🔧 RECOVERY FILE LOCATIONS

### Emergency Script
```bash
~/.config/tpm-keyring/emergency-recovery.sh emergency
```

### Log Files (for troubleshooting)
```bash
# TPM logs
tail -f ~/.cache/tpm-keyring-enhanced.log

# System logs
journalctl --user -u gnome-keyring-daemon -f
```

## ⚡ ONE-LINER FIXES

```bash
# Fix 1: Complete reset
pkill -f keyring && systemctl --user restart gnome-keyring-daemon && gnome-keyring-daemon --unlock

# Fix 2: TPM disable + unlock
system-cli keyring emergency-recovery --action disable && gnome-keyring-daemon --unlock

# Fix 3: Emergency recovery
system-cli keyring emergency-recovery --action emergency --force

# Fix 4: Service restart
systemctl --user restart gnome-keyring-daemon && gnome-keyring-daemon --unlock
```

## 🎯 REMEMBER

1. **Password unlock ALWAYS works** - TPM never replaces password authentication
2. **Emergency script is your friend** - `~/.config/tpm-keyring/emergency-recovery.sh`
3. **System-CLI has emergency commands** - `system-cli keyring emergency-recovery`
4. **When in doubt, restart the service** - `systemctl --user restart gnome-keyring-daemon`

## 📱 SAVE THIS COMMAND

**The most important command to remember:**
```bash
gnome-keyring-daemon --unlock
```
**This ALWAYS works and will prompt for your password.**

---

*Keep this reference handy - print it out or bookmark it!*
