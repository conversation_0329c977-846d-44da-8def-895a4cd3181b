# TPM Keyring Integration: Fallback & Recovery Guide

## Overview

This guide provides comprehensive information about fallback mechanisms and recovery procedures for TPM-enabled keyring integration. **You will never be locked out** of your system when following these procedures.

## 🔐 Authentication Hierarchy

The TPM integration implements a **robust fallback cascade** that ensures you always have access to your keyring:

```
1st Priority: TPM Hardware Unlock (automatic, seamless)
     ↓ (if TPM fails)
2nd Priority: Fingerprint Authentication (if configured)
     ↓ (if fingerprint fails)
3rd Priority: Password Authentication (always available)
     ↓ (if password fails)
4th Priority: Emergency Recovery (manual intervention)
```

## 🛡️ Built-in Safety Mechanisms

### 1. **Password Fallback Always Available**
- Password authentication is **never disabled**
- TPM enhances security, doesn't replace basic authentication
- Manual unlock always works: `gnome-keyring-daemon --unlock`

### 2. **Timeout Protection**
- All TPM operations have timeouts (default: 10 seconds)
- System won't hang if TPM becomes unresponsive
- Automatic fallback to next method

### 3. **Multiple Recovery Methods**
- Emergency recovery script
- System-CLI emergency commands
- Manual service restart procedures
- Complete TPM disable option

## 🚀 Quick Recovery Commands

### Immediate Unlock (If Locked Out)
```bash
# Method 1: Direct password unlock
gnome-keyring-daemon --unlock

# Method 2: Using system-cli
system-cli keyring unlock-manual

# Method 3: Restart keyring service
systemctl --user restart gnome-keyring-daemon
gnome-keyring-daemon --unlock
```

### Emergency TPM Disable
```bash
# Method 1: Using system-cli
system-cli keyring emergency-recovery --action emergency --force

# Method 2: Using emergency script
~/.config/tpm-keyring/emergency-recovery.sh emergency

# Method 3: Manual disable
systemctl --user stop tpm-keyring-unlock-enhanced.service
systemctl --user disable tpm-keyring-unlock-enhanced.service
```

## 📋 Detailed Recovery Procedures

### Scenario 1: TPM Not Responding
**Symptoms:** System hangs during login, TPM unlock times out

**Solution:**
1. **Immediate:** Press Ctrl+C to cancel TPM unlock
2. **Fallback:** System automatically prompts for password
3. **Manual:** Run `gnome-keyring-daemon --unlock`

**Prevention:** Enhanced script has 10-second timeout built-in

### Scenario 2: TPM Hardware Failure
**Symptoms:** TPM commands fail, hardware errors in logs

**Solution:**
```bash
# Step 1: Emergency recovery
system-cli keyring emergency-recovery --action emergency

# Step 2: Verify keyring works
secret-tool search keyring login

# Step 3: Disable TPM integration
system-cli keyring emergency-recovery --action disable
```

### Scenario 3: Corrupted TPM Configuration
**Symptoms:** TPM setup exists but doesn't work

**Solution:**
```bash
# Step 1: Backup current config
mv ~/.config/tpm-keyring ~/.config/tpm-keyring.backup.$(date +%Y%m%d)

# Step 2: Restart keyring normally
systemctl --user restart gnome-keyring-daemon

# Step 3: Unlock with password
gnome-keyring-daemon --unlock

# Step 4: Reconfigure TPM (optional)
system-cli keyring tpm-setup-enhanced
```

### Scenario 4: Complete System Failure
**Symptoms:** Nothing works, all methods fail

**Solution:**
```bash
# Nuclear option: Reset everything
pkill -f keyring
pkill -f tpm
systemctl --user stop gnome-keyring-daemon
systemctl --user start gnome-keyring-daemon
gnome-keyring-daemon --unlock
```

## 🔧 System-CLI Emergency Commands

### Status Check
```bash
# Check overall status
system-cli keyring emergency-recovery --action status

# Test all fallback methods
system-cli keyring emergency-recovery --action test
```

### Recovery Actions
```bash
# Graceful TPM disable
system-cli keyring emergency-recovery --action disable

# Emergency recovery (nuclear option)
system-cli keyring emergency-recovery --action emergency --force

# Manual unlock with timeout
system-cli keyring unlock-manual --timeout 30
```

### Enhanced TPM Setup
```bash
# Setup with robust fallbacks
system-cli keyring tpm-setup-enhanced

# Force setup (skip confirmations)
system-cli keyring tpm-setup-enhanced --force
```

## 🧪 Testing Your Fallback System

### Comprehensive Test
```bash
# Run full test suite
./scripts/test-keyring-fallbacks.sh

# Quick essential tests
./scripts/test-keyring-fallbacks.sh quick

# TPM-specific tests
./scripts/test-keyring-fallbacks.sh tpm
```

### Manual Testing
```bash
# Test 1: Password unlock
gnome-keyring-daemon --unlock

# Test 2: Service restart
systemctl --user restart gnome-keyring-daemon

# Test 3: Emergency script
~/.config/tpm-keyring/emergency-recovery.sh test

# Test 4: System-CLI integration
system-cli keyring emergency-recovery --action test
```

## 📁 Important File Locations

### Configuration Files
```
~/.config/tpm-keyring/                    # TPM configuration directory
├── keyring-password.enc                  # TPM-encrypted password
├── fallback-config.json                  # Fallback configuration
├── emergency-recovery.sh                 # Emergency recovery script
└── tpm-unlock-keyring-enhanced.sh        # Enhanced unlock script
```

### Log Files
```
~/.cache/tpm-keyring-enhanced.log         # Enhanced TPM integration logs
~/.cache/keyring-manager/                 # Keyring manager logs
```

### Service Files
```
~/.config/systemd/user/tpm-keyring-unlock-enhanced.service
```

## ⚙️ Configuration Options

### Fallback Configuration (`~/.config/tpm-keyring/fallback-config.json`)
```json
{
    "version": "1.0",
    "unlock_methods": ["tpm", "fingerprint", "password"],
    "fallback_enabled": true,
    "emergency_password_prompt": true,
    "tpm_timeout": 10,
    "max_retry_attempts": 3,
    "pcr_values": "0,1,7"
}
```

### Customization Options
- **Timeout values:** Adjust `tpm_timeout` for slower systems
- **Retry attempts:** Modify `max_retry_attempts` for reliability
- **PCR values:** Change `pcr_values` for different security levels
- **Methods:** Reorder `unlock_methods` for preference

## 🚨 Emergency Contacts & Resources

### If All Else Fails
1. **Boot from live USB** to access files
2. **Restore from backup** (you did create backups, right?)
3. **Reset keyring completely** (lose stored passwords)

### Reset Keyring (Last Resort)
```bash
# WARNING: This deletes all stored passwords
rm -rf ~/.local/share/keyrings/*
systemctl --user restart gnome-keyring-daemon
# You'll need to re-enter all passwords
```

## 🎯 Best Practices

### Prevention
- ✅ **Always test** after TPM setup
- ✅ **Keep emergency script** accessible
- ✅ **Know your password** (never rely only on TPM)
- ✅ **Regular backups** of keyring data
- ✅ **Document your setup** for future reference

### Monitoring
- 📊 **Check logs** regularly: `tail -f ~/.cache/tpm-keyring-enhanced.log`
- 🔍 **Test fallbacks** monthly: `./scripts/test-keyring-fallbacks.sh`
- 📈 **Monitor TPM health**: `sudo tpm2_getcap properties-fixed`

### Maintenance
- 🔄 **Update scripts** when system changes
- 🧹 **Clean old logs** periodically
- 🔧 **Review configuration** after major updates

## 📞 Troubleshooting Quick Reference

| Problem | Quick Fix | Command |
|---------|-----------|---------|
| Keyring locked | Manual unlock | `gnome-keyring-daemon --unlock` |
| TPM hanging | Emergency recovery | `system-cli keyring emergency-recovery --action emergency` |
| Service not running | Restart service | `systemctl --user restart gnome-keyring-daemon` |
| TPM not working | Disable TPM | `system-cli keyring emergency-recovery --action disable` |
| Complete failure | Nuclear reset | `pkill -f keyring && systemctl --user restart gnome-keyring-daemon` |

## 🔗 Related Documentation

- [TPM Enhanced Setup Guide](./tpm-enhanced-setup.md)
- [Keyring Management Guide](./keyring-management.md)
- [System-CLI Reference](./system-cli-reference.md)
- [Security Best Practices](./security-best-practices.md)

---

**Remember:** The goal of TPM integration is to **enhance** security while maintaining **reliable access**. You should never be locked out when following these procedures.
