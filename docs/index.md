# System CLI Documentation

Welcome to the System CLI documentation! This comprehensive Linux system administration tool provides a modern Python CLI interface for managing all aspects of your Linux system.

## 🎯 Overview

System CLI is designed for the Dell Precision 5560 and provides unified management for:

- **🔐 Keyring Management**: Secure credential storage with dual keyring architecture
- **🛡️ Security Auditing**: Comprehensive security assessments and hardening
- **📊 System Monitoring**: Real-time system health and performance monitoring
- **⚙️ Configuration Management**: Centralized system configuration
- **🔧 Setup & Maintenance**: Automated system setup and maintenance tasks

## ✨ Key Features

- **Modern Python CLI** built with Typer and Rich for excellent user experience
- **Comprehensive Security** with automated audits and hardening recommendations
- **Dual Keyring Architecture** for secure credential management
- **Real-time Monitoring** with customizable alerts and dashboards
- **Extensible Design** with plugin architecture for custom functionality
- **Professional Documentation** with detailed guides and API reference

## 🚀 Quick Start

### Installation

```bash
# Install from source
git clone https://github.com/bcherrington/cursor-system.git
cd cursor-system
pip install -e .

# Or install development version
pip install -e ".[dev]"
```

### Basic Usage

```bash
# Check system status
system-cli status

# Manage keyrings
system-cli keyring status
system-cli keyring unlock convenience

# Run security audit
system-cli security audit
system-cli security fix --auto

# Setup dual keyring
system-cli setup dual-keyring

# Monitor system health
system-cli monitor dashboard
```

## 📚 Documentation Structure

- **[Installation Guide](getting-started/installation.md)**: Detailed installation instructions
- **[Quick Start Guide](getting-started/quick-start.md)**: Get started in 5 minutes
- **[User Guides](user-guides/system-cli.md)**: Complete user documentation
- **[Developer Guide](development/contributing.md)**: Development and contribution guidelines
- **[API Reference](development/api-reference.md)**: Complete API documentation

## 🛡️ Security Features

System CLI provides enterprise-grade security features:

- **Automated Security Audits** with 50+ security checks
- **SSH Hardening** with best practice configurations
- **Firewall Management** with UFW integration
- **Keyring Security** with encrypted credential storage
- **System Monitoring** with security event detection
- **Compliance Reporting** with detailed audit trails

## 🔧 System Requirements

- **Operating System**: Ubuntu 20.04+ (optimized for Dell Precision 5560)
- **Python**: 3.8 or higher
- **Dependencies**: Automatically managed via pip
- **Privileges**: Some features require sudo access

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](development/contributing.md) for details on:

- Setting up the development environment
- Running tests and quality checks
- Submitting pull requests
- Code style and conventions

## 📄 License

This project is licensed under the GNU General Public License v3.0. See the LICENSE file for details.

This ensures that System CLI remains free and open source, and that any modifications or distributions must also be made available under the same terms, protecting the freedom of all users.

## 🆘 Support

- **Documentation**: Browse the complete documentation in this site
- **Issues**: Report bugs and request features on [GitHub Issues](https://github.com/bcherrington/cursor-system/issues)
- **Discussions**: Join the conversation on [GitHub Discussions](https://github.com/bcherrington/cursor-system/discussions)

---

*System CLI - Professional Linux system administration made simple.*
