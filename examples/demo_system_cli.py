#!/usr/bin/env python3
"""
Demo script for System CLI functionality.
Shows off the key features and capabilities.
"""

import subprocess
import time
import sys
from pathlib import Path


def run_cli_command(cmd: str, description: str = None):
    """Run a CLI command and display the output."""
    if description:
        print(f"\n🔹 {description}")
        print("=" * (len(description) + 3))
    
    print(f"$ {cmd}")
    print()
    
    try:
        # Activate venv and run command using bash explicitly
        full_cmd = f"bash -c 'source .venv/bin/activate && {cmd}'"
        result = subprocess.run(
            full_cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.stdout:
            print(result.stdout)
        
        if result.stderr and result.returncode != 0:
            print(f"Error: {result.stderr}")
        
        return result.returncode == 0
    
    except subprocess.TimeoutExpired:
        print("⏰ Command timed out")
        return False
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False


def main():
    """Run the demo."""
    print("🖥️ System CLI Demonstration")
    print("=" * 30)
    print()
    print("This demo showcases the key features of the new Python CLI")
    print("that replaces the original bash scripts.")
    print()
    
    # Check if virtual environment exists
    if not Path(".venv").exists():
        print("❌ Virtual environment not found. Please run:")
        print("   python3 -m venv .venv")
        print("   source .venv/bin/activate")
        print("   pip install -e .")
        return 1
    
    demos = [
        # Basic CLI functionality
        ("system-cli --help", "Main CLI Help"),
        ("system-cli version", "Version Information"),

        # Keyring management
        ("system-cli keyring --help", "Keyring Management Commands"),

        # Security features
        ("system-cli security --help", "Security Audit Commands"),

        # Setup commands
        ("system-cli setup --help", "Setup and Configuration Commands"),

        # Testing functionality
        ("system-cli test --help", "Testing and Validation Commands"),
        ("system-cli test dependencies", "System Dependencies Check"),

        # System health
        ("system-cli system --help", "System Health Commands"),

        # Monitoring
        ("system-cli monitor --help", "Monitoring and Automation Commands"),
    ]
    
    success_count = 0
    total_count = len(demos)
    
    for cmd, description in demos:
        success = run_cli_command(cmd, description)
        if success:
            success_count += 1
        
        # Small delay between commands
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Demo Summary")
    print("=" * 50)
    print(f"Commands executed: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 All demo commands executed successfully!")
        print()
        print("🚀 Next Steps:")
        print("1. Run: system-cli setup dual-keyring")
        print("2. Run: system-cli test dual-keyring")
        print("3. Run: system-cli status")
        print("4. Explore other commands with --help")
    else:
        print("⚠️ Some commands failed. Check the output above.")
    
    print()
    print("📚 Key Features Demonstrated:")
    print("• Rich terminal interface with colors and formatting")
    print("• Comprehensive help system")
    print("• Modular command structure")
    print("• System dependency checking")
    print("• Error handling and validation")
    print("• Modern Python CLI with Typer framework")
    
    return 0 if success_count == total_count else 1


if __name__ == "__main__":
    sys.exit(main())
