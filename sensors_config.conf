# Custom sensors configuration for Dell Precision 5560
# Place this in /etc/sensors3.conf or ~/.sensors3.conf

# Intel Core Temperature Sensor
chip "coretemp-isa-*"
    label temp1 "Package"
    label temp2 "Core 0"
    label temp3 "Core 1" 
    label temp4 "Core 2"
    label temp5 "Core 3"
    label temp6 "Core 4"
    label temp7 "Core 5"
    label temp8 "Core 6"
    label temp9 "Core 7"
    
    # Set warning and critical temperatures
    set temp1_max 85
    set temp1_crit 95
    set temp2_max 85
    set temp2_crit 95
    set temp3_max 85
    set temp3_crit 95
    set temp4_max 85
    set temp4_crit 95
    set temp5_max 85
    set temp5_crit 95
    set temp6_max 85
    set temp6_crit 95
    set temp7_max 85
    set temp7_crit 95
    set temp8_max 85
    set temp8_crit 95
    set temp9_max 85
    set temp9_crit 95

# Dell SMM (System Management Mode) sensors
chip "dell_smm-virtual-*"
    label fan1 "CPU Fan"
    label fan2 "GPU Fan"
    label temp1 "CPU"
    label temp2 "Memory"
    label temp3 "Ambient 1"
    label temp4 "Ambient 2"
    label temp5 "Storage"
    label temp6 "Ambient 3"
    label temp7 "Ambient 4"
    label temp8 "Ambient 5"
    label temp9 "Ambient 6"
    label temp10 "Other"
    
    # Fan speed limits (adjust based on your preferences)
    set fan1_min 0
    set fan1_max 5400
    set fan2_min 0
    set fan2_max 5700

# NVMe Drive 1 (1TB Crucial)
chip "nvme-pci-0200"
    label temp1 "NVMe 1TB"
    set temp1_max 70
    set temp1_crit 80

# NVMe Drive 2 (512GB SK Hynix)  
chip "nvme-pci-0300"
    label temp1 "NVMe 512GB"
    label temp2 "NVMe 512GB Sensor 2"
    set temp1_max 70
    set temp1_crit 80
    set temp2_max 70
    set temp2_crit 80

# WiFi Module
chip "iwlwifi_1-virtual-*"
    label temp1 "WiFi Module"
    set temp1_max 70
    set temp1_crit 85

# Battery monitoring
chip "BAT0-acpi-*"
    label in0 "Battery Voltage"
    label curr1 "Battery Current"
