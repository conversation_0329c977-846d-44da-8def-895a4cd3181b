# Configuration for tools that don't yet support pyproject.toml

[flake8]
max-line-length = 100
extend-ignore = E203, W503, E501
exclude = 
    .git,
    __pycache__,
    build,
    dist,
    .venv,
    venv,
    .eggs,
    *.egg-info,
    .mypy_cache,
    .pytest_cache
per-file-ignores =
    __init__.py:F401
    tests/*:B011

[mypy-questionary.*]
ignore_missing_imports = True

[mypy-psutil.*]
ignore_missing_imports = True

[mypy-yaml.*]
ignore_missing_imports = True

[mypy-toml.*]
ignore_missing_imports = True
