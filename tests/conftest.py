"""Pytest configuration and fixtures."""

import pytest
from pathlib import Path
from unittest.mock import Mock, MagicMock
from typing import Dict, Any
import tempfile
import shutil


@pytest.fixture
def temp_config_dir(tmp_path):
    """Create a temporary configuration directory."""
    config_dir = tmp_path / "config"
    config_dir.mkdir()
    return config_dir


@pytest.fixture
def temp_keyring_dir(tmp_path):
    """Create a temporary keyring directory."""
    keyring_dir = tmp_path / ".local" / "share" / "keyrings"
    keyring_dir.mkdir(parents=True)
    return keyring_dir


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    from system_cli.core.config import Config
    return Mock(spec=Config)


@pytest.fixture
def sample_keyring_data():
    """Sample keyring data for testing."""
    return {
        "login": {"status": "unlocked", "items": 5},
        "convenience": {"status": "unlocked", "items": 3}
    }


@pytest.fixture
def sample_security_audit_data():
    """Sample security audit data for testing."""
    return {
        "score": 8.5,
        "checks": {
            "firewall": {"status": "enabled", "score": 10},
            "ssh": {"status": "hardened", "score": 9},
            "updates": {"status": "current", "score": 8}
        },
        "recommendations": [
            "Enable automatic security updates",
            "Configure fail2ban"
        ]
    }


@pytest.fixture
def mock_subprocess():
    """Mock subprocess for testing system commands."""
    with pytest.mock.patch('subprocess.run') as mock_run:
        mock_run.return_value.returncode = 0
        mock_run.return_value.stdout = "mock output"
        mock_run.return_value.stderr = ""
        yield mock_run


@pytest.fixture
def mock_keyring_manager():
    """Mock keyring manager for testing."""
    from system_cli.core.keyring_manager import KeyringManager
    mock_manager = Mock(spec=KeyringManager)
    mock_manager.get_status.return_value = {
        "login_keyring": {"unlocked": True, "items": 5},
        "convenience_keyring": {"unlocked": True, "items": 3}
    }
    return mock_manager


@pytest.fixture
def mock_security_auditor():
    """Mock security auditor for testing."""
    from system_cli.core.security_auditor import SecurityAuditor
    mock_auditor = Mock(spec=SecurityAuditor)
    mock_auditor.run_audit.return_value = {
        "overall_score": 8.5,
        "checks": {},
        "recommendations": []
    }
    return mock_auditor


@pytest.fixture
def cli_runner():
    """Typer CLI test runner."""
    from typer.testing import CliRunner
    return CliRunner()


@pytest.fixture(scope="session")
def test_data_dir():
    """Directory containing test data files."""
    return Path(__file__).parent / "fixtures" / "sample_data"


@pytest.fixture
def isolated_filesystem():
    """Create an isolated filesystem for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        original_cwd = Path.cwd()
        try:
            import os
            os.chdir(temp_dir)
            yield Path(temp_dir)
        finally:
            os.chdir(original_cwd)


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "e2e: marks tests as end-to-end tests"
    )


def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their location."""
    for item in items:
        # Mark tests in unit/ directory as unit tests
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        # Mark tests in integration/ directory as integration tests
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        # Mark tests in e2e/ directory as e2e tests
        elif "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
