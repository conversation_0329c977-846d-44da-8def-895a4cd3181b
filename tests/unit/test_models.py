"""Test data models."""

import pytest
from datetime import datetime
from pathlib import Path

from system_cli.models.config import <PERSON>fig<PERSON>ode<PERSON>, KeyringConfig, SecurityConfig
from system_cli.models.keyring import KeyringInfo, KeyringStatus, KeyringType, KeyringState
from system_cli.models.security import SecurityCheck, SecurityAuditResult, CheckStatus, SecurityLevel
from system_cli.models.system import SystemInfo, SystemHealth, ResourceUsage, SystemStatus


class TestConfigModel:
    """Test configuration models."""
    
    def test_config_model_defaults(self):
        """Test default configuration values."""
        config = ConfigModel()
        
        assert config.version == "1.0.0"
        assert config.debug_mode is False
        assert config.log_level == "INFO"
        assert isinstance(config.keyring, KeyringConfig)
        assert isinstance(config.security, SecurityConfig)
    
    def test_keyring_config_defaults(self):
        """Test keyring configuration defaults."""
        config = KeyringConfig()
        
        assert config.auto_unlock_convenience is True
        assert config.backup_enabled is True
        assert config.login_keyring_path.name == "login.keyring"
        assert config.convenience_keyring_path.name == "convenience.keyring"
    
    def test_security_config_defaults(self):
        """Test security configuration defaults."""
        config = SecurityConfig()
        
        assert config.audit_enabled is True
        assert config.auto_fix_enabled is False
        assert config.security_level == "balanced"
        assert config.firewall_required is True
        assert config.log_retention_days == 30


class TestKeyringModels:
    """Test keyring data models."""
    
    def test_keyring_info_creation(self):
        """Test keyring info model creation."""
        keyring = KeyringInfo(
            name="test-keyring",
            type=KeyringType.LOGIN,
            state=KeyringState.UNLOCKED,
            item_count=5
        )
        
        assert keyring.name == "test-keyring"
        assert keyring.type == KeyringType.LOGIN
        assert keyring.is_unlocked is True
        assert keyring.is_locked is False
        assert keyring.item_count == 5
    
    def test_keyring_status_calculation(self):
        """Test keyring status calculations."""
        keyrings = [
            KeyringInfo(
                name="login",
                type=KeyringType.LOGIN,
                state=KeyringState.UNLOCKED,
                item_count=3
            ),
            KeyringInfo(
                name="convenience",
                type=KeyringType.CONVENIENCE,
                state=KeyringState.LOCKED,
                item_count=2
            )
        ]
        
        status = KeyringStatus(
            keyrings=keyrings,
            keyring_daemon_running=True,
            secret_service_available=True
        )
        
        assert status.total_items == 5
        assert status.unlocked_keyrings == 1
        assert status.locked_keyrings == 1
        assert status.health_score > 0


class TestSecurityModels:
    """Test security audit models."""
    
    def test_security_check_creation(self):
        """Test security check model creation."""
        check = SecurityCheck(
            name="firewall_status",
            description="Check if firewall is enabled",
            status=CheckStatus.PASSED,
            score=10.0,
            level=SecurityLevel.HIGH,
            message="Firewall is active and enabled"
        )
        
        assert check.name == "firewall_status"
        assert check.status == CheckStatus.PASSED
        assert check.score == 10.0
        assert check.level == SecurityLevel.HIGH
    
    def test_security_audit_result(self):
        """Test security audit result model."""
        checks = [
            SecurityCheck(
                name="test1",
                description="Test check 1",
                status=CheckStatus.PASSED,
                score=10.0,
                level=SecurityLevel.HIGH,
                message="Passed"
            ),
            SecurityCheck(
                name="test2",
                description="Test check 2",
                status=CheckStatus.FAILED,
                score=0.0,
                level=SecurityLevel.CRITICAL,
                message="Failed"
            )
        ]
        
        result = SecurityAuditResult(
            overall_score=5.0,
            security_level="medium",
            categories=[],
            recommendations=[],
            total_checks=2,
            passed_checks=1,
            failed_checks=1,
            warning_checks=0
        )
        
        assert result.overall_score == 5.0
        assert result.pass_rate == 50.0
        assert result.total_checks == 2


class TestSystemModels:
    """Test system information models."""
    
    def test_system_info_creation(self):
        """Test system info model creation."""
        boot_time = datetime.now()
        
        info = SystemInfo(
            hostname="test-host",
            kernel="5.15.0",
            distribution="Ubuntu 22.04",
            architecture="x86_64",
            uptime_seconds=86400,  # 1 day
            boot_time=boot_time,
            cpu_cores=8,
            total_memory=16 * 1024**3,  # 16 GB
            python_version="3.10.0"
        )
        
        assert info.hostname == "test-host"
        assert info.uptime_days == 1.0
        assert info.total_memory_gb == 16.0
        assert info.cpu_cores == 8
    
    def test_resource_usage_properties(self):
        """Test resource usage calculated properties."""
        usage = ResourceUsage(
            cpu_percent=50.0,
            memory_percent=75.0,
            disk_percent=80.0,
            memory_total=16 * 1024**3,  # 16 GB
            memory_used=12 * 1024**3,   # 12 GB
            disk_total=1000 * 1024**3,  # 1 TB
            disk_used=800 * 1024**3     # 800 GB
        )
        
        assert usage.memory_total_gb == 16.0
        assert usage.memory_used_gb == 12.0
        assert usage.disk_total_gb == 1000.0
        assert usage.disk_used_gb == 800.0
    
    def test_system_health_assessment(self):
        """Test system health assessment."""
        system_info = SystemInfo(
            hostname="test",
            kernel="5.15.0",
            distribution="Ubuntu",
            architecture="x86_64",
            uptime_seconds=3600,
            boot_time=datetime.now(),
            python_version="3.10.0"
        )
        
        resource_usage = ResourceUsage(
            cpu_percent=20.0,
            memory_percent=60.0,
            disk_percent=70.0
        )
        
        health = SystemHealth(
            status=SystemStatus.GOOD,
            score=8.0,
            system_info=system_info,
            resource_usage=resource_usage
        )
        
        # Health should be recalculated based on resource usage
        assert health.status in [SystemStatus.EXCELLENT, SystemStatus.GOOD]
        assert health.score >= 7.0  # Should be good with these resource levels
