"""Integration tests for CLI commands."""

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import patch, Mock

from system_cli.main import app


class TestCLIIntegration:
    """Test CLI command integration."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
    
    def test_main_help(self):
        """Test main help command."""
        result = self.runner.invoke(app, ["--help"])
        assert result.exit_code == 0
        assert "System CLI" in result.stdout
    
    def test_version_command(self):
        """Test version command."""
        result = self.runner.invoke(app, ["version"])
        assert result.exit_code == 0
        assert "1.0.0" in result.stdout
    
    @patch('system_cli.core.keyring_manager.KeyringManager')
    def test_keyring_status_command(self, mock_keyring_manager):
        """Test keyring status command."""
        # Mock keyring manager
        mock_manager = Mock()
        mock_manager.get_status.return_value = {
            "login_keyring": {"unlocked": True, "items": 5},
            "convenience_keyring": {"unlocked": False, "items": 3}
        }
        mock_keyring_manager.return_value = mock_manager
        
        result = self.runner.invoke(app, ["keyring", "status"])
        assert result.exit_code == 0
    
    @patch('system_cli.core.security_auditor.SecurityAuditor')
    def test_security_audit_command(self, mock_security_auditor):
        """Test security audit command."""
        # Mock security auditor
        mock_auditor = Mock()
        mock_auditor.run_audit.return_value = {
            "overall_score": 8.5,
            "checks": {},
            "recommendations": []
        }
        mock_security_auditor.return_value = mock_auditor
        
        result = self.runner.invoke(app, ["security", "audit"])
        assert result.exit_code == 0
    
    def test_invalid_command(self):
        """Test invalid command handling."""
        result = self.runner.invoke(app, ["invalid-command"])
        assert result.exit_code != 0
    
    def test_keyring_help(self):
        """Test keyring command help."""
        result = self.runner.invoke(app, ["keyring", "--help"])
        assert result.exit_code == 0
        assert "keyring" in result.stdout.lower()
    
    def test_security_help(self):
        """Test security command help."""
        result = self.runner.invoke(app, ["security", "--help"])
        assert result.exit_code == 0
        assert "security" in result.stdout.lower()
    
    def test_setup_help(self):
        """Test setup command help."""
        result = self.runner.invoke(app, ["setup", "--help"])
        assert result.exit_code == 0
        assert "setup" in result.stdout.lower()
    
    def test_test_help(self):
        """Test test command help."""
        result = self.runner.invoke(app, ["test", "--help"])
        assert result.exit_code == 0
        assert "test" in result.stdout.lower()


class TestCLIErrorHandling:
    """Test CLI error handling."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
    
    @patch('system_cli.core.keyring_manager.KeyringManager')
    def test_keyring_command_error_handling(self, mock_keyring_manager):
        """Test keyring command error handling."""
        # Mock keyring manager to raise exception
        mock_keyring_manager.side_effect = Exception("Test error")
        
        result = self.runner.invoke(app, ["keyring", "status"])
        # Should handle error gracefully
        assert result.exit_code != 0 or "error" in result.stdout.lower()
    
    @patch('system_cli.core.security_auditor.SecurityAuditor')
    def test_security_command_error_handling(self, mock_security_auditor):
        """Test security command error handling."""
        # Mock security auditor to raise exception
        mock_security_auditor.side_effect = Exception("Test error")
        
        result = self.runner.invoke(app, ["security", "audit"])
        # Should handle error gracefully
        assert result.exit_code != 0 or "error" in result.stdout.lower()


class TestCLIOutput:
    """Test CLI output formatting."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
    
    @patch('system_cli.core.keyring_manager.KeyringManager')
    def test_keyring_status_output_format(self, mock_keyring_manager):
        """Test keyring status output formatting."""
        # Mock keyring manager with specific data
        mock_manager = Mock()
        mock_manager.get_status.return_value = {
            "login_keyring": {"unlocked": True, "items": 5},
            "convenience_keyring": {"unlocked": True, "items": 3}
        }
        mock_keyring_manager.return_value = mock_manager
        
        result = self.runner.invoke(app, ["keyring", "status"])
        assert result.exit_code == 0
        
        # Check for expected output elements
        output = result.stdout.lower()
        assert "keyring" in output
        assert "status" in output
    
    def test_help_output_formatting(self):
        """Test help output formatting."""
        result = self.runner.invoke(app, ["--help"])
        assert result.exit_code == 0
        
        output = result.stdout
        # Check for proper help formatting
        assert "Usage:" in output
        assert "Commands:" in output
        assert "Options:" in output
