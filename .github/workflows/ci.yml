name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[test,lint]"

    - name: Lint with flake8
      run: |
        flake8 src/system_cli tests --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 src/system_cli tests --count --exit-zero --max-complexity=10 --max-line-length=100 --statistics

    - name: Type check with mypy
      run: |
        mypy src/system_cli

    - name: Security check with bandit
      run: |
        bandit -r src/system_cli

    - name: Test with pytest
      run: |
        pytest tests/ --cov=system_cli --cov-report=xml --cov-report=term-missing

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[lint]"

    - name: Check code formatting with black
      run: |
        black --check src/system_cli tests

    - name: Check import sorting with isort
      run: |
        isort --check-only src/system_cli tests

    - name: Run pre-commit hooks
      uses: pre-commit/action@v3.0.0

  build:
    runs-on: ubuntu-latest
    needs: [test, lint]
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine

    - name: Build package
      run: |
        python -m build

    - name: Check package
      run: |
        twine check dist/*

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/
