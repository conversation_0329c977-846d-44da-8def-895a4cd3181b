"""
Security auditor for keyring content analysis.
Converted from keyring-content-audit.sh
"""

import os
import subprocess
import time
import json
from pathlib import Path
from typing import Dict, List, Any, Optional

from system_cli.utils.helpers import run_command, check_command_exists


class SecurityAuditor:
    """Handles keyring content auditing and analysis."""
    
    def __init__(self, output_dir: Optional[Path] = None):
        self.output_dir = output_dir or Path.home() / "security-audits" / "content"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.hostname = os.uname().nodename
    
    def keyring_content_audit(self) -> Dict[str, Any]:
        """Perform comprehensive keyring content audit."""
        audit_results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "hostname": self.hostname,
            "keyring_structure": {},
            "application_usage": {},
            "security_analysis": {},
            "recommendations": []
        }
        
        # Analyze keyring structure
        audit_results["keyring_structure"] = self._analyze_keyring_structure()
        
        # Analyze application usage patterns
        audit_results["application_usage"] = self._analyze_application_usage()
        
        # Perform security analysis
        audit_results["security_analysis"] = self._perform_security_analysis()
        
        # Generate recommendations
        audit_results["recommendations"] = self._generate_recommendations(audit_results)
        
        # Save detailed audit report
        self._save_audit_report(audit_results)
        
        return audit_results
    
    def save_report(self, results: Dict[str, Any], format: str = "markdown") -> Path:
        """Save audit report in specified format."""
        timestamp = int(time.time())
        
        if format == "markdown":
            report_file = self.output_dir / f"content_audit_{timestamp}.md"
            self._save_markdown_report(results, report_file)
        elif format == "json":
            report_file = self.output_dir / f"content_audit_{timestamp}.json"
            with open(report_file, 'w') as f:
                json.dump(results, f, indent=2)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        return report_file
    
    def _analyze_keyring_structure(self) -> Dict[str, Any]:
        """Analyze keyring file structure and status."""
        keyring_dir = Path.home() / ".local" / "share" / "keyrings"
        structure = {}
        
        if not keyring_dir.exists():
            return {
                "error": "Keyring directory not found",
                "path": str(keyring_dir)
            }
        
        # Analyze each keyring file
        for keyring_file in keyring_dir.glob("*.keyring"):
            keyring_name = keyring_file.stem
            
            try:
                stat_info = keyring_file.stat()
                
                # Try to get item count using secret-tool
                item_count = self._get_keyring_item_count(keyring_name)
                
                structure[keyring_name] = {
                    "status": "accessible" if keyring_file.exists() else "missing",
                    "size": f"{stat_info.st_size} bytes",
                    "modified": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(stat_info.st_mtime)),
                    "item_count": item_count,
                    "permissions": oct(stat_info.st_mode)[-3:]
                }
                
            except Exception as e:
                structure[keyring_name] = {
                    "status": "error",
                    "error": str(e),
                    "item_count": 0
                }
        
        # Check for legacy files
        legacy_files = []
        for pattern in ["*.keystore", "*.keystore.*", "user.keystore"]:
            legacy_files.extend(keyring_dir.glob(pattern))
        
        if legacy_files:
            structure["legacy_files"] = {
                "count": len(legacy_files),
                "files": [str(f.name) for f in legacy_files],
                "recommendation": "Consider removing legacy keystore files"
            }
        
        return structure
    
    def _analyze_application_usage(self) -> Dict[str, Any]:
        """Analyze which applications use which keyrings."""
        usage = {
            "secure_apps": [],
            "convenience_apps": [],
            "active_apps": [],
            "mapping_config": {}
        }
        
        # Check application mapping configuration
        config_file = Path.home() / ".config" / "keyring-manager" / "app-keyring-map.conf"
        if config_file.exists():
            try:
                with open(config_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            if '=' in line:
                                app, keyring = line.split('=', 1)
                                usage["mapping_config"][app.strip()] = keyring.strip()
                                
                                if keyring.strip() == "secure":
                                    usage["secure_apps"].append(app.strip())
                                elif keyring.strip() == "convenience":
                                    usage["convenience_apps"].append(app.strip())
            except Exception:
                pass
        
        # Check for currently running applications that might use keyrings
        try:
            result = run_command(["ps", "aux"])
            if result.returncode == 0:
                keyring_related_processes = []
                for line in result.stdout.split('\n'):
                    if any(keyword in line.lower() for keyword in ["chrome", "firefox", "thunderbird", "evolution", "networkmanager"]):
                        # Extract process name
                        parts = line.split()
                        if len(parts) > 10:
                            process_name = parts[10].split('/')[-1]
                            if process_name not in keyring_related_processes:
                                keyring_related_processes.append(process_name)
                                usage["active_apps"].append(process_name)
        except Exception:
            pass
        
        return usage
    
    def _perform_security_analysis(self) -> Dict[str, Any]:
        """Perform security analysis of keyring configuration."""
        analysis = {
            "security_score": 0,
            "vulnerabilities": [],
            "strengths": [],
            "compliance": {}
        }
        
        keyring_dir = Path.home() / ".local" / "share" / "keyrings"
        
        # Check keyring file permissions
        if keyring_dir.exists():
            for keyring_file in keyring_dir.glob("*.keyring"):
                stat_info = keyring_file.stat()
                permissions = oct(stat_info.st_mode)[-3:]
                
                if permissions == "600":
                    analysis["strengths"].append(f"{keyring_file.name}: Proper file permissions (600)")
                    analysis["security_score"] += 2
                else:
                    analysis["vulnerabilities"].append(f"{keyring_file.name}: Insecure permissions ({permissions})")
        
        # Check for dual keyring architecture
        login_keyring = keyring_dir / "login.keyring"
        convenience_keyring = keyring_dir / "convenience.keyring"
        
        if login_keyring.exists() and convenience_keyring.exists():
            analysis["strengths"].append("Dual keyring architecture implemented")
            analysis["security_score"] += 5
        elif login_keyring.exists():
            analysis["vulnerabilities"].append("Only secure keyring found - convenience keyring missing")
        else:
            analysis["vulnerabilities"].append("No secure keyring found")
        
        # Check for legacy keystore files
        legacy_files = list(keyring_dir.glob("*.keystore*")) if keyring_dir.exists() else []
        if legacy_files:
            analysis["vulnerabilities"].append(f"Legacy keystore files present: {len(legacy_files)} files")
        else:
            analysis["strengths"].append("No legacy keystore files found")
            analysis["security_score"] += 1
        
        # Check keyring daemon status
        result = run_command(["pgrep", "-f", "gnome-keyring-daemon"])
        if result.returncode == 0:
            analysis["strengths"].append("Keyring daemon is running")
            analysis["security_score"] += 1
        else:
            analysis["vulnerabilities"].append("Keyring daemon not running")
        
        # Compliance checks
        analysis["compliance"] = {
            "file_permissions": all(oct(f.stat().st_mode)[-3:] == "600" for f in keyring_dir.glob("*.keyring")) if keyring_dir.exists() else False,
            "dual_architecture": login_keyring.exists() and convenience_keyring.exists(),
            "no_legacy_files": len(legacy_files) == 0,
            "daemon_running": result.returncode == 0
        }
        
        # Calculate final security score (out of 10)
        max_score = 9  # Maximum possible score
        analysis["security_score"] = min(10, (analysis["security_score"] / max_score) * 10)
        
        return analysis
    
    def _generate_recommendations(self, audit_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate security recommendations based on audit results."""
        recommendations = []
        
        security_analysis = audit_results.get("security_analysis", {})
        vulnerabilities = security_analysis.get("vulnerabilities", [])
        
        # Recommendations based on vulnerabilities
        for vulnerability in vulnerabilities:
            if "permissions" in vulnerability.lower():
                recommendations.append({
                    "priority": "high",
                    "category": "file_permissions",
                    "issue": vulnerability,
                    "action": "Fix file permissions: chmod 600 ~/.local/share/keyrings/*.keyring",
                    "benefit": "Prevents unauthorized access to keyring files"
                })
            elif "legacy keystore" in vulnerability.lower():
                recommendations.append({
                    "priority": "medium",
                    "category": "legacy_cleanup",
                    "issue": vulnerability,
                    "action": "Remove legacy keystore files after backing up",
                    "benefit": "Reduces attack surface and confusion"
                })
            elif "daemon not running" in vulnerability.lower():
                recommendations.append({
                    "priority": "high",
                    "category": "service_status",
                    "issue": vulnerability,
                    "action": "Restart keyring daemon or reboot system",
                    "benefit": "Enables keyring functionality"
                })
            elif "convenience keyring missing" in vulnerability.lower():
                recommendations.append({
                    "priority": "medium",
                    "category": "architecture",
                    "issue": vulnerability,
                    "action": "Set up convenience keyring using system-cli setup convenience-keyring",
                    "benefit": "Completes dual keyring architecture"
                })
        
        # General recommendations
        security_score = security_analysis.get("security_score", 0)
        if security_score < 8:
            recommendations.append({
                "priority": "medium",
                "category": "overall_security",
                "issue": f"Security score below optimal: {security_score:.1f}/10",
                "action": "Address identified vulnerabilities to improve security posture",
                "benefit": "Enhanced overall security"
            })
        
        # Application usage recommendations
        app_usage = audit_results.get("application_usage", {})
        if len(app_usage.get("secure_apps", [])) == 0:
            recommendations.append({
                "priority": "low",
                "category": "application_mapping",
                "issue": "No applications mapped to secure keyring",
                "action": "Configure sensitive applications to use secure keyring",
                "benefit": "Better separation of sensitive credentials"
            })
        
        return recommendations
    
    def _get_keyring_item_count(self, keyring_name: str) -> int:
        """Get the number of items in a keyring."""
        try:
            # This is a simplified approach - actual implementation would need
            # to safely query the keyring without exposing secrets
            if check_command_exists("secret-tool"):
                # Note: This is a placeholder - actual implementation would need
                # proper keyring querying without exposing secrets
                return 0  # Placeholder
            else:
                return 0
        except Exception:
            return 0
    
    def _save_audit_report(self, results: Dict[str, Any]):
        """Save detailed audit report."""
        timestamp = int(time.time())
        report_file = self.output_dir / f"detailed_audit_{timestamp}.json"
        
        try:
            with open(report_file, 'w') as f:
                json.dump(results, f, indent=2)
        except Exception:
            pass  # Continue if save fails
    
    def _save_markdown_report(self, results: Dict[str, Any], report_file: Path):
        """Save audit report in markdown format."""
        content = f"""# Keyring Content Audit Report

**Generated:** {results['timestamp']}  
**Hostname:** {results['hostname']}

## Executive Summary

Security Score: **{results.get('security_analysis', {}).get('security_score', 0):.1f}/10**

## Keyring Structure Analysis

"""
        
        # Keyring structure
        structure = results.get("keyring_structure", {})
        for keyring, data in structure.items():
            if keyring != "legacy_files":
                content += f"### {keyring.title()} Keyring\n"
                content += f"- Status: {data.get('status', 'Unknown')}\n"
                content += f"- Size: {data.get('size', 'Unknown')}\n"
                content += f"- Items: {data.get('item_count', 0)}\n"
                content += f"- Last Modified: {data.get('modified', 'Unknown')}\n\n"
        
        # Legacy files
        if "legacy_files" in structure:
            content += "### Legacy Files\n"
            content += f"- Count: {structure['legacy_files'].get('count', 0)}\n"
            content += f"- Files: {', '.join(structure['legacy_files'].get('files', []))}\n\n"
        
        # Application usage
        content += "## Application Usage\n\n"
        app_usage = results.get("application_usage", {})
        content += f"- Secure Apps: {len(app_usage.get('secure_apps', []))}\n"
        content += f"- Convenience Apps: {len(app_usage.get('convenience_apps', []))}\n"
        content += f"- Active Apps: {len(app_usage.get('active_apps', []))}\n\n"
        
        # Security analysis
        content += "## Security Analysis\n\n"
        security = results.get("security_analysis", {})
        
        if security.get("strengths"):
            content += "### Strengths\n"
            for strength in security["strengths"]:
                content += f"- ✅ {strength}\n"
            content += "\n"
        
        if security.get("vulnerabilities"):
            content += "### Vulnerabilities\n"
            for vuln in security["vulnerabilities"]:
                content += f"- ❌ {vuln}\n"
            content += "\n"
        
        # Recommendations
        content += "## Recommendations\n\n"
        for rec in results.get("recommendations", []):
            priority_icon = "🔴" if rec.get("priority") == "high" else "🟡" if rec.get("priority") == "medium" else "🟢"
            content += f"### {priority_icon} {rec.get('priority', 'Unknown').title()} Priority\n"
            content += f"**Issue:** {rec.get('issue', 'Unknown')}\n\n"
            content += f"**Action:** {rec.get('action', 'Unknown')}\n\n"
            content += f"**Benefit:** {rec.get('benefit', 'Unknown')}\n\n"
        
        content += "---\n"
        content += f"*Report generated by system-cli security content-audit on {results['timestamp']}*\n"
        
        with open(report_file, 'w') as f:
            f.write(content)
