"""
Keyring management commands.
Converted from keyring-manager.sh
"""

import typer
from typing import Optional
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Confirm, Prompt
from rich.progress import Progress, SpinnerColumn, TextColumn

from system_cli.core.keyring_manager import Keyring<PERSON>anager
from system_cli.utils.display import create_status_table, show_error, show_success, show_warning
from system_cli.utils.helpers import run_command

app = typer.Typer(help="🔐 Keyring management commands")
console = Console()


@app.command()
def status():
    """Show detailed keyring status."""
    try:
        manager = KeyringManager()
        status_data = manager.get_status()
        
        # Create main status table
        table = create_status_table(status_data)
        
        # Show keyring files info
        files_panel = Panel(
            manager.get_keyring_files_info(),
            title="📁 Keyring Files",
            border_style="blue"
        )
        
        # Show running processes
        processes_panel = Panel(
            manager.get_keyring_processes(),
            title="🔄 Running Processes",
            border_style="green"
        )
        
        console.print(table)
        console.print()
        console.print(files_panel)
        console.print()
        console.print(processes_panel)
        
    except Exception as e:
        show_error(f"Failed to get keyring status: {e}")
        raise typer.Exit(1)


@app.command()
def unlock(
    secure: bool = typer.Option(False, "--secure", help="Unlock secure keyring"),
    convenience: bool = typer.Option(False, "--convenience", help="Unlock convenience keyring"),
    timeout: int = typer.Option(3600, "--timeout", help="Session timeout in seconds")
):
    """Unlock keyrings."""
    if not secure and not convenience:
        # Interactive selection
        choice = Prompt.ask(
            "Which keyring to unlock?",
            choices=["secure", "convenience", "both"],
            default="secure"
        )
        secure = choice in ["secure", "both"]
        convenience = choice in ["convenience", "both"]
    
    manager = KeyringManager()
    
    if secure:
        console.print("🔓 Unlocking secure keyring...")
        success = manager.unlock_secure_keyring(timeout)
        if success:
            show_success("Secure keyring unlocked successfully")
        else:
            show_error("Failed to unlock secure keyring")
            raise typer.Exit(1)
    
    if convenience:
        console.print("🔓 Unlocking convenience keyring...")
        success = manager.unlock_convenience_keyring()
        if success:
            show_success("Convenience keyring unlocked successfully")
        else:
            show_warning("Convenience keyring may already be unlocked")


@app.command()
def lock(
    secure: bool = typer.Option(False, "--secure", help="Lock secure keyring"),
    convenience: bool = typer.Option(False, "--convenience", help="Lock convenience keyring"),
    force: bool = typer.Option(False, "--force", help="Force lock without confirmation")
):
    """Lock keyrings."""
    if not secure and not convenience:
        choice = Prompt.ask(
            "Which keyring to lock?",
            choices=["secure", "convenience", "both"],
            default="secure"
        )
        secure = choice in ["secure", "both"]
        convenience = choice in ["convenience", "both"]
    
    if not force:
        if not Confirm.ask("Are you sure you want to lock the selected keyrings?"):
            console.print("Operation cancelled.")
            return
    
    manager = KeyringManager()
    
    if secure:
        console.print("🔒 Locking secure keyring...")
        success = manager.lock_secure_keyring()
        if success:
            show_success("Secure keyring locked")
        else:
            show_error("Failed to lock secure keyring")
    
    if convenience:
        console.print("🔒 Locking convenience keyring...")
        success = manager.lock_convenience_keyring()
        if success:
            show_success("Convenience keyring locked")
        else:
            show_error("Failed to lock convenience keyring")


@app.command()
def list_items(
    keyring: str = typer.Option("secure", "--keyring", help="Keyring to list (secure/convenience)"),
    show_passwords: bool = typer.Option(False, "--show-passwords", help="Show password values (dangerous!)"),
    filter_type: Optional[str] = typer.Option(None, "--filter", help="Filter by item type")
):
    """List items in keyrings."""
    if keyring not in ["secure", "convenience"]:
        show_error("Keyring must be 'secure' or 'convenience'")
        raise typer.Exit(1)
    
    if show_passwords and not Confirm.ask(
        "[red]⚠️ This will display passwords in plain text. Continue?[/red]"
    ):
        return
    
    manager = KeyringManager()
    items = manager.list_keyring_items(keyring, show_passwords, filter_type)
    
    if not items:
        console.print(f"No items found in {keyring} keyring")
        return
    
    # Create table for items
    table = Table(title=f"Items in {keyring.title()} Keyring")
    table.add_column("Label", style="cyan")
    table.add_column("Type", style="green")
    table.add_column("Application", style="blue")
    if show_passwords:
        table.add_column("Password", style="red")
    
    for item in items:
        row = [item.get("label", ""), item.get("type", ""), item.get("application", "")]
        if show_passwords:
            row.append(item.get("password", ""))
        table.add_row(*row)
    
    console.print(table)


@app.command()
def configure():
    """Configure application keyring preferences."""
    manager = KeyringManager()

    console.print("🔧 Configuring Application Keyring Preferences")
    console.print("=" * 50)

    success = manager.configure_applications()
    if success:
        show_success("Application keyring mapping configured successfully")
        console.print("\nConfiguration saved to: ~/.config/keyring-manager/app-keyring-map.conf")
        console.print("You can edit this file to customize which applications use which keyring.")
    else:
        show_error("Failed to configure application mapping")
        raise typer.Exit(1)


@app.command()
def tpm_setup():
    """Setup TPM-based keyring integration."""
    console.print("🔐 TPM-Keyring Integration Setup")
    console.print("=" * 40)

    # Check if TPM integration script exists
    tpm_script = Path.home().parent.parent / "home" / "bcherrington" / "cursor-system" / "scripts" / "tpm-keyring-integration.sh"

    if not tpm_script.exists():
        show_error("TPM integration script not found")
        console.print("Please ensure the script is available at: scripts/tpm-keyring-integration.sh")
        raise typer.Exit(1)

    # Run TPM setup
    result = run_command([str(tpm_script), "setup"], capture_output=False)

    if result.returncode == 0:
        show_success("TPM-keyring integration setup completed")
    else:
        show_error("TPM-keyring integration setup failed")
        raise typer.Exit(1)


@app.command()
def tpm_status():
    """Check TPM-keyring integration status."""
    console.print("📊 TPM-Keyring Integration Status")
    console.print("=" * 40)

    # Check if TPM integration script exists
    tpm_script = Path.home().parent.parent / "home" / "bcherrington" / "cursor-system" / "scripts" / "tpm-keyring-integration.sh"

    if not tpm_script.exists():
        show_warning("TPM integration script not found")
        return

    # Run TPM status
    result = run_command([str(tpm_script), "status"], capture_output=False)

    if result.returncode != 0:
        show_warning("TPM status check encountered issues")


@app.command()
def tpm_test():
    """Test TPM-keyring integration."""
    console.print("🧪 Testing TPM-Keyring Integration")
    console.print("=" * 40)

    # Check if TPM integration script exists
    tpm_script = Path.home().parent.parent / "home" / "bcherrington" / "cursor-system" / "scripts" / "tpm-keyring-integration.sh"

    if not tpm_script.exists():
        show_error("TPM integration script not found")
        raise typer.Exit(1)

    # Run TPM test
    result = run_command([str(tpm_script), "test"], capture_output=False)

    if result.returncode == 0:
        show_success("TPM-keyring integration test passed")
    else:
        show_error("TPM-keyring integration test failed")
        raise typer.Exit(1)


@app.command()
def tpm_setup_advanced(
    method: str = typer.Option("improved", "--method", help="TPM setup method: improved, simple, fixed, basic, no-pcr"),
    permissions: bool = typer.Option(False, "--permissions", help="Setup with permissions handling")
):
    """Advanced TPM setup with multiple methods (replaces multiple TPM scripts)."""
    console.print("🔐 Advanced TPM-Keyring Integration Setup")
    console.print("=" * 45)

    # Map method to script
    script_map = {
        "improved": "tpm-integration-improved.sh",
        "simple": "tpm-setup-simple.sh",
        "fixed": "tpm-setup-fixed.sh",
        "basic": "simple-tpm-setup.sh",
        "no-pcr": "tpm-simple-no-pcr.sh",
        "permissions": "tpm-setup-with-permissions.sh"
    }

    if permissions:
        script_name = script_map["permissions"]
    else:
        script_name = script_map.get(method, "tpm-integration-improved.sh")

    tpm_script = Path.home() / "cursor-system" / "scripts" / script_name

    if not tpm_script.exists():
        show_error(f"TPM integration script not found: {script_name}")
        console.print(f"Please ensure the script is available at: scripts/{script_name}")
        raise typer.Exit(1)

    console.print(f"Using method: [cyan]{method}[/cyan]")
    console.print(f"Script: [cyan]{script_name}[/cyan]")

    # Run TPM setup
    result = run_command([str(tpm_script), "setup"], capture_output=False)

    if result.returncode == 0:
        show_success(f"TPM-keyring integration setup completed using {method} method")
    else:
        show_error(f"TPM-keyring integration setup failed using {method} method")
        raise typer.Exit(1)


@app.command()
def audit_and_backup(
    output_dir: Optional[Path] = typer.Option(None, "--output-dir", help="Directory for audit results"),
    include_passwords: bool = typer.Option(False, "--include-passwords", help="Include password values in audit (DANGEROUS)"),
    format: str = typer.Option("json", "--format", help="Output format: json, yaml, table")
):
    """Comprehensive keyring audit and backup for 1Password consolidation."""
    from system_cli.core.keyring_auditor import KeyringAuditor

    console.print("🔍 [bold blue]Keyring Consolidation Audit & Backup[/bold blue]")
    console.print("=" * 50)
    console.print()

    if include_passwords:
        console.print("⚠️  [bold red]WARNING: Including passwords in audit output![/bold red]")
        if not Confirm.ask("Are you sure you want to include password values?"):
            include_passwords = False

    # Set default output directory
    if output_dir is None:
        output_dir = Path.home() / "security-audits" / "keyring-consolidation"

    output_dir.mkdir(parents=True, exist_ok=True)

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:

        # Initialize auditor
        task = progress.add_task("Initializing keyring auditor...", total=None)
        auditor = KeyringAuditor(output_dir, include_passwords)
        progress.update(task, completed=True)

        # Run comprehensive audit
        task = progress.add_task("Running comprehensive keyring audit...", total=None)
        audit_results = auditor.run_comprehensive_audit()
        progress.update(task, completed=True)

        # Create backup
        task = progress.add_task("Creating keyring backup...", total=None)
        backup_results = auditor.create_backup()
        progress.update(task, completed=True)

        # Generate migration plan
        task = progress.add_task("Generating 1Password migration plan...", total=None)
        migration_plan = auditor.generate_migration_plan()
        progress.update(task, completed=True)

        # Save results
        task = progress.add_task("Saving audit results...", total=None)
        report_file = auditor.save_results(audit_results, backup_results, migration_plan, format)
        progress.update(task, completed=True)

    console.print()
    show_success("Keyring audit and backup completed!")
    console.print(f"📁 Results saved to: [cyan]{report_file}[/cyan]")
    console.print(f"📁 Backup location: [cyan]{backup_results['backup_dir']}[/cyan]")
    console.print()

    # Display summary
    console.print("📊 [bold]Audit Summary[/bold]")
    console.print("-" * 20)
    console.print(f"• Keyrings found: {audit_results['summary']['total_keyrings']}")
    console.print(f"• Total items: {audit_results['summary']['total_items']}")
    console.print(f"• Migratable to 1Password: {migration_plan['summary']['migratable_items']}")
    console.print(f"• Must stay in GNOME: {migration_plan['summary']['gnome_only_items']}")
    console.print()

    if migration_plan['summary']['migratable_items'] > 0:
        console.print("🎯 [bold green]Ready for Phase 2: Migration to 1Password[/bold green]")
        console.print("Run: [cyan]system-cli keyring migrate-to-1password[/cyan]")
    else:
        console.print("ℹ️  No items found suitable for 1Password migration")

    return audit_results


@app.command()
def cleanup_for_1password(
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be done without making changes"),
    force: bool = typer.Option(False, "--force", help="Skip confirmation prompts"),
    backup_first: bool = typer.Option(True, "--backup/--no-backup", help="Create backup before cleanup")
):
    """Clean up keyring system for 1Password consolidation."""
    from system_cli.core.keyring_cleaner import KeyringCleaner

    console.print("🧹 [bold blue]Keyring Cleanup for 1Password Consolidation[/bold blue]")
    console.print("=" * 55)
    console.print()

    if not force and not dry_run:
        console.print("⚠️  [yellow]This will modify your keyring configuration![/yellow]")
        console.print("The following actions will be performed:")
        console.print("• Kill redundant keyring daemon processes")
        console.print("• Fix file permissions on keyring files")
        console.print("• Remove legacy keystore backup files")
        console.print("• Disable convenience keyring autostart")
        console.print("• Streamline keyring services")
        console.print()

        if not Confirm.ask("Do you want to proceed with the cleanup?"):
            console.print("Cleanup cancelled.")
            return

    # Set default backup location
    backup_dir = Path.home() / "security-audits" / "keyring-consolidation" / "pre-cleanup-backup"

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:

        # Initialize cleaner
        task = progress.add_task("Initializing keyring cleaner...", total=None)
        cleaner = KeyringCleaner(backup_dir, dry_run)
        progress.update(task, completed=True)

        # Create backup if requested
        if backup_first and not dry_run:
            task = progress.add_task("Creating pre-cleanup backup...", total=None)
            backup_results = cleaner.create_backup()
            progress.update(task, completed=True)

        # Run cleanup actions
        task = progress.add_task("Analyzing current state...", total=None)
        cleanup_plan = cleaner.analyze_cleanup_needs()
        progress.update(task, completed=True)

        task = progress.add_task("Executing cleanup actions...", total=None)
        cleanup_results = cleaner.execute_cleanup(cleanup_plan)
        progress.update(task, completed=True)

        # Verify results
        task = progress.add_task("Verifying cleanup results...", total=None)
        verification_results = cleaner.verify_cleanup()
        progress.update(task, completed=True)

    console.print()

    if dry_run:
        show_success("Dry run completed - no changes made!")
        console.print("📋 [bold]Cleanup Plan Summary[/bold]")
        console.print("-" * 25)
        for action in cleanup_plan.get("actions", []):
            status = "🔍" if action.get("needed", False) else "✅"
            console.print(f"{status} {action['description']}")
    else:
        show_success("Keyring cleanup completed!")

        if backup_first:
            console.print(f"📁 Backup created at: [cyan]{backup_results['backup_dir']}[/cyan]")

        console.print()
        console.print("📊 [bold]Cleanup Results[/bold]")
        console.print("-" * 20)

        for action, result in cleanup_results.items():
            if result.get("success", False):
                console.print(f"✅ {action}: {result.get('message', 'Completed')}")
            elif result.get("skipped", False):
                console.print(f"⏭️  {action}: {result.get('message', 'Skipped')}")
            else:
                console.print(f"❌ {action}: {result.get('message', 'Failed')}")

        console.print()
        console.print("🎯 [bold green]Next Steps[/bold green]")
        console.print("• Your keyring system is now optimized for 1Password")
        console.print("• GNOME keyring will handle only system authentication")
        console.print("• All application credentials should use 1Password")
        console.print("• Run: [cyan]system-cli keyring status[/cyan] to verify")

    return cleanup_results


@app.command()
def minimize_gnome_usage(
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be done without making changes"),
    force: bool = typer.Option(False, "--force", help="Skip confirmation prompts"),
    backup_first: bool = typer.Option(True, "--backup/--no-backup", help="Create backup before changes")
):
    """Phase 3: Minimize GNOME keyring usage and configure apps for 1Password."""
    from system_cli.core.keyring_minimizer import KeyringMinimizer

    console.print("📉 [bold blue]Phase 3: Minimize GNOME Keyring Usage[/bold blue]")
    console.print("=" * 50)
    console.print()

    if not force and not dry_run:
        console.print("🔄 [yellow]This will reconfigure applications to use 1Password![/yellow]")
        console.print("The following actions will be performed:")
        console.print("• Configure browsers to use 1Password")
        console.print("• Update development tools configuration")
        console.print("• Set up application-specific 1Password integration")
        console.print("• Minimize GNOME keyring to system-only usage")
        console.print()

        if not Confirm.ask("Do you want to proceed with minimizing GNOME keyring usage?"):
            console.print("Operation cancelled.")
            return

    # Set backup location
    backup_dir = Path.home() / "security-audits" / "keyring-consolidation" / "phase3-backup"

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:

        # Initialize minimizer
        task = progress.add_task("Initializing keyring minimizer...", total=None)
        minimizer = KeyringMinimizer(backup_dir, dry_run)
        progress.update(task, completed=True)

        # Create backup if requested
        if backup_first and not dry_run:
            task = progress.add_task("Creating pre-minimization backup...", total=None)
            backup_results = minimizer.create_backup()
            progress.update(task, completed=True)

        # Analyze current application usage
        task = progress.add_task("Analyzing application keyring usage...", total=None)
        usage_analysis = minimizer.analyze_application_usage()
        progress.update(task, completed=True)

        # Configure applications for 1Password
        task = progress.add_task("Configuring applications for 1Password...", total=None)
        config_results = minimizer.configure_applications_for_1password()
        progress.update(task, completed=True)

        # Update system configurations
        task = progress.add_task("Updating system configurations...", total=None)
        system_results = minimizer.update_system_configurations()
        progress.update(task, completed=True)

        # Verify minimization
        task = progress.add_task("Verifying minimization results...", total=None)
        verification_results = minimizer.verify_minimization()
        progress.update(task, completed=True)

    console.print()

    if dry_run:
        show_success("Phase 3 dry run completed - no changes made!")
        console.print("📋 [bold]Minimization Plan Summary[/bold]")
        console.print("-" * 30)
        for app, config in usage_analysis.get("applications", {}).items():
            status = "🔄" if config.get("needs_reconfiguration", False) else "✅"
            console.print(f"{status} {app}: {config.get('current_keyring', 'unknown')} → 1Password")
    else:
        show_success("Phase 3: GNOME keyring usage minimized!")

        if backup_first:
            console.print(f"📁 Backup created at: [cyan]{backup_results['backup_dir']}[/cyan]")

        console.print()
        console.print("📊 [bold]Minimization Results[/bold]")
        console.print("-" * 25)

        for category, result in config_results.items():
            if result.get("success", False):
                console.print(f"✅ {category}: {result.get('message', 'Configured')}")
            elif result.get("skipped", False):
                console.print(f"⏭️  {category}: {result.get('message', 'Skipped')}")
            else:
                console.print(f"❌ {category}: {result.get('message', 'Failed')}")

        console.print()
        console.print("🎯 [bold green]Phase 3 Complete![/bold green]")
        console.print("• Applications configured for 1Password")
        console.print("• GNOME keyring minimized to system use only")
        console.print("• Ready for Phase 4: Final optimization")

    return config_results


@app.command()
def optimize_configuration(
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be done without making changes"),
    force: bool = typer.Option(False, "--force", help="Skip confirmation prompts"),
    test_flows: bool = typer.Option(True, "--test/--no-test", help="Test authentication flows after optimization")
):
    """Phase 4: Optimize remaining configuration and test authentication flows."""
    from system_cli.core.keyring_optimizer import KeyringOptimizer

    console.print("⚡ [bold blue]Phase 4: Optimize Remaining Configuration[/bold blue]")
    console.print("=" * 55)
    console.print()

    if not force and not dry_run:
        console.print("🔧 [yellow]This will optimize your final keyring configuration![/yellow]")
        console.print("The following actions will be performed:")
        console.print("• Streamline systemd services")
        console.print("• Update autostart configurations")
        console.print("• Clean up configuration files")
        console.print("• Test all authentication flows")
        console.print()

        if not Confirm.ask("Do you want to proceed with final optimization?"):
            console.print("Operation cancelled.")
            return

    # Set backup location
    backup_dir = Path.home() / "security-audits" / "keyring-consolidation" / "phase4-backup"

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:

        # Initialize optimizer
        task = progress.add_task("Initializing configuration optimizer...", total=None)
        optimizer = KeyringOptimizer(backup_dir, dry_run)
        progress.update(task, completed=True)

        # Create backup
        if not dry_run:
            task = progress.add_task("Creating pre-optimization backup...", total=None)
            backup_results = optimizer.create_backup()
            progress.update(task, completed=True)

        # Streamline systemd services
        task = progress.add_task("Streamlining systemd services...", total=None)
        systemd_results = optimizer.optimize_systemd_services()
        progress.update(task, completed=True)

        # Update autostart configurations
        task = progress.add_task("Optimizing autostart configurations...", total=None)
        autostart_results = optimizer.optimize_autostart_configs()
        progress.update(task, completed=True)

        # Clean up configuration files
        task = progress.add_task("Cleaning up configuration files...", total=None)
        cleanup_results = optimizer.cleanup_config_files()
        progress.update(task, completed=True)

        # Test authentication flows
        if test_flows:
            task = progress.add_task("Testing authentication flows...", total=None)
            test_results = optimizer.test_authentication_flows()
            progress.update(task, completed=True)

    console.print()

    if dry_run:
        show_success("Phase 4 dry run completed - no changes made!")
        console.print("📋 [bold]Optimization Plan Summary[/bold]")
        console.print("-" * 30)
        console.print("🔧 Systemd services to optimize")
        console.print("🚀 Autostart configurations to update")
        console.print("🧹 Configuration files to clean")
        if test_flows:
            console.print("🧪 Authentication flows to test")
    else:
        show_success("Phase 4: Configuration optimization complete!")

        console.print(f"📁 Backup created at: [cyan]{backup_results['backup_dir']}[/cyan]")

        console.print()
        console.print("📊 [bold]Optimization Results[/bold]")
        console.print("-" * 25)

        all_results = {
            "systemd_services": systemd_results,
            "autostart_configs": autostart_results,
            "config_cleanup": cleanup_results
        }

        if test_flows:
            all_results["authentication_tests"] = test_results

        for category, result in all_results.items():
            if isinstance(result, dict):
                if result.get("success", False):
                    console.print(f"✅ {category}: {result.get('message', 'Completed')}")
                else:
                    console.print(f"❌ {category}: {result.get('message', 'Failed')}")
            else:
                console.print(f"ℹ️  {category}: Completed")

        console.print()
        console.print("🎉 [bold green]Keyring Consolidation Complete![/bold green]")
        console.print("=" * 40)
        console.print("✅ Phase 1: Audit and backup")
        console.print("✅ Phase 2: Cleanup and optimization")
        console.print("✅ Phase 3: Minimize GNOME keyring usage")
        console.print("✅ Phase 4: Final configuration optimization")
        console.print()
        console.print("🎯 [bold]Your authentication system is now:[/bold]")
        console.print("• Fully consolidated with 1Password")
        console.print("• Minimal GNOME keyring for system auth only")
        console.print("• Optimized for performance and security")
        console.print("• Ready for production use")

    return all_results if not dry_run else {"dry_run": True}


@app.command()
def emergency_recovery(
    action: str = typer.Option("status", "--action", help="Recovery action: status, disable, emergency, test"),
    force: bool = typer.Option(False, "--force", help="Skip confirmation prompts")
):
    """Emergency TPM recovery and fallback management."""
    console.print("🚨 [bold red]Emergency TPM Recovery System[/bold red]")
    console.print("=" * 40)
    console.print()

    emergency_script = Path.home() / ".config" / "tpm-keyring" / "emergency-recovery.sh"

    if action == "status":
        console.print("📊 [bold]TPM Integration Status[/bold]")
        console.print("-" * 30)

        # Check TPM service status
        try:
            result = run_command(["systemctl", "--user", "is-active", "tpm-keyring-unlock-enhanced.service"], capture_output=True)
            if result.returncode == 0:
                console.print("✅ TPM Service: [green]Active[/green]")
            else:
                console.print("❌ TPM Service: [red]Inactive[/red]")
        except:
            console.print("❌ TPM Service: [red]Not configured[/red]")

        # Check TPM files
        tpm_dir = Path.home() / ".config" / "tpm-keyring"
        if tpm_dir.exists():
            console.print("✅ TPM Directory: [green]Present[/green]")

            password_file = tpm_dir / "keyring-password.enc"
            if password_file.exists():
                console.print("✅ Encrypted Password: [green]Present[/green]")
            else:
                console.print("❌ Encrypted Password: [red]Missing[/red]")

            if emergency_script.exists():
                console.print("✅ Emergency Recovery: [green]Available[/green]")
            else:
                console.print("❌ Emergency Recovery: [red]Missing[/red]")
        else:
            console.print("❌ TPM Directory: [red]Missing[/red]")

        # Check keyring status
        try:
            result = run_command(["secret-tool", "search", "keyring", "login"], capture_output=True)
            if result.returncode == 0:
                console.print("✅ Keyring Status: [green]Unlocked[/green]")
            else:
                console.print("🔒 Keyring Status: [yellow]Locked[/yellow]")
        except:
            console.print("❓ Keyring Status: [yellow]Unknown[/yellow]")

    elif action == "test":
        console.print("🧪 [bold]Testing Fallback Methods[/bold]")
        console.print("-" * 30)

        # Test 1: Password unlock
        console.print("Test 1: Manual password unlock...")
        try:
            result = run_command(["gnome-keyring-daemon", "--unlock"], input="", capture_output=True, text=True)
            if result.returncode == 0:
                show_success("Password unlock method: WORKING")
            else:
                show_warning("Password unlock method: May require manual input")
        except Exception as e:
            show_error(f"Password unlock test failed: {e}")

        # Test 2: Keyring daemon
        console.print("\nTest 2: Keyring daemon status...")
        try:
            result = run_command(["systemctl", "--user", "is-active", "gnome-keyring-daemon"], capture_output=True)
            if result.returncode == 0:
                show_success("Keyring daemon: RUNNING")
            else:
                show_error("Keyring daemon: NOT RUNNING")
        except Exception as e:
            show_error(f"Keyring daemon test failed: {e}")

        # Test 3: Emergency script
        console.print("\nTest 3: Emergency recovery script...")
        if emergency_script.exists():
            show_success("Emergency recovery script: AVAILABLE")
            console.print(f"Location: [cyan]{emergency_script}[/cyan]")
        else:
            show_error("Emergency recovery script: MISSING")

    elif action == "disable":
        if not force:
            console.print("⚠️  [yellow]This will disable TPM integration![/yellow]")
            console.print("Your keyring will revert to password-only authentication.")
            console.print()
            if not Confirm.ask("Continue with TPM disable?"):
                console.print("Operation cancelled.")
                return

        console.print("🔄 [bold]Disabling TPM Integration[/bold]")
        console.print("-" * 30)

        # Stop TPM service
        try:
            run_command(["systemctl", "--user", "stop", "tpm-keyring-unlock-enhanced.service"], capture_output=True)
            run_command(["systemctl", "--user", "disable", "tpm-keyring-unlock-enhanced.service"], capture_output=True)
            show_success("TPM service stopped and disabled")
        except:
            show_warning("TPM service was not running")

        # Remove autostart
        autostart_file = Path.home() / ".config" / "autostart" / "tpm-keyring-unlock.desktop"
        if autostart_file.exists():
            autostart_file.unlink()
            show_success("TPM autostart removed")

        # Backup TPM config
        tpm_dir = Path.home() / ".config" / "tpm-keyring"
        if tpm_dir.exists():
            backup_name = f"tpm-keyring.disabled.{int(time.time())}"
            backup_dir = tpm_dir.parent / backup_name
            tpm_dir.rename(backup_dir)
            show_success(f"TPM configuration backed up to: {backup_name}")

        show_success("TPM integration disabled successfully")
        console.print()
        console.print("🔑 Your keyring now uses password authentication only")
        console.print("Run: [cyan]gnome-keyring-daemon --unlock[/cyan] to unlock manually")

    elif action == "emergency":
        console.print("🚨 [bold red]EMERGENCY RECOVERY MODE[/bold red]")
        console.print("=" * 35)
        console.print()
        console.print("This will completely disable TPM and restore normal keyring operation.")
        console.print("Use this only if TPM integration has completely failed.")
        console.print()

        if not force:
            if not Confirm.ask("[bold red]Proceed with emergency recovery?[/bold red]"):
                console.print("Emergency recovery cancelled.")
                return

        console.print("🔄 [bold]Executing Emergency Recovery[/bold]")
        console.print("-" * 35)

        # Kill all TPM processes
        try:
            run_command(["pkill", "-f", "tpm-unlock-keyring"], capture_output=True)
            run_command(["pkill", "-f", "clevis"], capture_output=True)
            show_success("TPM processes terminated")
        except:
            show_warning("No TPM processes found")

        # Stop services
        try:
            run_command(["systemctl", "--user", "stop", "tpm-keyring-unlock-enhanced.service"], capture_output=True)
            run_command(["sudo", "systemctl", "stop", "tcsd"], capture_output=True)
            show_success("TPM services stopped")
        except:
            show_warning("Some TPM services were not running")

        # Restart keyring daemon
        try:
            run_command(["systemctl", "--user", "restart", "gnome-keyring-daemon"], capture_output=True)
            show_success("Keyring daemon restarted")
        except Exception as e:
            show_error(f"Failed to restart keyring daemon: {e}")

        show_success("Emergency recovery completed!")
        console.print()
        console.print("🔑 [bold green]Next Steps:[/bold green]")
        console.print("1. Unlock your keyring: [cyan]gnome-keyring-daemon --unlock[/cyan]")
        console.print("2. Test keyring access: [cyan]secret-tool search keyring login[/cyan]")
        console.print("3. Re-enable TPM later: [cyan]system-cli keyring tpm-setup-enhanced[/cyan]")

    else:
        show_error(f"Unknown action: {action}")
        console.print("Available actions: status, test, disable, emergency")
        raise typer.Exit(1)


@app.command()
def unlock_manual(
    method: str = typer.Option("password", "--method", help="Unlock method: password, auto"),
    timeout: int = typer.Option(30, "--timeout", help="Timeout in seconds")
):
    """Manual keyring unlock with fallback options."""
    console.print("🔓 [bold blue]Manual Keyring Unlock[/bold blue]")
    console.print("=" * 30)
    console.print()

    if method == "password":
        console.print("🔑 Using password authentication...")
        try:
            # Use timeout to prevent hanging
            result = run_command(
                ["timeout", str(timeout), "gnome-keyring-daemon", "--unlock"],
                capture_output=False
            )

            if result.returncode == 0:
                show_success("Keyring unlocked successfully with password")

                # Verify unlock
                verify_result = run_command(["secret-tool", "search", "keyring", "login"], capture_output=True)
                if verify_result.returncode == 0:
                    show_success("Keyring access verified")
                else:
                    show_warning("Keyring unlocked but no items found (normal if empty)")
            else:
                show_error("Password unlock failed or timed out")
                raise typer.Exit(1)

        except Exception as e:
            show_error(f"Manual unlock failed: {e}")
            raise typer.Exit(1)

    elif method == "auto":
        console.print("🔄 Trying automatic unlock methods...")

        # Try enhanced TPM script if available
        enhanced_script = Path.home() / ".config" / "tpm-keyring" / "tpm-unlock-keyring-enhanced.sh"
        if enhanced_script.exists():
            console.print("Attempting enhanced TPM unlock...")
            try:
                result = run_command([str(enhanced_script), "auto"], capture_output=False)
                if result.returncode == 0:
                    show_success("Automatic unlock successful")
                    return
            except:
                pass

        # Fallback to password
        console.print("Falling back to password unlock...")
        try:
            result = run_command(["gnome-keyring-daemon", "--unlock"], capture_output=False)
            if result.returncode == 0:
                show_success("Password unlock successful")
            else:
                show_error("All unlock methods failed")
                raise typer.Exit(1)
        except Exception as e:
            show_error(f"Fallback unlock failed: {e}")
            raise typer.Exit(1)

    else:
        show_error(f"Unknown unlock method: {method}")
        console.print("Available methods: password, auto")
        raise typer.Exit(1)


@app.command()
def tpm_setup_enhanced(
    force: bool = typer.Option(False, "--force", help="Skip confirmation prompts")
):
    """Setup enhanced TPM integration with robust fallback logic."""
    console.print("🔐 [bold blue]Enhanced TPM Setup with Fallback Logic[/bold blue]")
    console.print("=" * 50)
    console.print()

    enhanced_script = Path.home() / "cursor-system" / "scripts" / "tpm-enhanced-setup.sh"

    if not enhanced_script.exists():
        show_error(f"Enhanced TPM setup script not found: {enhanced_script}")
        console.print("Please ensure the script is available in the scripts directory")
        raise typer.Exit(1)

    if not force:
        console.print("🔧 [yellow]This will setup enhanced TPM integration![/yellow]")
        console.print("Features:")
        console.print("• Multiple unlock methods: TPM → Fingerprint → Password")
        console.print("• Emergency recovery script for complete TPM failure")
        console.print("• Timeout protection to prevent hanging")
        console.print("• Password fallback always available")
        console.print()

        if not Confirm.ask("Proceed with enhanced TPM setup?"):
            console.print("Setup cancelled.")
            return

    console.print("🚀 Running enhanced TPM setup...")
    result = run_command([str(enhanced_script), "setup"], capture_output=False)

    if result.returncode == 0:
        show_success("Enhanced TPM setup completed successfully!")
        console.print()
        console.print("🎯 [bold green]Available Commands:[/bold green]")
        console.print("• Test integration: [cyan]system-cli keyring emergency-recovery --action test[/cyan]")
        console.print("• Check status: [cyan]system-cli keyring emergency-recovery --action status[/cyan]")
        console.print("• Emergency recovery: [cyan]system-cli keyring emergency-recovery --action emergency[/cyan]")
        console.print("• Manual unlock: [cyan]system-cli keyring unlock-manual[/cyan]")
    else:
        show_error("Enhanced TPM setup failed")
        console.print()
        console.print("🔧 [yellow]Troubleshooting:[/yellow]")
        console.print("• Check TPM hardware: [cyan]sudo tpm2_getcap properties-fixed[/cyan]")
        console.print("• Verify user groups: [cyan]groups | grep tss[/cyan]")
        console.print("• Manual unlock: [cyan]system-cli keyring unlock-manual[/cyan]")
        raise typer.Exit(1)


@app.command()
def move_item(
    label: str = typer.Argument(..., help="Label of the item to move"),
    from_keyring: str = typer.Option(..., "--from", help="Source keyring (secure/convenience)"),
    to_keyring: str = typer.Option(..., "--to", help="Destination keyring (secure/convenience)"),
    force: bool = typer.Option(False, "--force", help="Force move without confirmation")
):
    """Move an item between keyrings."""
    if from_keyring == to_keyring:
        show_error("Source and destination keyrings cannot be the same")
        raise typer.Exit(1)

    if from_keyring not in ["secure", "convenience"] or to_keyring not in ["secure", "convenience"]:
        show_error("Keyrings must be 'secure' or 'convenience'")
        raise typer.Exit(1)

    if not force:
        if not Confirm.ask(f"Move '{label}' from {from_keyring} to {to_keyring} keyring?"):
            console.print("Operation cancelled.")
            return

    manager = KeyringManager()
    success = manager.move_item(label, from_keyring, to_keyring)

    if success:
        show_success(f"Item '{label}' moved from {from_keyring} to {to_keyring} keyring")
    else:
        show_error(f"Failed to move item '{label}'")
        raise typer.Exit(1)
