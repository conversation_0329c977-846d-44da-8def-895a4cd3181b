"""
System maintenance commands.
Handles disk cleanup, cache management, and system optimization.
"""

import typer
from typing import Optional
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Confirm

from system_cli.core.maintenance import MaintenanceManager
from system_cli.utils.display import show_error, show_success, show_warning
from system_cli.utils.helpers import run_command

app = typer.Typer(help="🧹 System maintenance and cleanup")
console = Console()


@app.command()
def cleanup(
    interactive: bool = typer.Option(False, "--interactive", help="Interactive cleanup with prompts"),
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be cleaned without doing it"),
    all_items: bool = typer.Option(False, "--all", help="Clean all items without prompting"),
    package_cache: bool = typer.Option(False, "--package-cache", help="Clean package cache"),
    docker_images: bool = typer.Option(False, "--docker", help="Clean Docker images and containers"),
    system_logs: bool = typer.Option(False, "--logs", help="Clean system logs"),
    temp_files: bool = typer.Option(False, "--temp", help="Clean temporary files (excludes ~/.cache)"),
    browser_cache: bool = typer.Option(False, "--browser", help="Clean browser cache"),
    thumbnails: bool = typer.Option(False, "--thumbnails", help="Clean thumbnail cache"),
    user_cache: bool = typer.Option(False, "--user-cache", help="Clean user cache directory (~/.cache) - use with caution")
):
    """System cleanup operations to free disk space."""
    manager = MaintenanceManager()
    
    if interactive:
        console.print("🧹 [bold blue]Interactive System Cleanup[/bold blue]")
        console.print("This will help you clean up disk space and optimize system performance.\n")

        # Get current disk usage
        disk_info = manager.get_disk_usage()
        _display_disk_usage(disk_info)

        # Show what cleanup items are available
        console.print("\n📋 [bold blue]Available Cleanup Items:[/bold blue]")
        estimates = manager.get_cleanup_estimates()
        _display_cleanup_checklist(estimates)

        console.print("\n💡 [bold yellow]How this works:[/bold yellow]")
        console.print("• You will be asked about each cleanup item individually")
        console.print("• You can choose which items to clean")
        console.print("• High-risk items will have warnings")
        console.print("• You can cancel at any time")

        if not Confirm.ask("\nWould you like to proceed with interactive cleanup?"):
            console.print("Cleanup cancelled.")
            return

        # Interactive selection of cleanup items
        cleanup_items = _get_interactive_cleanup_selection(manager)
    else:
        # Use command line flags or default to all
        cleanup_items = {
            'package_cache': package_cache or all_items,
            'docker_images': docker_images or all_items,
            'system_logs': system_logs or all_items,
            'temp_files': temp_files or all_items,
            'browser_cache': browser_cache or all_items,
            'thumbnails': thumbnails or all_items,
            'user_cache': user_cache  # Never include in --all for safety
        }
        
        # If no specific items selected, default to safe items
        if not any(cleanup_items.values()):
            cleanup_items = {
                'package_cache': True,
                'system_logs': True,
                'temp_files': True,
                'thumbnails': True,
                'docker_images': False,  # Requires confirmation
                'browser_cache': False   # Requires confirmation
            }
    
    if dry_run:
        console.print("🔍 [bold yellow]Dry Run - Showing what would be cleaned:[/bold yellow]")
        cleanup_plan = manager.get_cleanup_plan(cleanup_items)
        _display_cleanup_plan(cleanup_plan)
        return
    
    # Execute cleanup
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Running system cleanup...", total=None)
        cleanup_results = manager.run_cleanup(cleanup_items)
        progress.update(task, completed=True)
    
    _display_cleanup_results(cleanup_results)
    
    # Show updated disk usage
    console.print("\n📊 [bold green]Updated Disk Usage:[/bold green]")
    updated_disk_info = manager.get_disk_usage()
    _display_disk_usage(updated_disk_info)


@app.command()
def status():
    """Show system maintenance status and disk usage."""
    manager = MaintenanceManager()
    
    console.print("🔧 [bold blue]System Maintenance Status[/bold blue]\n")
    
    # Show disk usage
    disk_info = manager.get_disk_usage()
    _display_disk_usage(disk_info)
    
    # Show cleanup estimates
    console.print("\n📊 [bold blue]Cleanup Opportunities:[/bold blue]")
    estimates = manager.get_cleanup_estimates()
    _display_cleanup_estimates(estimates)


@app.command()
def analyze(
    temp_files: bool = typer.Option(False, "--temp-files", help="Analyze temporary files breakdown"),
    all_items: bool = typer.Option(False, "--all", help="Analyze all cleanup items"),
    show_paths: bool = typer.Option(False, "--show-paths", help="Show actual file paths")
):
    """Analyze what files would be cleaned in detail."""
    manager = MaintenanceManager()

    if temp_files or all_items:
        console.print("📁 [bold blue]Temporary Files Analysis[/bold blue]\n")
        _display_temp_files_breakdown(manager, show_paths)

    if all_items:
        console.print("\n📊 [bold blue]All Cleanup Items Analysis[/bold blue]")
        estimates = manager.get_cleanup_estimates()
        _display_cleanup_estimates(estimates)


@app.command()
def schedule(
    enable: bool = typer.Option(False, "--enable", help="Enable scheduled maintenance"),
    disable: bool = typer.Option(False, "--disable", help="Disable scheduled maintenance"),
    show: bool = typer.Option(False, "--show", help="Show current schedule"),
    frequency: str = typer.Option("weekly", "--frequency", help="Schedule frequency: daily, weekly, monthly")
):
    """Manage scheduled maintenance tasks."""
    if show or not any([enable, disable]):
        console.print("📅 [bold blue]Scheduled Maintenance Status[/bold blue]")
        # TODO: Implement scheduled maintenance status
        console.print("Scheduled maintenance is not yet implemented.")
        return

    if enable:
        console.print(f"🔄 Enabling {frequency} scheduled maintenance...")
        # TODO: Implement scheduled maintenance setup
        show_success(f"Scheduled maintenance enabled ({frequency})")

    if disable:
        console.print("⏹️ Disabling scheduled maintenance...")
        # TODO: Implement scheduled maintenance disable
        show_success("Scheduled maintenance disabled")


@app.command()
def config_cleanup():
    """Clean up configuration files (replaces config-cleanup.sh)."""
    script_path = Path.home() / "cursor-system" / "scripts" / "config-cleanup.sh"

    if not script_path.exists():
        show_error(f"Config cleanup script not found: {script_path}")
        raise typer.Exit(1)

    console.print("🧹 Cleaning up configuration files...")

    result = run_command([str(script_path)], capture_output=False)

    if result.returncode == 0:
        show_success("Configuration cleanup completed successfully")
    else:
        show_error("Configuration cleanup failed")
        raise typer.Exit(1)


def _display_disk_usage(disk_info: dict):
    """Display disk usage information."""
    usage_percent = disk_info.get('usage_percent', 0)
    usage_color = "red" if usage_percent >= 90 else "yellow" if usage_percent >= 80 else "green"
    
    usage_panel = Panel(
        f"Total: {disk_info.get('total', 'Unknown')}\n"
        f"Used: {disk_info.get('used', 'Unknown')} ({usage_percent}%)\n"
        f"Free: {disk_info.get('free', 'Unknown')}\n"
        f"Mount: {disk_info.get('mount_point', '/')}",
        title=f"💾 Disk Usage",
        border_style=usage_color
    )
    console.print(usage_panel)


def _get_interactive_cleanup_selection(manager) -> dict:
    """Get interactive cleanup selection from user."""
    cleanup_items = {}
    
    # Get cleanup estimates
    estimates = manager.get_cleanup_estimates()
    
    console.print("\n🧹 [bold blue]Select cleanup items:[/bold blue]")
    
    # Package cache
    pkg_size = estimates.get('package_cache', {}).get('size', 'Unknown')
    if Confirm.ask(f"Clean package cache? (Estimated: {pkg_size})"):
        cleanup_items['package_cache'] = True
    
    # System logs
    log_size = estimates.get('system_logs', {}).get('size', 'Unknown')
    if Confirm.ask(f"Clean old system logs? (Estimated: {log_size})"):
        cleanup_items['system_logs'] = True
    
    # Temporary files
    temp_size = estimates.get('temp_files', {}).get('size', 'Unknown')
    if Confirm.ask(f"Clean temporary files? (Estimated: {temp_size})"):
        cleanup_items['temp_files'] = True
    
    # Thumbnails
    thumb_size = estimates.get('thumbnails', {}).get('size', 'Unknown')
    if Confirm.ask(f"Clean thumbnail cache? (Estimated: {thumb_size})"):
        cleanup_items['thumbnails'] = True
    
    # Docker (with warning)
    docker_size = estimates.get('docker_images', {}).get('size', 'Unknown')
    if estimates.get('docker_images', {}).get('available', False):
        console.print(f"\n⚠️  [yellow]Docker cleanup will remove unused images and containers[/yellow]")
        if Confirm.ask(f"Clean Docker images/containers? (Estimated: {docker_size})"):
            cleanup_items['docker_images'] = True
    
    # Browser cache (with warning)
    browser_size = estimates.get('browser_cache', {}).get('size', 'Unknown')
    console.print(f"\n⚠️  [yellow]Browser cache cleanup will clear saved web data[/yellow]")
    if Confirm.ask(f"Clean browser cache? (Estimated: {browser_size})"):
        cleanup_items['browser_cache'] = True

    # User cache (with strong warning)
    user_cache_size = estimates.get('user_cache', {}).get('size', 'Unknown')
    console.print(f"\n🚨 [red]WARNING: User cache cleanup will remove ~/.cache[/red]")
    console.print("   [red]This may significantly slow down applications and require re-downloading data[/red]")
    if Confirm.ask(f"Clean user cache directory? (Estimated: {user_cache_size})"):
        cleanup_items['user_cache'] = True

    return cleanup_items


def _display_cleanup_plan(cleanup_plan: dict):
    """Display cleanup plan for dry run."""
    table = Table(title="🧹 Cleanup Plan")
    table.add_column("Item", style="cyan")
    table.add_column("Action", style="yellow")
    table.add_column("Estimated Size", style="green")
    table.add_column("Risk", style="red")
    
    for item, details in cleanup_plan.items():
        # Skip the total_estimated_size entry as it's not a cleanup item
        if item == 'total_estimated_size':
            continue
            
        risk_level = details.get('risk', 'Low')
        risk_color = "red" if risk_level == "High" else "yellow" if risk_level == "Medium" else "green"
        
        table.add_row(
            details.get('name', item),
            details.get('action', ''),
            details.get('size', 'Unknown'),
            f"[{risk_color}]{risk_level}[/{risk_color}]"
        )
    
    console.print(table)
    
    total_size = cleanup_plan.get('total_estimated_size', 'Unknown')
    console.print(f"\n📊 Total estimated space to be freed: [bold green]{total_size}[/bold green]")


def _display_cleanup_results(results: dict):
    """Display cleanup results."""
    success_count = len(results.get('successful', []))
    failed_count = len(results.get('failed', []))
    
    # Summary
    if success_count > 0:
        show_success(f"Successfully cleaned {success_count} items")
    
    if failed_count > 0:
        show_error(f"Failed to clean {failed_count} items")
    
    # Detailed results table
    if results.get('details'):
        results_table = Table(title="🧹 Cleanup Results")
        results_table.add_column("Item", style="cyan")
        results_table.add_column("Status", style="green")
        results_table.add_column("Space Freed", style="yellow")
        results_table.add_column("Details", style="white")
        
        for item, details in results['details'].items():
            status_icon = "✅" if details.get('success') else "❌"
            status_text = "Success" if details.get('success') else "Failed"
            
            results_table.add_row(
                details.get('name', item),
                f"{status_icon} {status_text}",
                details.get('space_freed', 'Unknown'),
                details.get('message', '')
            )
        
        console.print(results_table)
    
    # Total space freed
    total_freed = results.get('total_space_freed', 'Unknown')
    if total_freed != 'Unknown':
        freed_panel = Panel(
            f"Total space freed: {total_freed}",
            title="💾 Space Freed",
            border_style="green"
        )
        console.print(freed_panel)


def _display_cleanup_estimates(estimates: dict):
    """Display cleanup estimates."""
    table = Table(title="🧹 Cleanup Opportunities")
    table.add_column("Item", style="cyan")
    table.add_column("Estimated Size", style="green")
    table.add_column("Available", style="blue")

    for item, details in estimates.items():
        available_icon = "✅" if details.get('available', False) else "❌"
        available_text = "Yes" if details.get('available', False) else "No"

        table.add_row(
            item.replace('_', ' ').title(),
            details.get('size', 'Unknown'),
            f"{available_icon} {available_text}"
        )

    console.print(table)


def _display_cleanup_checklist(estimates: dict):
    """Display cleanup checklist with checkboxes."""
    from rich.table import Table

    table = Table(show_header=True, header_style="bold blue")
    table.add_column("☐", style="white", width=3)
    table.add_column("Cleanup Item", style="cyan", min_width=20)
    table.add_column("Size", style="green", justify="right", min_width=12)
    table.add_column("Risk", style="yellow", justify="center", min_width=8)
    table.add_column("Description", style="white", min_width=30)

    # Define cleanup items with their properties
    cleanup_items = [
        {
            'key': 'package_cache',
            'name': 'Package Cache',
            'risk': 'Low',
            'description': 'Remove cached .deb packages and orphaned packages'
        },
        {
            'key': 'system_logs',
            'name': 'System Logs',
            'risk': 'Low',
            'description': 'Clean logs older than 7 days'
        },
        {
            'key': 'temp_files',
            'name': 'Temporary Files',
            'risk': 'Low',
            'description': '/tmp, /var/tmp (>7 days), ~/.local/share/Trash (>1 day)'
        },
        {
            'key': 'thumbnails',
            'name': 'Thumbnail Cache',
            'risk': 'Low',
            'description': 'Clear image thumbnail cache'
        },
        {
            'key': 'docker_images',
            'name': 'Docker Images',
            'risk': 'Medium',
            'description': 'Remove unused Docker images and containers'
        },
        {
            'key': 'browser_cache',
            'name': 'Browser Cache',
            'risk': 'Medium',
            'description': 'Clear web browser cache and temporary files'
        },
        {
            'key': 'user_cache',
            'name': 'User Cache',
            'risk': 'High',
            'description': 'Clear ~/.cache (may slow down applications)'
        }
    ]

    for item in cleanup_items:
        key = item['key']
        details = estimates.get(key, {})
        available = details.get('available', False)
        size = details.get('size', 'Unknown')

        # Style based on availability and risk
        checkbox = "☐" if available else "☒"
        risk_color = "red" if item['risk'] == 'High' else "yellow" if item['risk'] == 'Medium' else "green"

        if not available:
            # Gray out unavailable items
            table.add_row(
                f"[dim]{checkbox}[/dim]",
                f"[dim]{item['name']}[/dim]",
                f"[dim]{size}[/dim]",
                f"[dim]N/A[/dim]",
                f"[dim]Not available[/dim]"
            )
        else:
            table.add_row(
                checkbox,
                item['name'],
                size,
                f"[{risk_color}]{item['risk']}[/{risk_color}]",
                item['description']
            )

    console.print(table)

    # Add legend
    console.print("\n📝 [bold]Legend:[/bold]")
    console.print("  ☐ Available for cleanup")
    console.print("  ☒ Not available/applicable")
    console.print("  [green]Low Risk[/green]: Safe to clean, no data loss")
    console.print("  [yellow]Medium Risk[/yellow]: May require re-downloading or re-login")
    console.print("  [red]High Risk[/red]: Could affect system functionality")


def _display_temp_files_breakdown(manager, show_paths: bool = False):
    """Display detailed breakdown of temporary files."""
    from rich.table import Table
    import os
    import subprocess
    from pathlib import Path

    table = Table(title="📁 Temporary Files Breakdown")
    table.add_column("Directory", style="cyan", min_width=25)
    table.add_column("Size", style="green", justify="right", min_width=12)
    table.add_column("Cleanup Policy", style="yellow", min_width=20)
    table.add_column("Description", style="white", min_width=35)

    temp_dirs_info = [
        {
            'path': '/tmp',
            'policy': 'Files older than 7 days',
            'description': 'System temporary files, application temp data'
        },
        {
            'path': '/var/tmp',
            'policy': 'Files older than 7 days',
            'description': 'System temporary files that survive reboots'
        },
        {
            'path': str(manager.home_dir / '.local/share/Trash'),
            'policy': 'Files older than 1 day',
            'description': 'User deleted files (trash/recycle bin)'
        }
    ]

    # Show excluded cache directory info
    cache_dir = str(manager.home_dir / '.cache')
    if os.path.exists(cache_dir):
        cache_size = manager._get_directory_size_bytes(cache_dir)
        cache_size_str = manager._format_bytes(cache_size)
        console.print(f"\n💡 [bold yellow]Note:[/bold yellow] User cache directory [cyan]{cache_dir}[/cyan] ({cache_size_str}) is [bold green]excluded[/bold green] from cleanup")
        console.print("   This preserves application performance and user preferences")

    total_size = 0
    for dir_info in temp_dirs_info:
        path = dir_info['path']
        if os.path.exists(path):
            size_bytes = manager._get_directory_size_bytes(path)
            size_str = manager._format_bytes(size_bytes)
            total_size += size_bytes
        else:
            size_str = "Not found"

        table.add_row(
            path,
            size_str,
            dir_info['policy'],
            dir_info['description']
        )

    console.print(table)

    # Show total
    total_panel = Panel(
        f"Total temporary files: {manager._format_bytes(total_size)}",
        title="📊 Total Size",
        border_style="blue"
    )
    console.print(total_panel)

    # Show what files would be cleaned
    if show_paths:
        console.print("\n🔍 [bold blue]Sample files that would be cleaned:[/bold blue]")
        for dir_info in temp_dirs_info:
            path = dir_info['path']
            if os.path.exists(path):
                console.print(f"\n📂 [cyan]{path}[/cyan]:")
                try:
                    # Show sample of old files that would be deleted
                    days = 7 if path in ['/tmp', '/var/tmp'] else 1
                    result = subprocess.run(
                        ['find', path, '-type', 'f', '-mtime', f'+{days}', '-ls'],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    if result.returncode == 0 and result.stdout.strip():
                        lines = result.stdout.strip().split('\n')[:5]  # Show first 5 files
                        for line in lines:
                            # Extract filename from ls output
                            parts = line.split()
                            if len(parts) > 10:
                                filename = ' '.join(parts[10:])
                                console.print(f"  • {filename}")
                        if len(result.stdout.strip().split('\n')) > 5:
                            console.print(f"  ... and {len(result.stdout.strip().split('\n')) - 5} more files")
                    else:
                        console.print("  [dim]No old files found[/dim]")
                except Exception as e:
                    console.print(f"  [red]Error analyzing {path}: {e}[/red]")

    # Add safety note
    console.print("\n⚠️  [bold yellow]Safety Notes:[/bold yellow]")
    console.print("• Only files older than the specified days are removed")
    console.print("• Directory structure is preserved")
    console.print("• Currently running applications are not affected")
    console.print("• System files and important data are not touched")
