"""
Monitoring and automation commands.
Converted from monitoring scripts.
"""

import typer
from typing import Optional
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Confirm, Prompt
from rich.panel import Panel
from rich.table import Table

from system_cli.core.monitor_manager import MonitorManager
from system_cli.utils.display import show_error, show_success, show_warning
from system_cli.utils.helpers import run_command

app = typer.Typer(help="📊 Monitoring and automation")
console = Console()


@app.command()
def install(
    email: Optional[str] = typer.Option(None, "--email", help="Email address for alerts"),
    daily: bool = typer.Option(True, "--daily/--no-daily", help="Enable daily monitoring"),
    weekly: bool = typer.Option(True, "--weekly/--no-weekly", help="Enable weekly monitoring"),
    monthly: bool = typer.Option(True, "--monthly/--no-monthly", help="Enable monthly monitoring"),
    output_dir: Optional[Path] = typer.Option(None, "--output-dir", help="Directory for reports")
):
    """Install automated security monitoring."""
    monitor_manager = MonitorManager()
    
    # Get email if not provided
    if not email and Confirm.ask("Enable email alerts?"):
        email = Prompt.ask("Enter email address for alerts")
    
    console.print("🤖 Installing automated security monitoring...")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        
        # Setup directories
        task1 = progress.add_task("Setting up directories...", total=None)
        monitor_manager.setup_directories(output_dir)
        progress.update(task1, completed=True)
        
        # Create configuration
        task2 = progress.add_task("Creating configuration...", total=None)
        monitor_manager.create_configuration(email, daily, weekly, monthly)
        progress.update(task2, completed=True)
        
        # Install cron jobs
        task3 = progress.add_task("Installing cron jobs...", total=None)
        monitor_manager.install_cron_jobs()
        progress.update(task3, completed=True)
        
        # Setup email if configured
        if email:
            task4 = progress.add_task("Configuring email alerts...", total=None)
            monitor_manager.setup_email_alerts(email)
            progress.update(task4, completed=True)
    
    show_success("Automated monitoring installed successfully!")
    
    # Show configuration summary
    _show_monitoring_config(monitor_manager.get_configuration())
    
    # Test configuration
    if Confirm.ask("Test monitoring configuration?"):
        _test_monitoring(monitor_manager)


@app.command()
def uninstall(
    keep_reports: bool = typer.Option(True, "--keep-reports/--remove-reports", help="Keep existing reports"),
    backup_config: bool = typer.Option(True, "--backup/--no-backup", help="Backup configuration")
):
    """Uninstall automated monitoring."""
    if not Confirm.ask("[red]⚠️ This will remove all monitoring cron jobs. Continue?[/red]"):
        console.print("Uninstall cancelled.")
        return
    
    monitor_manager = MonitorManager()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        
        # Backup configuration
        if backup_config:
            task1 = progress.add_task("Backing up configuration...", total=None)
            backup_path = monitor_manager.backup_configuration()
            progress.update(task1, completed=True)
            show_success(f"Configuration backed up to: {backup_path}")
        
        # Remove cron jobs
        task2 = progress.add_task("Removing cron jobs...", total=None)
        monitor_manager.remove_cron_jobs()
        progress.update(task2, completed=True)
        
        # Clean up files
        if not keep_reports:
            task3 = progress.add_task("Removing reports...", total=None)
            monitor_manager.remove_reports()
            progress.update(task3, completed=True)
    
    show_success("Automated monitoring uninstalled successfully!")


@app.command()
def status():
    """Show monitoring status and recent activity."""
    monitor_manager = MonitorManager()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Checking monitoring status...", total=None)
        status_data = monitor_manager.get_status()
        progress.update(task, completed=True)
    
    _display_monitoring_status(status_data)


@app.command()
def test(
    component: str = typer.Option("all", "--component", help="Component to test: email, cron, reports, all"),
    send_email: bool = typer.Option(False, "--send-email", help="Send test email")
):
    """Test monitoring configuration."""
    monitor_manager = MonitorManager()
    
    console.print(f"🧪 Testing monitoring configuration: {component}")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Running tests...", total=None)
        test_results = monitor_manager.test_configuration(component, send_email)
        progress.update(task, completed=True)
    
    _display_test_results(test_results)


@app.command()
def configure(
    email: Optional[str] = typer.Option(None, "--email", help="Update email address"),
    schedule: Optional[str] = typer.Option(None, "--schedule", help="Update schedule (daily/weekly/monthly)"),
    output_dir: Optional[Path] = typer.Option(None, "--output-dir", help="Update output directory")
):
    """Update monitoring configuration."""
    monitor_manager = MonitorManager()
    
    current_config = monitor_manager.get_configuration()
    console.print("📝 Current Configuration:")
    _show_monitoring_config(current_config)
    console.print()
    
    # Interactive configuration update
    if not any([email, schedule, output_dir]):
        console.print("Interactive configuration update:")
        
        if Confirm.ask("Update email settings?"):
            email = Prompt.ask("Enter new email address", default=current_config.get('email', ''))
        
        if Confirm.ask("Update schedule settings?"):
            schedule = Prompt.ask(
                "Enter schedule",
                choices=["daily", "weekly", "monthly", "all"],
                default="all"
            )
        
        if Confirm.ask("Update output directory?"):
            output_dir = Prompt.ask("Enter output directory", default=str(current_config.get('output_dir', '')))
    
    # Apply updates
    updates = {}
    if email:
        updates['email'] = email
    if schedule:
        updates['schedule'] = schedule
    if output_dir:
        updates['output_dir'] = Path(output_dir)
    
    if updates:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Updating configuration...", total=None)
            monitor_manager.update_configuration(updates)
            progress.update(task, completed=True)
        
        show_success("Configuration updated successfully!")
        
        # Show updated configuration
        updated_config = monitor_manager.get_configuration()
        console.print("\n📝 Updated Configuration:")
        _show_monitoring_config(updated_config)
    else:
        console.print("No changes made.")


@app.command()
def reports(
    period: str = typer.Option("week", "--period", help="Report period: day, week, month"),
    count: int = typer.Option(10, "--count", help="Number of reports to show"),
    format: str = typer.Option("table", "--format", help="Output format: table, json, list")
):
    """Show recent monitoring reports."""
    monitor_manager = MonitorManager()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Loading reports...", total=None)
        reports_data = monitor_manager.get_recent_reports(period, count)
        progress.update(task, completed=True)
    
    if format == "table":
        _display_reports_table(reports_data)
    elif format == "json":
        console.print_json(data=reports_data)
    elif format == "list":
        _display_reports_list(reports_data)


@app.command()
def logs(
    lines: int = typer.Option(50, "--lines", help="Number of log lines to show"),
    follow: bool = typer.Option(False, "--follow", "-f", help="Follow log output"),
    level: str = typer.Option("info", "--level", help="Log level: debug, info, warning, error")
):
    """Show monitoring logs."""
    monitor_manager = MonitorManager()

    if follow:
        console.print("📋 Following monitoring logs (Ctrl+C to stop)...")
        monitor_manager.follow_logs(level)
    else:
        logs_data = monitor_manager.get_logs(lines, level)
        _display_logs(logs_data)


@app.command()
def daily(
    format: str = typer.Option("table", "--format", help="Output format: table, json, dashboard")
):
    """Run daily security monitoring checks (replaces keyring-monitor.sh)."""
    from system_cli.core.security_monitor import SecurityMonitor

    monitor = SecurityMonitor()

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Running daily security checks...", total=None)
        results = monitor.run_daily_checks()
        progress.update(task, completed=True)

    if format == "table":
        _display_daily_checks_table(results)
    elif format == "json":
        console.print_json(data=results)
    elif format == "dashboard":
        _display_daily_dashboard(results)

    # Show summary
    issues_count = len(results.get("issues", []))
    if issues_count == 0:
        show_success("All daily security checks passed")
    else:
        show_warning(f"Found {issues_count} security issues that need attention")


@app.command()
def dashboard(
    refresh: int = typer.Option(0, "--refresh", help="Auto-refresh interval in seconds (0 = no refresh)"),
    detailed: bool = typer.Option(False, "--detailed", help="Show detailed monitoring information")
):
    """Show real-time security monitoring dashboard."""
    from system_cli.core.security_monitor import SecurityMonitor
    import time

    monitor = SecurityMonitor()

    if refresh > 0:
        console.print(f"🔄 Auto-refreshing every {refresh} seconds (Ctrl+C to stop)...")
        try:
            while True:
                console.clear()
                dashboard_data = monitor.get_dashboard_data(detailed)
                _display_monitoring_dashboard(dashboard_data)
                time.sleep(refresh)
        except KeyboardInterrupt:
            console.print("\n👋 Dashboard stopped")
    else:
        dashboard_data = monitor.get_dashboard_data(detailed)
        _display_monitoring_dashboard(dashboard_data)


@app.command()
def power():
    """Monitor power usage (replaces power-monitor.py)."""
    script_path = Path.home() / "cursor-system" / "scripts" / "power-monitor.py"

    if not script_path.exists():
        show_error(f"Power monitor script not found: {script_path}")
        raise typer.Exit(1)

    console.print("⚡ Power Usage Monitor")
    console.print("=" * 25)

    result = run_command(["python3", str(script_path)], capture_output=False)

    if result.returncode == 0:
        show_success("Power monitoring completed")
    else:
        show_error("Power monitoring failed")
        raise typer.Exit(1)


@app.command()
def email_setup(
    action: str = typer.Argument(..., help="Action: setup, test, status"),
    email: Optional[str] = typer.Option(None, "--email", help="Email address for alerts")
):
    """Configure email alerts (replaces configure-email-alerts.sh)."""
    script_path = Path.home() / "cursor-system" / "scripts" / "configure-email-alerts.sh"

    if not script_path.exists():
        show_error(f"Email configuration script not found: {script_path}")
        raise typer.Exit(1)

    console.print("📧 Email Alerts Configuration")
    console.print("=" * 35)

    cmd = [str(script_path), action]
    if email and action == "setup":
        cmd.extend(["--email", email])

    result = run_command(cmd, capture_output=False)

    if result.returncode == 0:
        show_success(f"Email {action} completed successfully")
    else:
        show_error(f"Email {action} failed")
        raise typer.Exit(1)


@app.command()
def cron_wrapper(
    command: str = typer.Argument(..., help="Command to run with cron wrapper"),
    config_file: Optional[Path] = typer.Option(None, "--config", help="Configuration file path")
):
    """Run command with cron security wrapper (replaces cron-security-wrapper.sh)."""
    script_path = Path.home() / "cursor-system" / "scripts" / "cron-security-wrapper.sh"

    if not script_path.exists():
        show_error(f"Cron wrapper script not found: {script_path}")
        raise typer.Exit(1)

    console.print("🔒 Running command with cron security wrapper...")

    cmd = [str(script_path), command]
    if config_file:
        cmd.extend(["--config", str(config_file)])

    result = run_command(cmd, capture_output=False)

    if result.returncode == 0:
        show_success("Command executed successfully with security wrapper")
    else:
        show_error("Command execution failed")
        raise typer.Exit(1)


def _display_monitoring_status(status_data: dict):
    """Display monitoring status."""
    # Main status panel
    status_text = f"""
Status: {status_data.get('status', 'Unknown')}
Installed: {status_data.get('installed', 'No')}
Email Alerts: {status_data.get('email_enabled', 'No')}
Last Run: {status_data.get('last_run', 'Never')}
Next Run: {status_data.get('next_run', 'Not scheduled')}
"""
    
    status_panel = Panel(
        status_text,
        title="📊 Monitoring Status",
        border_style="green" if status_data.get('status') == 'Active' else "yellow"
    )
    console.print(status_panel)
    
    # Cron jobs table
    if status_data.get('cron_jobs'):
        cron_table = Table(title="⏰ Scheduled Jobs")
        cron_table.add_column("Schedule", style="cyan")
        cron_table.add_column("Command", style="green")
        cron_table.add_column("Status", style="yellow")
        
        for job in status_data['cron_jobs']:
            cron_table.add_row(
                job.get('schedule', ''),
                job.get('command', ''),
                job.get('status', '')
            )
        
        console.print(cron_table)


def _show_monitoring_config(config: dict):
    """Show monitoring configuration."""
    config_text = f"""
Email: {config.get('email', 'Not configured')}
Daily Reports: {config.get('daily_enabled', 'No')}
Weekly Reports: {config.get('weekly_enabled', 'No')}
Monthly Reports: {config.get('monthly_enabled', 'No')}
Output Directory: {config.get('output_dir', 'Default')}
"""
    
    config_panel = Panel(
        config_text,
        title="⚙️ Configuration",
        border_style="blue"
    )
    console.print(config_panel)


def _test_monitoring(monitor_manager):
    """Test monitoring configuration."""
    console.print("🧪 Testing monitoring configuration...")
    
    test_results = monitor_manager.test_configuration("all", True)
    _display_test_results(test_results)


def _display_test_results(results: dict):
    """Display test results."""
    table = Table(title="🧪 Test Results")
    table.add_column("Test", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details", style="white")
    
    for test_name, result in results.items():
        status_icon = "✅" if result.get('passed') else "❌"
        status_text = "PASS" if result.get('passed') else "FAIL"
        
        table.add_row(
            test_name.replace('_', ' ').title(),
            f"{status_icon} {status_text}",
            result.get('details', '')
        )
    
    console.print(table)


def _display_reports_table(reports: list):
    """Display reports in table format."""
    table = Table(title="📊 Recent Reports")
    table.add_column("Date", style="cyan")
    table.add_column("Type", style="green")
    table.add_column("Status", style="yellow")
    table.add_column("Issues", style="red")
    table.add_column("File", style="blue")
    
    for report in reports:
        table.add_row(
            report.get('date', ''),
            report.get('type', ''),
            report.get('status', ''),
            str(report.get('issues_count', 0)),
            report.get('file', '')
        )
    
    console.print(table)


def _display_reports_list(reports: list):
    """Display reports in list format."""
    for report in reports:
        console.print(f"📄 {report.get('date', '')} - {report.get('type', '')} - {report.get('file', '')}")


def _display_logs(logs: list):
    """Display logs."""
    for log_entry in logs:
        timestamp = log_entry.get('timestamp', '')
        level = log_entry.get('level', 'INFO')
        message = log_entry.get('message', '')
        
        level_color = {
            'DEBUG': 'blue',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red'
        }.get(level, 'white')
        
        console.print(f"[{level_color}]{timestamp} [{level}][/{level_color}] {message}")


def _display_daily_checks_table(results: dict):
    """Display daily security checks in table format."""
    console.print("\n🔍 [bold blue]Daily Security Checks[/bold blue]")

    table = Table(title="Security Check Results")
    table.add_column("Check", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details", style="white")
    table.add_column("Action", style="yellow")

    for check in results.get("checks", []):
        status_icon = "✅" if check.get("passed", False) else "❌"
        status_text = f"{status_icon} {'Passed' if check.get('passed', False) else 'Failed'}"

        table.add_row(
            check.get("name", "Unknown"),
            status_text,
            check.get("details", ""),
            check.get("action", "None")
        )

    console.print(table)

    # Summary
    total_checks = len(results.get("checks", []))
    passed_checks = len([c for c in results.get("checks", []) if c.get("passed", False)])

    summary_panel = Panel(
        f"Total Checks: {total_checks}\n"
        f"Passed: {passed_checks}\n"
        f"Failed: {total_checks - passed_checks}",
        title="📊 Summary",
        border_style="green" if passed_checks == total_checks else "yellow"
    )
    console.print(summary_panel)


def _display_daily_dashboard(results: dict):
    """Display daily checks as dashboard."""
    console.print("\n📊 [bold cyan]Daily Security Dashboard[/bold cyan]")

    # Quick status overview
    total_checks = len(results.get("checks", []))
    passed_checks = len([c for c in results.get("checks", []) if c.get("passed", False)])
    success_rate = (passed_checks / total_checks * 100) if total_checks > 0 else 0

    status_color = "green" if success_rate == 100 else "yellow" if success_rate >= 80 else "red"

    overview_panel = Panel(
        f"[bold {status_color}]{success_rate:.1f}% Success Rate[/bold {status_color}]\n"
        f"Checks: {passed_checks}/{total_checks}\n"
        f"Last Run: {results.get('timestamp', 'Unknown')}",
        title="🎯 Overall Status",
        border_style=status_color
    )
    console.print(overview_panel)

    # Issues that need attention
    issues = [c for c in results.get("checks", []) if not c.get("passed", False)]
    if issues:
        console.print("\n🚨 [bold red]Issues Requiring Attention[/bold red]")
        for issue in issues:
            console.print(f"❌ {issue.get('name', 'Unknown')}: {issue.get('details', 'No details')}")
            if issue.get('action'):
                console.print(f"   💡 Action: {issue['action']}")


def _display_monitoring_dashboard(dashboard_data: dict):
    """Display real-time monitoring dashboard."""
    console.print("🛡️ [bold cyan]Security Monitoring Dashboard[/bold cyan]")
    console.print(f"Last Updated: {dashboard_data.get('timestamp', 'Unknown')}")

    # System status
    system_status = dashboard_data.get("system_status", {})
    status_table = Table(title="System Status")
    status_table.add_column("Component", style="cyan")
    status_table.add_column("Status", style="green")
    status_table.add_column("Details", style="white")

    for component, data in system_status.items():
        status_icon = "✅" if data.get("healthy", False) else "❌"
        status_table.add_row(
            component.replace("_", " ").title(),
            f"{status_icon} {data.get('status', 'Unknown')}",
            data.get("details", "")
        )

    console.print(status_table)

    # Recent alerts
    alerts = dashboard_data.get("recent_alerts", [])
    if alerts:
        console.print("\n🚨 [bold yellow]Recent Alerts[/bold yellow]")
        for alert in alerts[-5:]:  # Show last 5 alerts
            alert_time = alert.get("timestamp", "Unknown")
            alert_message = alert.get("message", "No details")
            console.print(f"⚠️ {alert_time}: {alert_message}")

    # Performance metrics
    metrics = dashboard_data.get("metrics", {})
    if metrics:
        metrics_panel = Panel(
            f"CPU Usage: {metrics.get('cpu_usage', 'Unknown')}\n"
            f"Memory Usage: {metrics.get('memory_usage', 'Unknown')}\n"
            f"Disk Usage: {metrics.get('disk_usage', 'Unknown')}\n"
            f"Network Connections: {metrics.get('network_connections', 'Unknown')}",
            title="📈 Performance Metrics",
            border_style="blue"
        )
        console.print(metrics_panel)
