"""
Security audit and monitoring commands.
Converted from security audit scripts.
"""

import typer
from typing import Optional
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.table import Table

from system_cli.core.security_audit import SecurityAuditor
from system_cli.utils.display import show_error, show_success, show_warning
from system_cli.utils.helpers import run_command

app = typer.Typer(help="🔒 Security audit and monitoring")
console = Console()


@app.command()
def audit(
    comprehensive: bool = typer.Option(False, "--comprehensive", help="Run comprehensive system audit"),
    keyring_only: bool = typer.Option(False, "--keyring-only", help="Audit keyring contents only"),
    output_dir: Optional[Path] = typer.Option(None, "--output", help="Output directory for reports"),
    format: str = typer.Option("table", "--format", help="Output format: table, json, yaml, markdown"),
    save_report: bool = typer.Option(True, "--save/--no-save", help="Save detailed report to file")
):
    """Run security audit."""
    auditor = SecurityAuditor(output_dir=output_dir)
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        
        if comprehensive:
            task = progress.add_task("Running comprehensive security audit...", total=None)
            results = auditor.comprehensive_audit()
        elif keyring_only:
            task = progress.add_task("Auditing keyring contents...", total=None)
            results = auditor.keyring_content_audit()
        else:
            task = progress.add_task("Running standard security audit...", total=None)
            results = auditor.standard_audit()
        
        progress.update(task, completed=True)
    
    # Display results based on format
    if format == "table":
        _display_audit_table(results)
    elif format == "json":
        console.print_json(data=results)
    elif format == "yaml":
        import yaml
        console.print(yaml.dump(results, default_flow_style=False))
    elif format == "markdown":
        _display_audit_markdown(results)
    
    if save_report:
        report_path = auditor.save_report(results, format)
        show_success(f"Detailed report saved to: {report_path}")
    
    # Show summary
    _show_audit_summary(results)


@app.command()
def status():
    """Show quick security status summary."""
    auditor = SecurityAuditor()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Checking security status...", total=None)
        status = auditor.get_security_status()
        progress.update(task, completed=True)
    
    # Create status dashboard
    _display_security_dashboard(status)





@app.command()
def content_audit(
    output_dir: Optional[Path] = typer.Option(None, "--output-dir", help="Directory for audit results"),
    format: str = typer.Option("table", "--format", help="Output format: table, json, markdown")
):
    """Run keyring content audit (replaces keyring-content-audit.sh)."""
    from system_cli.core.security_auditor import SecurityAuditor
    auditor = SecurityAuditor(output_dir)

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Running keyring content audit...", total=None)
        results = auditor.keyring_content_audit()
        progress.update(task, completed=True)

    if format == "table":
        _display_content_audit_table(results)
    elif format == "json":
        console.print_json(data=results)
    elif format == "markdown":
        report_path = auditor.save_report(results, "markdown")
        show_success(f"Content audit report saved to: {report_path}")

    show_success("Keyring content audit completed successfully")


@app.command()
def fix_critical(
    firewall: bool = typer.Option(True, "--firewall/--no-firewall", help="Configure firewall"),
    ssh: bool = typer.Option(True, "--ssh/--no-ssh", help="Harden SSH configuration"),
    docker: bool = typer.Option(True, "--docker/--no-docker", help="Secure Docker configuration"),
    fail2ban: bool = typer.Option(True, "--fail2ban/--no-fail2ban", help="Configure Fail2Ban"),
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be fixed without applying")
):
    """Apply critical security fixes (replaces critical-security-fixes.sh)."""
    from system_cli.core.security_fixer import SecurityFixer

    fixer = SecurityFixer()

    if dry_run:
        console.print("🔍 Dry run - showing critical security fixes that would be applied:")
        fixes = fixer.get_critical_fixes_plan(firewall, ssh, docker, fail2ban)
        _display_fixes_plan(fixes)
        return

    console.print("🚨 Applying critical security fixes...")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Applying security fixes...", total=None)
        results = fixer.apply_critical_fixes(firewall, ssh, docker, fail2ban)
        progress.update(task, completed=True)

    _display_fixes_results(results)

    if results.get("success", False):
        show_success("Critical security fixes applied successfully")
    else:
        show_error("Some security fixes failed - check the results above")


@app.command()
def cleanup(
    guest_networks: bool = typer.Option(True, "--guest-networks/--no-guest-networks", help="Remove guest WiFi networks"),
    legacy_keystore: bool = typer.Option(True, "--legacy-keystore/--no-legacy-keystore", help="Handle legacy keystore"),
    unused_connections: bool = typer.Option(True, "--unused-connections/--no-unused-connections", help="Remove unused network connections"),
    dry_run: bool = typer.Option(False, "--dry-run", help="Show what would be cleaned without applying")
):
    """Clean up security issues (replaces security-cleanup.sh)."""
    from system_cli.core.security_cleaner import SecurityCleaner

    cleaner = SecurityCleaner()

    if dry_run:
        console.print("🔍 Dry run - showing security cleanup that would be performed:")
        cleanup_plan = cleaner.get_cleanup_plan(guest_networks, legacy_keystore, unused_connections)
        _display_cleanup_plan(cleanup_plan)
        return

    console.print("🧹 Performing security cleanup...")

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Cleaning up security issues...", total=None)
        results = cleaner.perform_cleanup(guest_networks, legacy_keystore, unused_connections)
        progress.update(task, completed=True)

    _display_cleanup_results(results)

    if results.get("success", False):
        show_success("Security cleanup completed successfully")
    else:
        show_warning("Some cleanup operations had issues - check the results above")


@app.command()
def summary(
    detailed: bool = typer.Option(False, "--detailed", help="Show detailed security metrics"),
    format: str = typer.Option("dashboard", "--format", help="Output format: dashboard, table, json")
):
    """Show security summary dashboard (replaces security-summary.sh)."""
    from system_cli.core.security_dashboard import SecurityDashboard

    dashboard = SecurityDashboard()

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Generating security summary...", total=None)
        summary_data = dashboard.get_security_summary(detailed)
        progress.update(task, completed=True)

    if format == "dashboard":
        _display_security_dashboard(summary_data)
    elif format == "table":
        _display_security_table(summary_data)
    elif format == "json":
        console.print_json(data=summary_data)


@app.command()
def ssh_harden():
    """Harden SSH configuration (replaces fix-ssh-hardening-corrected.sh)."""
    script_path = Path.home() / "cursor-system" / "scripts" / "fix-ssh-hardening-corrected.sh"

    if not script_path.exists():
        show_error(f"SSH hardening script not found: {script_path}")
        raise typer.Exit(1)

    console.print("🔒 Hardening SSH Configuration...")

    result = run_command([str(script_path)], capture_output=False)

    if result.returncode == 0:
        show_success("SSH hardening completed successfully")
    else:
        show_error("SSH hardening failed")
        raise typer.Exit(1)


@app.command()
def auth_audit():
    """Run authentication complexity audit (replaces authentication-complexity-audit.sh)."""
    script_path = Path.home() / "cursor-system" / "scripts" / "authentication-complexity-audit.sh"

    if not script_path.exists():
        show_error(f"Authentication audit script not found: {script_path}")
        raise typer.Exit(1)

    console.print("🔍 Running Authentication Complexity Audit...")

    result = run_command([str(script_path)], capture_output=False)

    if result.returncode == 0:
        show_success("Authentication complexity audit completed successfully")
    else:
        show_error("Authentication complexity audit failed")
        raise typer.Exit(1)


def _display_audit_table(results: dict):
    """Display audit results in table format."""
    # Security Score
    score_panel = Panel(
        f"[bold green]{results.get('security_score', 'N/A')}/10[/bold green]",
        title="🛡️ Security Score",
        border_style="green" if results.get('security_score', 0) >= 8 else "yellow"
    )
    console.print(score_panel)
    console.print()
    
    # Critical Issues
    if results.get('critical_issues'):
        critical_table = Table(title="🚨 Critical Issues", border_style="red")
        critical_table.add_column("Issue", style="red")
        critical_table.add_column("Severity", style="bold red")
        critical_table.add_column("Recommendation", style="yellow")
        
        for issue in results['critical_issues']:
            critical_table.add_row(
                issue.get('description', ''),
                issue.get('severity', ''),
                issue.get('recommendation', '')
            )
        console.print(critical_table)
        console.print()
    
    # Warnings
    if results.get('warnings'):
        warning_table = Table(title="⚠️ Warnings", border_style="yellow")
        warning_table.add_column("Warning", style="yellow")
        warning_table.add_column("Impact", style="orange")
        
        for warning in results['warnings']:
            warning_table.add_row(
                warning.get('description', ''),
                warning.get('impact', '')
            )
        console.print(warning_table)
        console.print()
    
    # Recommendations
    if results.get('recommendations'):
        rec_table = Table(title="💡 Recommendations", border_style="blue")
        rec_table.add_column("Priority", style="blue")
        rec_table.add_column("Action", style="cyan")
        rec_table.add_column("Benefit", style="green")
        
        for rec in results['recommendations']:
            rec_table.add_row(
                rec.get('priority', ''),
                rec.get('action', ''),
                rec.get('benefit', '')
            )
        console.print(rec_table)


def _display_audit_markdown(results: dict):
    """Display audit results in markdown format."""
    markdown = f"""
# Security Audit Report

## Security Score: {results.get('security_score', 'N/A')}/10

## Critical Issues
{_format_issues_markdown(results.get('critical_issues', []))}

## Warnings  
{_format_issues_markdown(results.get('warnings', []))}

## Recommendations
{_format_recommendations_markdown(results.get('recommendations', []))}
"""
    console.print(markdown)


def _show_audit_summary(results: dict):
    """Show audit summary."""
    score = results.get('security_score', 0)
    critical_count = len(results.get('critical_issues', []))
    warning_count = len(results.get('warnings', []))
    
    if score >= 9:
        status_color = "green"
        status_text = "Excellent"
    elif score >= 7:
        status_color = "yellow"
        status_text = "Good"
    else:
        status_color = "red"
        status_text = "Needs Attention"
    
    summary_text = f"""
Security Status: [{status_color}]{status_text}[/{status_color}]
Critical Issues: {critical_count}
Warnings: {warning_count}
"""
    
    summary_panel = Panel(
        summary_text,
        title="📊 Audit Summary",
        border_style=status_color
    )
    console.print(summary_panel)


def _display_security_dashboard(status: dict):
    """Display security status dashboard."""
    # Create main dashboard table
    table = Table(title="🛡️ Security Status Dashboard", show_header=True, header_style="bold blue")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details", style="white")
    
    for component, info in status.items():
        status_icon = "✅" if info.get('status') == 'ok' else "❌" if info.get('status') == 'error' else "⚠️"
        table.add_row(
            component.replace('_', ' ').title(),
            f"{status_icon} {info.get('status', 'unknown').title()}",
            info.get('details', '')
        )
    
    console.print(table)


def _display_cleanup_plan(plan: dict):
    """Display cleanup plan for dry run."""
    table = Table(title="🧹 Cleanup Plan", show_header=True)
    table.add_column("Action", style="cyan")
    table.add_column("Target", style="yellow")
    table.add_column("Reason", style="white")
    
    for action in plan.get('actions', []):
        table.add_row(
            action.get('type', ''),
            action.get('target', ''),
            action.get('reason', '')
        )
    
    console.print(table)


def _display_cleanup_results(results: dict):
    """Display cleanup results."""
    success_count = len(results.get('successful', []))
    failed_count = len(results.get('failed', []))
    
    if success_count > 0:
        show_success(f"Successfully cleaned {success_count} items")
    
    if failed_count > 0:
        show_error(f"Failed to clean {failed_count} items")
        for failure in results.get('failed', []):
            console.print(f"  ❌ {failure}")


def _display_monitoring_status(status: dict):
    """Display monitoring status."""
    monitoring_panel = Panel(
        f"Status: {status.get('status', 'Unknown')}\n"
        f"Last Check: {status.get('last_check', 'Never')}\n"
        f"Next Check: {status.get('next_check', 'Not scheduled')}",
        title="📊 Security Monitoring",
        border_style="blue"
    )
    console.print(monitoring_panel)


def _format_issues_markdown(issues: list) -> str:
    """Format issues for markdown display."""
    if not issues:
        return "None"
    
    formatted = []
    for issue in issues:
        formatted.append(f"- **{issue.get('description', '')}**: {issue.get('recommendation', '')}")
    
    return "\n".join(formatted)


def _format_recommendations_markdown(recommendations: list) -> str:
    """Format recommendations for markdown display."""
    if not recommendations:
        return "None"

    formatted = []
    for rec in recommendations:
        formatted.append(f"- **{rec.get('priority', '')}**: {rec.get('action', '')} - {rec.get('benefit', '')}")

    return "\n".join(formatted)


def _display_content_audit_table(results: dict):
    """Display content audit results in table format."""
    console.print("\n🔍 [bold blue]Keyring Content Audit Results[/bold blue]")

    # Keyring structure
    if "keyring_structure" in results:
        structure = results["keyring_structure"]
        table = Table(title="📁 Keyring Structure")
        table.add_column("Keyring", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Items", style="yellow")
        table.add_column("Size", style="blue")

        for keyring, data in structure.items():
            table.add_row(
                keyring,
                data.get("status", "Unknown"),
                str(data.get("item_count", 0)),
                data.get("size", "Unknown")
            )
        console.print(table)

    # Application usage
    if "application_usage" in results:
        usage = results["application_usage"]
        panel = Panel(
            f"Secure Apps: {len(usage.get('secure_apps', []))}\n"
            f"Convenience Apps: {len(usage.get('convenience_apps', []))}\n"
            f"Active Apps: {len(usage.get('active_apps', []))}",
            title="📱 Application Usage",
            border_style="blue"
        )
        console.print(panel)


def _display_fixes_plan(fixes: dict):
    """Display security fixes plan."""
    console.print("\n🔧 [bold yellow]Critical Security Fixes Plan[/bold yellow]")

    table = Table(title="Planned Security Fixes")
    table.add_column("Component", style="cyan")
    table.add_column("Action", style="yellow")
    table.add_column("Impact", style="green")
    table.add_column("Risk", style="red")

    for fix in fixes.get("planned_fixes", []):
        table.add_row(
            fix.get("component", "Unknown"),
            fix.get("action", "Unknown"),
            fix.get("impact", "Unknown"),
            fix.get("risk", "Low")
        )

    console.print(table)


def _display_fixes_results(results: dict):
    """Display security fixes results."""
    console.print("\n✅ [bold green]Security Fixes Results[/bold green]")

    for fix in results.get("applied_fixes", []):
        status = "✅" if fix.get("success", False) else "❌"
        console.print(f"{status} {fix.get('component', 'Unknown')}: {fix.get('message', 'No details')}")

    if results.get("warnings"):
        console.print("\n⚠️ [bold yellow]Warnings:[/bold yellow]")
        for warning in results["warnings"]:
            console.print(f"  • {warning}")


def _display_security_table(summary: dict):
    """Display security summary in table format."""
    table = Table(title="Security Summary")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    table.add_column("Status", style="yellow")

    table.add_row("Overall Score", f"{summary.get('overall_score', 0)}/10", "Good" if summary.get('overall_score', 0) >= 7 else "Needs Attention")
    table.add_row("Components Checked", str(len(summary.get('components', {}))), "Complete")
    table.add_row("Action Items", str(len(summary.get('action_items', []))), "Pending" if summary.get('action_items') else "None")

    console.print(table)


def _display_cleanup_plan(plan: dict):
    """Display cleanup plan."""
    console.print("\n🧹 [bold yellow]Security Cleanup Plan[/bold yellow]")

    table = Table(title="Planned Cleanup Actions")
    table.add_column("Category", style="cyan")
    table.add_column("Items to Remove", style="yellow")
    table.add_column("Impact", style="green")

    for category, items in plan.get("cleanup_items", {}).items():
        table.add_row(
            category.replace("_", " ").title(),
            str(len(items)) if isinstance(items, list) else str(items),
            plan.get("impacts", {}).get(category, "Low")
        )

    console.print(table)


def _display_security_dashboard(summary: dict):
    """Display security dashboard."""
    console.print("\n🛡️ [bold cyan]Security Dashboard[/bold cyan]")

    # Overall score
    score = summary.get("overall_score", 0)
    score_color = "green" if score >= 8 else "yellow" if score >= 6 else "red"
    score_panel = Panel(
        f"[bold {score_color}]{score}/10[/bold {score_color}]",
        title="🎯 Overall Security Score",
        border_style=score_color
    )
    console.print(score_panel)

    # Component status
    table = Table(title="📊 Security Component Status")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Score", style="yellow")
    table.add_column("Notes", style="blue")

    for component, data in summary.get("components", {}).items():
        status_icon = "✅" if data.get("status") == "excellent" else "⚠️" if data.get("status") == "good" else "❌"
        table.add_row(
            component.replace("_", " ").title(),
            f"{status_icon} {data.get('status', 'Unknown').title()}",
            f"{data.get('score', 0)}/10",
            data.get("notes", "")
        )

    console.print(table)

    # Action items
    if summary.get("action_items"):
        console.print("\n🚨 [bold red]Action Items[/bold red]")
        for item in summary["action_items"]:
            priority = item.get("priority", "medium")
            priority_color = "red" if priority == "high" else "yellow" if priority == "medium" else "blue"
            console.print(f"[{priority_color}]• {item.get('description', 'Unknown action')}[/{priority_color}]")
