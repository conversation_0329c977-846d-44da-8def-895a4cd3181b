#!/usr/bin/env python3
"""
System Configuration Audit Command

Comprehensive audit of /etc/ configuration files and system settings.
"""

import subprocess
import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

console = Console()

def run_command(cmd: str, capture_output: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result."""
    try:
        return subprocess.run(
            cmd, 
            shell=True, 
            capture_output=capture_output, 
            text=True,
            check=False
        )
    except Exception as e:
        console.print(f"[red]Error running command '{cmd}': {e}[/red]")
        return subprocess.CompletedProcess(cmd, 1, "", str(e))

def check_firewall_status() -> Dict[str, Any]:
    """Check UFW firewall status."""
    result = run_command("sudo ufw status verbose")
    
    status = {
        "enabled": "Status: active" in result.stdout,
        "rules": [],
        "default_policy": "unknown"
    }
    
    if status["enabled"]:
        lines = result.stdout.split('\n')
        for line in lines:
            if "Default:" in line:
                status["default_policy"] = line.strip()
            elif " ALLOW " in line or " DENY " in line:
                status["rules"].append(line.strip())
    
    return status

def check_apparmor_status() -> Dict[str, Any]:
    """Check AppArmor status."""
    result = run_command("sudo aa-status")
    
    status = {
        "loaded": False,
        "enforced": 0,
        "complain": 0,
        "unconfined": 0,
        "total_profiles": 0
    }
    
    if result.returncode == 0:
        lines = result.stdout.split('\n')
        for line in lines:
            if "profiles are loaded" in line:
                status["total_profiles"] = int(line.split()[0])
                status["loaded"] = True
            elif "profiles are in enforce mode" in line:
                status["enforced"] = int(line.split()[0])
            elif "profiles are in complain mode" in line:
                status["complain"] = int(line.split()[0])
            elif "profiles are in unconfined mode" in line:
                status["unconfined"] = int(line.split()[0])
    
    return status

def check_ssh_hardening() -> Dict[str, Any]:
    """Check SSH hardening configuration."""
    ssh_config_path = Path("/etc/ssh/sshd_config.d/99-security-hardening.conf")
    
    status = {
        "hardened": ssh_config_path.exists(),
        "settings": {}
    }
    
    if status["hardened"]:
        try:
            with open(ssh_config_path, 'r') as f:
                content = f.read()
                for line in content.split('\n'):
                    if line.strip() and not line.startswith('#'):
                        if ' ' in line:
                            key, value = line.split(' ', 1)
                            status["settings"][key] = value
        except Exception as e:
            console.print(f"[yellow]Warning: Could not read SSH config: {e}[/yellow]")
    
    return status

def check_power_management() -> Dict[str, Any]:
    """Check power management configuration."""
    tlp_status = run_command("systemctl is-active tlp")
    powertop_status = run_command("systemctl is-active powertop")
    tuned_status = run_command("systemctl is-active tuned")
    
    return {
        "tlp": tlp_status.stdout.strip(),
        "powertop": powertop_status.stdout.strip(),
        "tuned": tuned_status.stdout.strip() if tuned_status.returncode == 0 else "not-installed"
    }

def check_service_status() -> Dict[str, Any]:
    """Check status of key services."""
    services = ["cups", "ModemManager", "bluetooth", "avahi-daemon"]
    status = {}
    
    for service in services:
        result = run_command(f"systemctl is-enabled {service}")
        status[service] = result.stdout.strip()
    
    return status

def find_recent_config_changes(days: int = 30) -> List[Dict[str, Any]]:
    """Find configuration files modified in the last N days."""
    result = run_command(f"find /etc -name '*.conf' -mtime -{days} -type f 2>/dev/null")
    
    changes = []
    if result.returncode == 0:
        for file_path in result.stdout.strip().split('\n'):
            if file_path:
                try:
                    stat = os.stat(file_path)
                    changes.append({
                        "path": file_path,
                        "modified": datetime.fromtimestamp(stat.st_mtime),
                        "size": stat.st_size
                    })
                except (OSError, FileNotFoundError):
                    continue
    
    return sorted(changes, key=lambda x: x["modified"], reverse=True)

def find_empty_config_files() -> List[str]:
    """Find empty configuration files that might be cleaned up."""
    result = run_command("find /etc -name '*.conf' -size 0 -type f 2>/dev/null")
    
    if result.returncode == 0:
        return [f for f in result.stdout.strip().split('\n') if f]
    return []

def audit_config() -> None:
    """Run comprehensive configuration audit."""
    console.print("\n[bold blue]🔍 System Configuration Audit[/bold blue]")
    console.print(f"[dim]Audit Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}[/dim]\n")
    
    # Security Status
    console.print("[bold green]🛡️  Security Configuration[/bold green]")
    
    # Firewall
    fw_status = check_firewall_status()
    fw_color = "green" if fw_status["enabled"] else "red"
    console.print(f"  UFW Firewall: [{fw_color}]{'✅ Active' if fw_status['enabled'] else '❌ Inactive'}[/{fw_color}]")
    if fw_status["enabled"]:
        console.print(f"    {fw_status['default_policy']}")
        console.print(f"    Rules: {len(fw_status['rules'])} active")
    
    # AppArmor
    aa_status = check_apparmor_status()
    if aa_status["loaded"]:
        console.print(f"  AppArmor: [green]✅ Active[/green]")
        console.print(f"    Enforced: {aa_status['enforced']}, Complain: {aa_status['complain']}, Unconfined: {aa_status['unconfined']}")
    else:
        console.print(f"  AppArmor: [red]❌ Not loaded[/red]")
    
    # SSH
    ssh_status = check_ssh_hardening()
    ssh_color = "green" if ssh_status["hardened"] else "yellow"
    console.print(f"  SSH Hardening: [{ssh_color}]{'✅ Configured' if ssh_status['hardened'] else '⚠️  Not configured'}[/{ssh_color}]")
    
    console.print()
    
    # Power Management
    console.print("[bold blue]⚡ Power Management[/bold blue]")
    pm_status = check_power_management()
    
    for service, status in pm_status.items():
        if status == "active":
            color = "green"
            icon = "✅"
        elif status == "inactive":
            color = "yellow"
            icon = "⏸️"
        else:
            color = "dim"
            icon = "➖"
        
        console.print(f"  {service.upper()}: [{color}]{icon} {status}[/{color}]")
    
    console.print()
    
    # Service Status
    console.print("[bold cyan]🔧 Service Management[/bold cyan]")
    svc_status = check_service_status()
    
    for service, status in svc_status.items():
        if status == "enabled":
            color = "green"
            icon = "✅"
        elif status == "disabled":
            color = "yellow"
            icon = "⏸️"
        elif status == "masked":
            color = "blue"
            icon = "🚫"
        else:
            color = "dim"
            icon = "❓"
        
        console.print(f"  {service}: [{color}]{icon} {status}[/{color}]")
    
    console.print()
    
    # Recent Changes
    console.print("[bold magenta]📝 Recent Configuration Changes[/bold magenta]")
    recent_changes = find_recent_config_changes(30)
    
    if recent_changes:
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("File", style="cyan")
        table.add_column("Modified", style="yellow")
        table.add_column("Size", style="green")
        
        for change in recent_changes[:10]:  # Show top 10
            table.add_row(
                change["path"],
                change["modified"].strftime("%Y-%m-%d %H:%M"),
                f"{change['size']} bytes"
            )
        
        console.print(table)
        
        if len(recent_changes) > 10:
            console.print(f"[dim]... and {len(recent_changes) - 10} more files[/dim]")
    else:
        console.print("  [dim]No recent configuration changes found[/dim]")
    
    console.print()
    
    # Cleanup Opportunities
    console.print("[bold yellow]🧹 Cleanup Opportunities[/bold yellow]")
    empty_files = find_empty_config_files()
    
    if empty_files:
        console.print("  Empty configuration files:")
        for file_path in empty_files:
            console.print(f"    [dim]{file_path}[/dim]")
    else:
        console.print("  [green]✅ No empty configuration files found[/green]")
    
    # Summary
    console.print()
    console.print("[bold green]📊 Audit Summary[/bold green]")
    
    security_score = 0
    if fw_status["enabled"]:
        security_score += 3
    if aa_status["loaded"]:
        security_score += 3
    if ssh_status["hardened"]:
        security_score += 2
    if pm_status["tlp"] == "active":
        security_score += 1
    if svc_status.get("cups") == "masked":
        security_score += 1
    
    total_score = security_score
    max_score = 10
    
    score_color = "green" if total_score >= 8 else "yellow" if total_score >= 6 else "red"
    console.print(f"  Overall Configuration Score: [{score_color}]{total_score}/{max_score}[/{score_color}]")
    
    if total_score >= 8:
        console.print("  [green]✅ Excellent configuration state[/green]")
    elif total_score >= 6:
        console.print("  [yellow]⚠️  Good configuration with room for improvement[/yellow]")
    else:
        console.print("  [red]❌ Configuration needs attention[/red]")

if __name__ == "__main__":
    audit_config()
