[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "system-cli"
version = "1.0.0"
description = "Comprehensive Linux system administration CLI"
readme = "SYSTEM_CLI_README.md"
license = {text = "MIT"}
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
maintainers = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
keywords = ["linux", "system", "administration", "cli", "keyring", "security", "monitoring", "systemd", "networking"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: System Administrators",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: POSIX :: Linux",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: System :: Systems Administration",
    "Topic :: Security",
    "Topic :: Utilities",
    "Environment :: Console",
    "Typing :: Typed",
]
requires-python = ">=3.8"
dependencies = [
    # Core CLI framework
    "typer>=0.9.0",
    "rich>=13.0.0",
    "click>=8.0.0",
    
    # Configuration management
    "pydantic>=2.0.0",
    "pyyaml>=6.0",
    "toml>=0.10.2",
    
    # Interactive prompts and UI
    "questionary>=1.10.0",
]

[project.optional-dependencies]
# System monitoring dependencies
monitoring = [
    "psutil>=5.9.0",
]

# Development dependencies
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "isort>=5.12.0",
    "pre-commit>=3.0.0",
]

# Documentation dependencies
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.0.0",
    "mkdocs-click>=0.8.0",
    "mkdocstrings[python]>=0.20.0",
]

# Testing dependencies
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "pytest-asyncio>=0.21.0",
    "coverage>=7.0.0",
]

# Linting and formatting
lint = [
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "isort>=5.12.0",
    "bandit>=1.7.0",
    "safety>=2.3.0",
]

# All development dependencies
all = [
    "system-cli[monitoring,dev,docs,test,lint]",
]

[project.urls]
Homepage = "https://github.com/bcherrington/cursor-system"
Documentation = "https://github.com/bcherrington/cursor-system/blob/main/KEYRING_CLI_README.md"
Repository = "https://github.com/bcherrington/cursor-system.git"
"Bug Tracker" = "https://github.com/bcherrington/cursor-system/issues"
Changelog = "https://github.com/bcherrington/cursor-system/blob/main/CHANGELOG.md"

[project.scripts]
system-cli = "system_cli.main:app"

[project.entry-points."console_scripts"]
system-cli = "system_cli.main:app"

[tool.setuptools]
package-dir = {"" = "src"}
packages = ["system_cli"]

[tool.setuptools.package-data]
system_cli = [
    "config/*.yaml",
    "config/*.toml",
    "config/templates/*.j2",
    "resources/templates/*.txt",
    "resources/scripts/*.sh",
    "py.typed",
]

# Black configuration
[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# MyPy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "questionary.*",
    "psutil.*",
    "yaml.*",
    "toml.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=system_cli",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage configuration
[tool.coverage.run]
source = ["system_cli"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Bandit security linting configuration
[tool.bandit]
exclude_dirs = ["tests", "venv", "env"]
skips = ["B101", "B601"]

# Flake8 configuration (in setup.cfg since flake8 doesn't support pyproject.toml yet)
# But we can document the intended configuration here:
# [tool.flake8]
# max-line-length = 100
# extend-ignore = ["E203", "W503"]
# exclude = [".git", "__pycache__", "build", "dist", ".venv", "venv"]

[tool.ruff]
line-length = 100
target-version = "py38"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/*" = ["B011"]

[tool.ruff.isort]
known-first-party = ["system_cli"]

# Semantic versioning and changelog
[tool.semantic_release]
version_variable = "system_cli/__init__.py:__version__"
version_pattern = "pyproject.toml:version = \"{version}\""
build_command = "pip install build && python -m build"
