#!/usr/bin/env python3
"""
Development CLI for System CLI project.
Replaces the Makefile with a more powerful and user-friendly Python CLI.
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path
from typing import List, Optional

import typer
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

# Initialize Rich console and Typer app
console = Console()
app = typer.Typer(
    name="dev",
    help="Development CLI for System CLI project",
    rich_markup_mode="rich",
    add_completion=False,
)

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent  # Go up one level from scripts/
SRC_DIR = PROJECT_ROOT / "src"
TESTS_DIR = PROJECT_ROOT / "tests"
DOCS_DIR = PROJECT_ROOT / "docs"


def run_command(
    cmd: List[str],
    description: str,
    cwd: Optional[Path] = None,
    check: bool = True,
    capture_output: bool = False,
) -> subprocess.CompletedProcess:
    """Run a command with rich output and error handling."""
    if cwd is None:
        cwd = PROJECT_ROOT
    
    console.print(f"[blue]Running:[/blue] {description}")
    console.print(f"[dim]Command: {' '.join(cmd)}[/dim]")
    
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd,
            check=check,
            capture_output=capture_output,
            text=True,
        )
        if not capture_output:
            console.print(f"[green]✅ {description} completed successfully[/green]")
        return result
    except subprocess.CalledProcessError as e:
        console.print(f"[red]❌ {description} failed with exit code {e.returncode}[/red]")
        if capture_output and e.stdout:
            console.print(f"[red]stdout:[/red] {e.stdout}")
        if capture_output and e.stderr:
            console.print(f"[red]stderr:[/red] {e.stderr}")
        raise typer.Exit(1)


def check_command_exists(command: str) -> bool:
    """Check if a command exists in PATH."""
    return shutil.which(command) is not None


# Setup and Installation Commands
install_app = typer.Typer(name="install", help="Setup and installation commands")
app.add_typer(install_app, name="install")


@install_app.command("base")
def install_base():
    """Install the package in development mode."""
    run_command(["pip", "install", "-e", "."], "Installing package in development mode")


@install_app.command("dev")
def install_dev():
    """Install with all development dependencies."""
    run_command(["pip", "install", "-e", ".[dev]"], "Installing with development dependencies")


@install_app.command("all")
def install_all():
    """Install with all optional dependencies."""
    run_command(["pip", "install", "-e", ".[all]"], "Installing with all dependencies")


# Testing Commands
test_app = typer.Typer(name="test", help="Testing commands")
app.add_typer(test_app, name="test")


@test_app.command("all")
def test_all():
    """Run tests with coverage."""
    run_command(
        ["pytest", "--cov=system_cli", "--cov-report=term-missing", "--cov-report=html"],
        "Running tests with coverage"
    )


@test_app.command("fast")
def test_fast():
    """Run tests without coverage."""
    run_command(["pytest", "-x", "--no-cov"], "Running fast tests")


@test_app.command("unit")
def test_unit():
    """Run only unit tests."""
    run_command(["pytest", "tests/unit/", "--no-cov"], "Running unit tests")


@test_app.command("integration")
def test_integration():
    """Run only integration tests."""
    run_command(["pytest", "tests/integration/", "--no-cov"], "Running integration tests")


@test_app.command("e2e")
def test_e2e():
    """Run only end-to-end tests."""
    run_command(["pytest", "tests/e2e/", "--no-cov"], "Running end-to-end tests")


# Linting and Formatting Commands
lint_app = typer.Typer(name="lint", help="Linting and formatting commands")
app.add_typer(lint_app, name="lint")


@lint_app.command("all")
def lint_all():
    """Run all linting tools."""
    format_code()
    type_check()
    security_check()
    run_command(["flake8", "src/system_cli", "tests"], "Running flake8")
    console.print("[green]✅ All linting checks passed![/green]")


@lint_app.command("format")
def format_code():
    """Format code with black and isort."""
    run_command(["black", "src/system_cli", "tests"], "Formatting with black")
    run_command(["isort", "src/system_cli", "tests"], "Sorting imports with isort")
    console.print("[green]✅ Code formatted with black and isort[/green]")


@lint_app.command("type-check")
def type_check():
    """Run mypy type checking."""
    run_command(["mypy", "src/system_cli"], "Running type checking")
    console.print("[green]✅ Type checking passed[/green]")


@lint_app.command("security")
def security_check():
    """Run security checks with bandit and safety."""
    run_command(["bandit", "-r", "src/system_cli"], "Running bandit security check")
    run_command(["safety", "check"], "Running safety dependency check")
    console.print("[green]✅ Security checks passed[/green]")


# Build Commands
build_app = typer.Typer(name="build", help="Build and distribution commands")
app.add_typer(build_app, name="build")


@build_app.command("dist")
def build_dist():
    """Build distribution packages."""
    clean_build()
    run_command(["python3", "-m", "build"], "Building distribution packages")


@build_app.command("clean")
def clean_build():
    """Clean build artifacts."""
    dirs_to_clean = [
        "build", "dist", "*.egg-info", ".pytest_cache",
        ".mypy_cache", "htmlcov"
    ]

    for pattern in dirs_to_clean:
        for path in PROJECT_ROOT.glob(pattern):
            if path.is_dir():
                shutil.rmtree(path)
                console.print(f"[yellow]Removed directory: {path}[/yellow]")

    # Clean __pycache__ directories
    for pycache in PROJECT_ROOT.rglob("__pycache__"):
        shutil.rmtree(pycache)

    # Clean .pyc files
    for pyc_file in PROJECT_ROOT.rglob("*.pyc"):
        pyc_file.unlink()

    console.print("[green]✅ Cleaned build artifacts[/green]")


@build_app.command("check")
def check_dist():
    """Check package metadata."""
    run_command(["python3", "-m", "twine", "check", "dist/*"], "Checking package metadata")


# Documentation Commands
docs_app = typer.Typer(name="docs", help="Documentation commands")
app.add_typer(docs_app, name="docs")


@docs_app.command("build")
def docs_build(
    clean: bool = typer.Option(False, "--clean", help="Clean site directory before building"),
    strict: bool = typer.Option(False, "--strict", help="Enable strict mode (warnings as errors)"),
):
    """Build documentation with MkDocs."""
    if not check_command_exists("mkdocs"):
        console.print("[red]❌ MkDocs not installed. Install with: pip install -e .[docs][/red]")
        raise typer.Exit(1)

    cmd = ["mkdocs", "build"]
    if clean:
        cmd.append("--clean")
    if strict:
        cmd.append("--strict")

    run_command(cmd, "Building documentation")
    console.print("[green]✅ Documentation built successfully in 'site/' directory[/green]")


@docs_app.command("serve")
def docs_serve(
    port: int = typer.Option(8000, "--port", "-p", help="Port to serve on"),
    host: str = typer.Option("127.0.0.1", "--host", "-h", help="Host to serve on"),
    dev_addr: Optional[str] = typer.Option(None, "--dev-addr", help="Development server address (host:port)"),
    livereload: bool = typer.Option(True, "--livereload/--no-livereload", help="Enable live reloading"),
):
    """Serve documentation locally with live reloading."""
    if not check_command_exists("mkdocs"):
        console.print("[red]❌ MkDocs not installed. Install with: pip install -e .[docs][/red]")
        raise typer.Exit(1)

    cmd = ["mkdocs", "serve"]

    if dev_addr:
        cmd.extend(["--dev-addr", dev_addr])
    else:
        cmd.extend(["--dev-addr", f"{host}:{port}"])

    if not livereload:
        cmd.append("--no-livereload")

    console.print(f"[blue]Starting documentation server on http://{host}:{port}[/blue]")
    console.print("[dim]Press Ctrl+C to stop the server[/dim]")
    console.print("[dim]The server will automatically reload when you make changes to documentation files[/dim]")

    try:
        run_command(cmd, "Serving documentation")
    except KeyboardInterrupt:
        console.print("\n[yellow]Documentation server stopped[/yellow]")


@docs_app.command("deploy")
def docs_deploy(
    force: bool = typer.Option(False, "--force", help="Force push to gh-pages branch"),
    message: Optional[str] = typer.Option(None, "--message", "-m", help="Commit message for deployment"),
):
    """Deploy documentation to GitHub Pages."""
    if not check_command_exists("mkdocs"):
        console.print("[red]❌ MkDocs not installed. Install with: pip install -e .[docs][/red]")
        raise typer.Exit(1)

    cmd = ["mkdocs", "gh-deploy"]

    if force:
        cmd.append("--force")
    if message:
        cmd.extend(["--message", message])

    console.print("[blue]Deploying documentation to GitHub Pages...[/blue]")
    console.print("[dim]This will build and push to the gh-pages branch[/dim]")

    run_command(cmd, "Deploying documentation")
    console.print("[green]✅ Documentation deployed to GitHub Pages[/green]")


@docs_app.command("clean")
def docs_clean():
    """Clean built documentation files."""
    site_dir = PROJECT_ROOT / "site"
    if site_dir.exists():
        shutil.rmtree(site_dir)
        console.print("[green]✅ Cleaned documentation build directory[/green]")
    else:
        console.print("[yellow]⚠️  No build directory to clean[/yellow]")


@docs_app.command("check")
def docs_check():
    """Check documentation for issues."""
    if not check_command_exists("mkdocs"):
        console.print("[red]❌ MkDocs not installed. Install with: pip install -e .[docs][/red]")
        raise typer.Exit(1)

    console.print("[blue]Checking documentation configuration and links...[/blue]")

    # Build in strict mode to catch warnings
    try:
        run_command(["mkdocs", "build", "--strict", "--clean"], "Checking documentation", capture_output=True)
        console.print("[green]✅ Documentation check passed[/green]")
    except typer.Exit:
        console.print("[red]❌ Documentation check failed - see warnings above[/red]")
        raise


@docs_app.command("new")
def docs_new(
    page_name: str = typer.Argument(..., help="Name of the new documentation page"),
    section: Optional[str] = typer.Option(None, "--section", "-s", help="Section/directory for the page"),
):
    """Create a new documentation page."""
    docs_dir = PROJECT_ROOT / "docs"

    if section:
        page_dir = docs_dir / section
        page_dir.mkdir(exist_ok=True)
        page_path = page_dir / f"{page_name}.md"
    else:
        page_path = docs_dir / f"{page_name}.md"

    if page_path.exists():
        console.print(f"[red]❌ Page already exists: {page_path}[/red]")
        raise typer.Exit(1)

    # Create basic page template
    template = f"""# {page_name.replace('-', ' ').replace('_', ' ').title()}

## Overview

Add your content here.

## Examples

```bash
# Add code examples
```

## See Also

- [Home](index.md)
"""

    page_path.write_text(template)
    console.print(f"[green]✅ Created new documentation page: {page_path}[/green]")
    console.print(f"[dim]Don't forget to add it to the navigation in mkdocs.yml[/dim]")


@docs_app.command("validate")
def docs_validate():
    """Validate MkDocs configuration and dependencies."""
    console.print("[blue]Validating MkDocs setup...[/blue]")

    # Check if mkdocs.yml exists
    mkdocs_config = PROJECT_ROOT / "mkdocs.yml"
    if not mkdocs_config.exists():
        console.print("[red]❌ mkdocs.yml not found in project root[/red]")
        raise typer.Exit(1)

    # Check if docs directory exists
    docs_dir = PROJECT_ROOT / "docs"
    if not docs_dir.exists():
        console.print("[red]❌ docs/ directory not found[/red]")
        raise typer.Exit(1)

    # Check if index.md exists
    index_file = docs_dir / "index.md"
    if not index_file.exists():
        console.print("[red]❌ docs/index.md not found[/red]")
        raise typer.Exit(1)

    # Check MkDocs installation
    if not check_command_exists("mkdocs"):
        console.print("[red]❌ MkDocs not installed[/red]")
        raise typer.Exit(1)

    # Validate configuration
    try:
        result = subprocess.run(
            ["mkdocs", "build", "--clean", "--strict"],
            capture_output=True,
            text=True,
            cwd=PROJECT_ROOT
        )
        if result.returncode == 0:
            console.print("[green]✅ MkDocs configuration is valid[/green]")
        else:
            console.print("[red]❌ MkDocs configuration has errors:[/red]")
            console.print(result.stderr)
            raise typer.Exit(1)
    except subprocess.CalledProcessError as e:
        console.print(f"[red]❌ Error validating MkDocs: {e}[/red]")
        raise typer.Exit(1)


# CLI Testing Commands
cli_app = typer.Typer(name="cli", help="CLI testing commands")
app.add_typer(cli_app, name="cli")


@cli_app.command("test")
def cli_test():
    """Test CLI installation and basic commands."""
    console.print("[blue]Testing CLI installation...[/blue]")

    commands = [
        (["system-cli", "--help"], "CLI help"),
        (["system-cli", "version"], "CLI version"),
        (["system-cli", "test", "dependencies"], "CLI dependency test"),
    ]

    for cmd, desc in commands:
        try:
            run_command(cmd, desc, capture_output=True)
        except typer.Exit:
            console.print(f"[red]❌ CLI test failed: {desc}[/red]")
            return

    console.print("[green]✅ CLI basic tests passed[/green]")


@cli_app.command("demo")
def cli_demo():
    """Run CLI demo commands."""
    console.print("[blue]Running CLI demo...[/blue]")

    demo_commands = [
        (["system-cli", "version"], "Showing version"),
        (["system-cli", "test", "dependencies"], "Checking dependencies"),
        (["system-cli", "status"], "Showing system status"),
        (["system-cli", "test", "configuration"], "Testing configuration"),
    ]

    for i, (cmd, desc) in enumerate(demo_commands, 1):
        console.print(f"\n[cyan]{i}. {desc}:[/cyan]")
        try:
            run_command(cmd, desc)
        except typer.Exit:
            console.print(f"[yellow]⚠️  Demo command failed: {desc}[/yellow]")

    console.print("\n[green]✅ CLI demo completed[/green]")


# Development Workflow Commands
@app.command("dev-setup")
def dev_setup():
    """Set up development environment."""
    console.print("[blue]Setting up development environment...[/blue]")

    # Install development dependencies
    install_dev()

    # Install pre-commit hooks
    if check_command_exists("pre-commit"):
        run_command(["pre-commit", "install"], "Installing pre-commit hooks")

    # Validate documentation setup
    console.print("[blue]Validating documentation setup...[/blue]")
    try:
        docs_validate()
    except typer.Exit:
        console.print("[yellow]⚠️  Documentation setup needs attention[/yellow]")

    console.print("[green]✅ Development environment set up[/green]")
    console.print("\n[bold]Next steps:[/bold]")
    console.print("  [cyan]dev.py docs serve[/cyan]     Start documentation server")
    console.print("  [cyan]dev.py test all[/cyan]       Run test suite")
    console.print("  [cyan]dev.py lint all[/cyan]       Run linting tools")


@app.command("pre-commit")
def run_pre_commit():
    """Run pre-commit on all files."""
    run_command(["pre-commit", "run", "--all-files"], "Running pre-commit")


# Version Management Commands
version_app = typer.Typer(name="version", help="Version management commands")
app.add_typer(version_app, name="version")


@version_app.command("patch")
def version_patch():
    """Bump patch version."""
    if check_command_exists("bump2version"):
        run_command(["bump2version", "patch"], "Bumping patch version")
    else:
        console.print("[red]❌ bump2version not installed[/red]")


@version_app.command("minor")
def version_minor():
    """Bump minor version."""
    if check_command_exists("bump2version"):
        run_command(["bump2version", "minor"], "Bumping minor version")
    else:
        console.print("[red]❌ bump2version not installed[/red]")


@version_app.command("major")
def version_major():
    """Bump major version."""
    if check_command_exists("bump2version"):
        run_command(["bump2version", "major"], "Bumping major version")
    else:
        console.print("[red]❌ bump2version not installed[/red]")


# Utility Commands
deps_app = typer.Typer(name="deps", help="Dependency management commands")
app.add_typer(deps_app, name="deps")


@deps_app.command("update")
def deps_update():
    """Update dependencies."""
    run_command(["pip", "install", "--upgrade", "pip", "setuptools", "wheel"], "Updating core tools")
    run_command(["pip", "install", "--upgrade", "-e", ".[all]"], "Updating all dependencies")


@deps_app.command("check")
def deps_check():
    """Check dependencies for issues."""
    run_command(["pip", "check"], "Checking dependency compatibility")
    run_command(["safety", "check"], "Checking for security vulnerabilities")


@deps_app.command("requirements")
def generate_requirements():
    """Generate requirements files."""
    if check_command_exists("pip-compile"):
        run_command(
            ["pip-compile", "pyproject.toml", "--output-file", "requirements.txt"],
            "Generating requirements.txt"
        )
        run_command(
            ["pip-compile", "pyproject.toml", "--extra", "dev", "--output-file", "requirements-dev.txt"],
            "Generating requirements-dev.txt"
        )
    else:
        console.print("[red]❌ pip-tools not installed. Install with: pip install pip-tools[/red]")


# Quality Assurance Commands
@app.command("qa")
def quality_assurance():
    """Run all quality assurance checks."""
    console.print("[blue]Running comprehensive quality assurance...[/blue]")

    try:
        lint_all()
        test_all()
        security_check()
        console.print("[green]✅ All quality assurance checks passed![/green]")
    except typer.Exit:
        console.print("[red]❌ Quality assurance checks failed[/red]")
        raise


@app.command("dev-cycle")
def dev_cycle(
    skip_docs: bool = typer.Option(False, "--skip-docs", help="Skip documentation build"),
):
    """Run full development cycle."""
    console.print("[blue]Running full development cycle...[/blue]")

    try:
        format_code()
        lint_all()
        test_all()

        if not skip_docs:
            console.print("[blue]Building documentation...[/blue]")
            docs_build(clean=True, strict=True)

        build_dist()
        console.print("[green]✅ Full development cycle completed![/green]")

        if not skip_docs:
            console.print("[dim]Documentation available at: site/index.html[/dim]")

    except typer.Exit:
        console.print("[red]❌ Development cycle failed[/red]")
        raise


# Environment and System Info Commands
@app.command("env-info")
def env_info():
    """Show environment information."""
    console.print("[blue]Environment Information[/blue]")

    table = Table(title="Environment Details")
    table.add_column("Component", style="cyan")
    table.add_column("Value", style="green")

    # Python version
    python_version = subprocess.run(
        ["python3", "--version"], capture_output=True, text=True
    ).stdout.strip()
    table.add_row("Python Version", python_version)

    # Pip version
    pip_version = subprocess.run(
        ["pip3", "--version"], capture_output=True, text=True
    ).stdout.strip()
    table.add_row("Pip Version", pip_version)

    # Python path
    python_path = subprocess.run(
        ["which", "python3"], capture_output=True, text=True
    ).stdout.strip()
    table.add_row("Python Path", python_path)

    # Virtual environment
    venv = os.environ.get("VIRTUAL_ENV", "Not in virtual environment")
    table.add_row("Virtual Environment", venv)

    console.print(table)

    # Show key packages
    console.print("\n[blue]Key Packages:[/blue]")
    try:
        result = subprocess.run(
            ["pip", "list"], capture_output=True, text=True, check=True
        )
        key_packages = ["typer", "rich", "pydantic", "pytest", "black", "mypy", "mkdocs", "mkdocs-material"]
        for line in result.stdout.split("\n"):
            for pkg in key_packages:
                if line.lower().startswith(pkg.lower()):
                    console.print(f"  {line}")
    except subprocess.CalledProcessError:
        console.print("[red]❌ Could not retrieve package list[/red]")

    # Show documentation status
    console.print("\n[blue]Documentation Status:[/blue]")
    mkdocs_config = PROJECT_ROOT / "mkdocs.yml"
    docs_dir = PROJECT_ROOT / "docs"
    site_dir = PROJECT_ROOT / "site"

    if mkdocs_config.exists():
        console.print("  [green]✅ mkdocs.yml found[/green]")
    else:
        console.print("  [red]❌ mkdocs.yml missing[/red]")

    if docs_dir.exists():
        doc_count = len(list(docs_dir.glob("*.md")))
        console.print(f"  [green]✅ docs/ directory found ({doc_count} markdown files)[/green]")
    else:
        console.print("  [red]❌ docs/ directory missing[/red]")

    if site_dir.exists():
        console.print("  [green]✅ Built documentation available[/green]")
    else:
        console.print("  [yellow]⚠️  Documentation not built yet[/yellow]")

    if check_command_exists("mkdocs"):
        try:
            mkdocs_version = subprocess.run(
                ["mkdocs", "--version"], capture_output=True, text=True
            ).stdout.strip()
            console.print(f"  [green]✅ MkDocs: {mkdocs_version}[/green]")
        except:
            console.print("  [yellow]⚠️  MkDocs installed but version check failed[/yellow]")
    else:
        console.print("  [red]❌ MkDocs not installed[/red]")


@app.command("verify-install")
def verify_install():
    """Verify installation."""
    console.print("[blue]Verifying installation...[/blue]")

    try:
        # Test import
        result = subprocess.run(
            ["python3", "-c", "import system_cli; print(f'System CLI {system_cli.__version__} imported successfully')"],
            capture_output=True, text=True, check=True
        )
        console.print(f"[green]✅ {result.stdout.strip()}[/green]")

        # Test CLI help
        subprocess.run(
            ["system-cli", "--help"], capture_output=True, check=True
        )
        console.print("[green]✅ CLI help command works[/green]")

        console.print("[green]✅ Installation verified[/green]")
    except subprocess.CalledProcessError as e:
        console.print(f"[red]❌ Installation verification failed: {e}[/red]")
        raise typer.Exit(1)


# Coverage Commands
coverage_app = typer.Typer(name="coverage", help="Coverage reporting commands")
app.add_typer(coverage_app, name="coverage")


@coverage_app.command("report")
def coverage_report():
    """Generate coverage report."""
    run_command(["coverage", "report", "--show-missing"], "Generating coverage report")


@coverage_app.command("html")
def coverage_html():
    """Generate HTML coverage report."""
    run_command(["coverage", "html"], "Generating HTML coverage report")
    console.print("[green]Coverage report generated in htmlcov/[/green]")


# Docker Commands (for future use)
docker_app = typer.Typer(name="docker", help="Docker commands (future use)")
app.add_typer(docker_app, name="docker")


@docker_app.command("build")
def docker_build():
    """Build Docker image."""
    if check_command_exists("docker"):
        run_command(["docker", "build", "-t", "system-cli", "."], "Building Docker image")
    else:
        console.print("[red]❌ Docker not installed[/red]")


@docker_app.command("test")
def docker_test():
    """Test in Docker container."""
    if check_command_exists("docker"):
        run_command(["docker", "run", "--rm", "system-cli", "test"], "Testing in Docker")
    else:
        console.print("[red]❌ Docker not installed[/red]")


# Benchmark Commands
@app.command("benchmark")
def benchmark():
    """Run performance benchmarks."""
    if (TESTS_DIR / "benchmark").exists():
        run_command(
            ["python3", "-m", "pytest", "tests/benchmark/", "--benchmark-only"],
            "Running performance benchmarks"
        )
    else:
        console.print("[yellow]⚠️  No benchmark tests found[/yellow]")


# Help Commands
help_app = typer.Typer(name="help", help="Detailed help for specific command groups")
app.add_typer(help_app, name="help")


@help_app.command("test")
def help_test():
    """Show detailed help for test commands."""
    table = Table(title="Test Commands")
    table.add_column("Command", style="cyan")
    table.add_column("Description", style="green")

    table.add_row("dev.py test all", "Run full test suite with coverage")
    table.add_row("dev.py test fast", "Run tests quickly without coverage")
    table.add_row("dev.py test unit", "Run only unit tests")
    table.add_row("dev.py test integration", "Run only integration tests")
    table.add_row("dev.py test e2e", "Run only end-to-end tests")

    console.print(table)


@help_app.command("lint")
def help_lint():
    """Show detailed help for linting commands."""
    table = Table(title="Linting Commands")
    table.add_column("Command", style="cyan")
    table.add_column("Description", style="green")

    table.add_row("dev.py lint all", "Run all linting tools")
    table.add_row("dev.py lint format", "Format code with black and isort")
    table.add_row("dev.py lint type-check", "Run mypy type checking")
    table.add_row("dev.py lint security", "Run security checks")

    console.print(table)


@help_app.command("build")
def help_build():
    """Show detailed help for build commands."""
    table = Table(title="Build Commands")
    table.add_column("Command", style="cyan")
    table.add_column("Description", style="green")

    table.add_row("dev.py build dist", "Build distribution packages")
    table.add_row("dev.py build clean", "Clean build artifacts")
    table.add_row("dev.py build check", "Check package metadata")

    console.print(table)


@help_app.command("docs")
def help_docs():
    """Show detailed help for documentation commands."""
    table = Table(title="Documentation Commands")
    table.add_column("Command", style="cyan")
    table.add_column("Description", style="green")

    table.add_row("dev.py docs build", "Build documentation with MkDocs")
    table.add_row("dev.py docs serve", "Serve documentation locally with live reload")
    table.add_row("dev.py docs deploy", "Deploy documentation to GitHub Pages")
    table.add_row("dev.py docs clean", "Clean built documentation files")
    table.add_row("dev.py docs check", "Check documentation for issues")
    table.add_row("dev.py docs new <name>", "Create a new documentation page")
    table.add_row("dev.py docs validate", "Validate MkDocs configuration")

    console.print(table)

    console.print("\n[bold]Examples:[/bold]")
    console.print("  [cyan]dev.py docs serve --port 8080[/cyan]           Serve on port 8080")
    console.print("  [cyan]dev.py docs build --clean --strict[/cyan]      Build with clean and strict mode")
    console.print("  [cyan]dev.py docs new user-guide --section guides[/cyan]  Create page in guides section")
    console.print("  [cyan]dev.py docs deploy --message 'Update docs'[/cyan]   Deploy with custom message")


# Main help command override
@app.callback()
def main(
    ctx: typer.Context,
    version: bool = typer.Option(False, "--version", "-v", help="Show version and exit"),
):
    """
    Development CLI for System CLI project.

    This tool replaces the Makefile with a more powerful and user-friendly Python CLI.
    Use 'dev.py --help' to see all available commands.
    """
    if version:
        console.print("[green]System CLI Development Tool v1.0.0[/green]")
        raise typer.Exit()

    if ctx.invoked_subcommand is None:
        # Show main help with organized command groups
        console.print(Panel.fit(
            "[bold blue]System CLI Development Tool[/bold blue]\n\n"
            "A comprehensive development CLI to replace the Makefile.\n"
            "Provides better error handling, rich output, and cross-platform compatibility.",
            title="Welcome"
        ))

        console.print("\n[bold]Available Command Groups:[/bold]")
        groups = [
            ("install", "Setup and installation commands"),
            ("test", "Testing commands"),
            ("lint", "Linting and formatting commands"),
            ("build", "Build and distribution commands"),
            ("docs", "Documentation commands"),
            ("cli", "CLI testing commands"),
            ("deps", "Dependency management commands"),
            ("version", "Version management commands"),
            ("coverage", "Coverage reporting commands"),
            ("help", "Detailed help for command groups"),
        ]

        for group, desc in groups:
            console.print(f"  [cyan]{group:<12}[/cyan] {desc}")

        console.print("\n[bold]Standalone Commands:[/bold]")
        standalone = [
            ("qa", "Run all quality assurance checks"),
            ("dev-cycle", "Run full development cycle"),
            ("dev-setup", "Set up development environment"),
            ("env-info", "Show environment information"),
            ("verify-install", "Verify installation"),
            ("benchmark", "Run performance benchmarks"),
        ]

        for cmd, desc in standalone:
            console.print(f"  [cyan]{cmd:<12}[/cyan] {desc}")

        console.print(f"\n[dim]Use 'dev.py <command> --help' for more information on a specific command.[/dim]")


if __name__ == "__main__":
    app()
