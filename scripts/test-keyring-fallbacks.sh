#!/bin/bash
# Comprehensive Keyring Fallback Testing Script
# Tests all authentication methods and fallback scenarios

set -o pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Functions
show_test_header() {
    echo -e "${PURPLE}🧪 $1${NC}"
    echo -e "${PURPLE}$(printf '=%.0s' $(seq 1 ${#1}))${NC}"
}

show_test() {
    echo -e "${BLUE}Test $((++TESTS_TOTAL)): $1${NC}"
}

show_pass() {
    echo -e "${GREEN}✅ PASS: $1${NC}"
    ((TESTS_PASSED++))
}

show_fail() {
    echo -e "${RED}❌ FAIL: $1${NC}"
    ((TESTS_FAILED++))
}

show_warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
}

show_info() {
    echo -e "${CYAN}ℹ️  INFO: $1${NC}"
}

test_keyring_daemon_status() {
    show_test "Keyring daemon status"
    
    if systemctl --user is-active gnome-keyring-daemon >/dev/null 2>&1; then
        show_pass "Keyring daemon is running"
    else
        show_fail "Keyring daemon is not running"
        show_info "Try: systemctl --user start gnome-keyring-daemon"
    fi
}

test_keyring_unlock_status() {
    show_test "Current keyring unlock status"
    
    if secret-tool search keyring login >/dev/null 2>&1; then
        show_pass "Keyring is currently unlocked"
        return 0
    else
        show_warning "Keyring is currently locked"
        return 1
    fi
}

test_manual_password_unlock() {
    show_test "Manual password unlock capability"
    
    # Test if the unlock command exists and is accessible
    if command -v gnome-keyring-daemon >/dev/null 2>&1; then
        show_pass "gnome-keyring-daemon command available"
        
        # Test unlock with empty input (should prompt for password)
        show_info "Testing unlock prompt (will timeout after 5 seconds)..."
        if timeout 5 bash -c 'echo "" | gnome-keyring-daemon --unlock' >/dev/null 2>&1 || true; then
            show_pass "Password unlock interface accessible"
        else
            show_warning "Password unlock requires manual input (normal behavior)"
        fi
    else
        show_fail "gnome-keyring-daemon command not found"
    fi
}

test_secret_tool_access() {
    show_test "Secret-tool access capability"
    
    if command -v secret-tool >/dev/null 2>&1; then
        show_pass "secret-tool command available"
        
        # Test basic secret-tool functionality
        if secret-tool search --all >/dev/null 2>&1; then
            show_pass "secret-tool can access keyring system"
        else
            show_warning "secret-tool access limited (may require unlocked keyring)"
        fi
    else
        show_fail "secret-tool command not found"
        show_info "Install with: sudo apt install libsecret-tools"
    fi
}

test_tpm_integration_status() {
    show_test "TPM integration status"
    
    local tpm_dir="$HOME/.config/tpm-keyring"
    local tpm_password_file="$tpm_dir/keyring-password.enc"
    
    if [ -d "$tpm_dir" ]; then
        show_pass "TPM configuration directory exists"
        
        if [ -f "$tpm_password_file" ]; then
            show_pass "TPM encrypted password file exists"
            
            # Test TPM decryption capability
            if command -v clevis >/dev/null 2>&1; then
                show_pass "Clevis TPM tool available"
                
                # Test decryption (without actually decrypting)
                if clevis decrypt --help >/dev/null 2>&1; then
                    show_pass "Clevis decrypt functionality available"
                else
                    show_fail "Clevis decrypt not working"
                fi
            else
                show_fail "Clevis not installed"
                show_info "Install with: sudo apt install clevis"
            fi
        else
            show_warning "TPM encrypted password file missing"
        fi
    else
        show_info "TPM integration not configured (normal if not using TPM)"
    fi
}

test_emergency_recovery_availability() {
    show_test "Emergency recovery script availability"
    
    local emergency_script="$HOME/.config/tpm-keyring/emergency-recovery.sh"
    
    if [ -f "$emergency_script" ]; then
        show_pass "Emergency recovery script exists"
        
        if [ -x "$emergency_script" ]; then
            show_pass "Emergency recovery script is executable"
            
            # Test script help function
            if "$emergency_script" --help >/dev/null 2>&1 || "$emergency_script" help >/dev/null 2>&1; then
                show_pass "Emergency recovery script responds to help"
            else
                show_warning "Emergency recovery script may not have help function"
            fi
        else
            show_fail "Emergency recovery script is not executable"
            show_info "Fix with: chmod +x $emergency_script"
        fi
    else
        show_info "Emergency recovery script not found (normal if TPM not configured)"
    fi
}

test_system_cli_integration() {
    show_test "System-CLI keyring integration"
    
    if command -v system-cli >/dev/null 2>&1; then
        show_pass "system-cli command available"
        
        # Test keyring subcommand
        if system-cli keyring --help >/dev/null 2>&1; then
            show_pass "system-cli keyring subcommand available"
            
            # Test specific emergency commands
            if system-cli keyring emergency-recovery --help >/dev/null 2>&1; then
                show_pass "Emergency recovery command available in system-cli"
            else
                show_warning "Emergency recovery command not available in system-cli"
            fi
            
            if system-cli keyring unlock-manual --help >/dev/null 2>&1; then
                show_pass "Manual unlock command available in system-cli"
            else
                show_warning "Manual unlock command not available in system-cli"
            fi
        else
            show_fail "system-cli keyring subcommand not available"
        fi
    else
        show_fail "system-cli not installed or not in PATH"
        show_info "Install with: pip install -e ."
    fi
}

test_fallback_sequence() {
    show_test "Fallback sequence simulation"
    
    show_info "Simulating fallback sequence (TPM → Fingerprint → Password)..."
    
    # Simulate TPM failure
    show_info "1. TPM unlock simulation..."
    if [ -f "$HOME/.config/tpm-keyring/keyring-password.enc" ]; then
        show_warning "TPM configured but testing fallback"
    else
        show_info "TPM not configured - would fall back to next method"
    fi
    
    # Simulate fingerprint failure
    show_info "2. Fingerprint unlock simulation..."
    if command -v fprintd-verify >/dev/null 2>&1; then
        show_warning "Fingerprint available but testing fallback"
    else
        show_info "Fingerprint not available - would fall back to password"
    fi
    
    # Password method (always available)
    show_info "3. Password unlock (final fallback)..."
    if command -v gnome-keyring-daemon >/dev/null 2>&1; then
        show_pass "Password unlock always available as final fallback"
    else
        show_fail "Password unlock not available - CRITICAL ISSUE"
    fi
}

test_recovery_procedures() {
    show_test "Recovery procedure documentation"
    
    show_info "Available recovery methods:"
    
    # Method 1: Direct unlock
    if command -v gnome-keyring-daemon >/dev/null 2>&1; then
        show_pass "Method 1: Direct unlock - gnome-keyring-daemon --unlock"
    fi
    
    # Method 2: Service restart
    if command -v systemctl >/dev/null 2>&1; then
        show_pass "Method 2: Service restart - systemctl --user restart gnome-keyring-daemon"
    fi
    
    # Method 3: Emergency script
    if [ -f "$HOME/.config/tpm-keyring/emergency-recovery.sh" ]; then
        show_pass "Method 3: Emergency script available"
    else
        show_info "Method 3: Emergency script not configured"
    fi
    
    # Method 4: System-CLI
    if command -v system-cli >/dev/null 2>&1; then
        show_pass "Method 4: System-CLI emergency commands available"
    else
        show_info "Method 4: System-CLI not available"
    fi
}

run_comprehensive_test() {
    show_test_header "Comprehensive Keyring Fallback Test Suite"
    echo
    
    # Core functionality tests
    show_test_header "Core Functionality Tests"
    test_keyring_daemon_status
    test_keyring_unlock_status
    test_manual_password_unlock
    test_secret_tool_access
    echo
    
    # TPM integration tests
    show_test_header "TPM Integration Tests"
    test_tpm_integration_status
    test_emergency_recovery_availability
    echo
    
    # System integration tests
    show_test_header "System Integration Tests"
    test_system_cli_integration
    echo
    
    # Fallback logic tests
    show_test_header "Fallback Logic Tests"
    test_fallback_sequence
    test_recovery_procedures
    echo
    
    # Summary
    show_test_header "Test Summary"
    echo -e "${CYAN}Total Tests: $TESTS_TOTAL${NC}"
    echo -e "${GREEN}Passed: $TESTS_PASSED${NC}"
    echo -e "${RED}Failed: $TESTS_FAILED${NC}"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo -e "${GREEN}🎉 All tests passed! Your fallback system is robust.${NC}"
    elif [ $TESTS_FAILED -le 2 ]; then
        echo -e "${YELLOW}⚠️  Minor issues detected. System should still work with fallbacks.${NC}"
    else
        echo -e "${RED}❌ Multiple issues detected. Review fallback configuration.${NC}"
    fi
    
    echo
    show_test_header "Recommendations"
    
    if [ $TESTS_FAILED -gt 0 ]; then
        echo -e "${YELLOW}Recommended actions:${NC}"
        echo "1. Fix any failed tests above"
        echo "2. Ensure gnome-keyring-daemon is always available"
        echo "3. Test manual unlock: gnome-keyring-daemon --unlock"
        echo "4. Consider setting up emergency recovery script"
    else
        echo -e "${GREEN}Your keyring fallback system is well configured!${NC}"
        echo "Available fallback methods:"
        echo "• Manual password unlock"
        echo "• System-CLI emergency commands"
        echo "• Service restart procedures"
    fi
}

# Main execution
case "${1:-test}" in
    "test"|"run")
        run_comprehensive_test
        ;;
    "quick")
        show_test_header "Quick Fallback Test"
        test_keyring_daemon_status
        test_manual_password_unlock
        test_keyring_unlock_status || true  # Don't exit on locked keyring
        ;;
    "tpm")
        show_test_header "TPM-Specific Tests"
        test_tpm_integration_status
        test_emergency_recovery_availability
        ;;
    *)
        echo "Keyring Fallback Testing Script"
        echo "==============================="
        echo
        echo "Usage: $0 [COMMAND]"
        echo
        echo "Commands:"
        echo "  test, run  - Run comprehensive fallback tests (default)"
        echo "  quick      - Run quick essential tests"
        echo "  tpm        - Run TPM-specific tests only"
        echo
        ;;
esac
