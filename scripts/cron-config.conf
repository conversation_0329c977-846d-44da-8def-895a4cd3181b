# Security Monitoring Cron Configuration
# Dell Precision 5560 - Automated Security Audits
# Last Updated: June 7, 2025

# =============================================================================
# OUTPUT CONFIGURATION
# =============================================================================

# Base directory for storing audit reports
AUDIT_BASE_DIR="/home/<USER>/security-audits"

# Email configuration (set ENABLE_EMAIL=true to activate)
ENABLE_EMAIL=false
EMAIL_RECIPIENT="<EMAIL>"
EMAIL_SENDER="security-monitor@$(hostname)"
EMAIL_SMTP_SERVER="localhost"

# Retention policy (days)
DAILY_RETENTION=30
WEEKLY_RETENTION=90
MONTHLY_RETENTION=365

# Log level (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL="INFO"

# =============================================================================
# DAILY MONITORING CONFIGURATION
# =============================================================================

# Daily checks - Run at 8:00 AM every day
DAILY_CRON_TIME="0 8 * * *"
DAILY_ENABLED=true

# Daily check components
DAILY_KEYRING_CHECK=true
DAILY_FINGERPRINT_CHECK=true
DAILY_FIREWALL_CHECK=true
DAILY_STORAGE_CHECK=true
DAILY_MEMORY_CHECK=true
DAILY_FAILED_LOGINS=true

# Daily alert thresholds
STORAGE_WARNING_THRESHOLD=85
STORAGE_CRITICAL_THRESHOLD=95
MEMORY_WARNING_THRESHOLD=80
FAILED_LOGIN_THRESHOLD=5

# =============================================================================
# WEEKLY AUDIT CONFIGURATION
# =============================================================================

# Weekly audits - Run at 9:00 AM every Sunday
WEEKLY_CRON_TIME="0 9 * * 0"
WEEKLY_ENABLED=true

# Weekly audit components
WEEKLY_SECURITY_AUDIT=true
WEEKLY_KEYRING_AUDIT=true
WEEKLY_POWER_ANALYSIS=true
WEEKLY_SERVICE_CHECK=true
WEEKLY_APPARMOR_CHECK=true
WEEKLY_PACKAGE_UPDATES=true

# Weekly analysis duration (minutes)
POWERTOP_ANALYSIS_TIME=60

# =============================================================================
# MONTHLY COMPREHENSIVE AUDIT CONFIGURATION
# =============================================================================

# Monthly comprehensive audit - Run at 10:00 AM on the 1st of each month
MONTHLY_CRON_TIME="0 10 1 * *"
MONTHLY_ENABLED=true

# Monthly audit components
MONTHLY_FULL_SECURITY_AUDIT=true
MONTHLY_SYSTEM_HEALTH=true
MONTHLY_PERFORMANCE_ANALYSIS=true
MONTHLY_CONFIGURATION_BACKUP=true
MONTHLY_VULNERABILITY_SCAN=true
MONTHLY_CLEANUP_OLD_REPORTS=true

# Monthly report format
MONTHLY_GENERATE_PDF=false
MONTHLY_INCLUDE_CHARTS=true

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Notification levels
NOTIFY_ON_SUCCESS=false
NOTIFY_ON_WARNING=true
NOTIFY_ON_ERROR=true
NOTIFY_ON_CRITICAL=true

# Notification methods
NOTIFY_EMAIL=false
NOTIFY_DESKTOP=true
NOTIFY_LOG=true

# Desktop notification duration (seconds)
DESKTOP_NOTIFICATION_TIMEOUT=10000

# =============================================================================
# SYSTEM INTEGRATION
# =============================================================================

# Script locations (relative to repository root)
SCRIPT_DIR="./scripts"
KEYRING_MONITOR="$SCRIPT_DIR/keyring-monitor.sh"
SECURITY_AUDIT="$SCRIPT_DIR/security-audit.sh"
COMPREHENSIVE_AUDIT="$SCRIPT_DIR/comprehensive-security-audit.sh"
SYSTEM_HEALTH="$SCRIPT_DIR/systemd_health_check.sh"

# System commands
POWERTOP_CMD="/usr/bin/powertop"
MAIL_CMD="/usr/bin/mail"
NOTIFY_CMD="/usr/bin/notify-send"

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Parallel execution
ENABLE_PARALLEL_EXECUTION=false
MAX_PARALLEL_JOBS=2

# Compression for old reports
COMPRESS_OLD_REPORTS=true
COMPRESSION_AGE_DAYS=7

# Emergency contact
EMERGENCY_CONTACT="<EMAIL>"
CRITICAL_ALERT_PHONE=""

# Backup configuration
BACKUP_REPORTS_TO_CLOUD=false
CLOUD_BACKUP_PATH=""

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# File permissions for reports
REPORT_FILE_PERMISSIONS=600
REPORT_DIR_PERMISSIONS=700

# Encryption for sensitive reports
ENCRYPT_REPORTS=false
GPG_RECIPIENT=""

# Audit trail
LOG_ALL_EXECUTIONS=true
AUDIT_LOG_FILE="$AUDIT_BASE_DIR/audit-execution.log"
