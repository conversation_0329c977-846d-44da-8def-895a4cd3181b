#!/bin/bash

# Thermal Monitoring Script for Dell Precision 5560
# Monitors CPU temps, fan speeds, and thermal throttling

echo "=== Dell Precision 5560 Thermal Monitor ==="
echo "Press Ctrl+C to stop"
echo

# Function to get CPU temperature
get_cpu_temp() {
    sensors | grep "Package id 0" | awk '{print $4}' | sed 's/+//;s/°C//'
}

# Function to get max core temp
get_max_core_temp() {
    sensors | grep "Core" | awk '{print $3}' | sed 's/+//;s/°C//' | sort -nr | head -1
}

# Function to get fan speeds
get_fan_speeds() {
    cpu_fan=$(sensors | grep "CPU Fan:" | awk '{print $3}')
    video_fan=$(sensors | grep "Video Fan:" | awk '{print $3}')
    echo "$cpu_fan $video_fan"
}

# Function to get CPU frequency
get_cpu_freq() {
    cat /proc/cpuinfo | grep "cpu MHz" | head -1 | awk '{print $4}'
}

# Function to check thermal throttling
check_throttling() {
    dmesg | tail -20 | grep -i "thermal\|throttle" | tail -1
}

# Main monitoring loop
while true; do
    clear
    echo "=== Dell Precision 5560 Thermal Monitor ==="
    echo "$(date)"
    echo "========================================"
    
    # CPU Temperature
    pkg_temp=$(get_cpu_temp)
    max_core=$(get_max_core_temp)
    echo "🌡️  CPU Package: ${pkg_temp}°C"
    echo "🌡️  Hottest Core: ${max_core}°C"
    
    # Temperature status
    if (( $(echo "$pkg_temp > 70" | bc -l) )); then
        echo "🔥 WARNING: CPU running hot!"
    elif (( $(echo "$pkg_temp > 60" | bc -l) )); then
        echo "⚠️  CPU temperature elevated"
    else
        echo "✅ CPU temperature normal"
    fi
    
    # Fan speeds
    read cpu_fan video_fan <<< $(get_fan_speeds)
    echo "🌪️  CPU Fan: $cpu_fan"
    echo "🌪️  Video Fan: $video_fan"
    
    # CPU frequency
    cpu_freq=$(get_cpu_freq)
    echo "⚡ CPU Frequency: ${cpu_freq} MHz"
    
    # Power governor
    governor=$(cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor)
    echo "⚙️  Power Governor: $governor"
    
    # NVMe temperatures
    nvme1_temp=$(sensors | grep "nvme-pci-0200" -A2 | grep "Composite" | awk '{print $2}' | sed 's/+//;s/°C//')
    nvme2_temp=$(sensors | grep "nvme-pci-0300" -A2 | grep "Composite" | awk '{print $2}' | sed 's/+//;s/°C//')
    echo "💾 NVMe 1: ${nvme1_temp}°C"
    echo "💾 NVMe 2: ${nvme2_temp}°C"
    
    # GPU temperature
    gpu_temp=$(sensors | grep "Video:" | awk '{print $2}' | sed 's/+//;s/°C//')
    echo "🎮 GPU: ${gpu_temp}°C"
    
    echo "========================================"
    echo "Press Ctrl+C to stop monitoring"
    
    sleep 3
done
