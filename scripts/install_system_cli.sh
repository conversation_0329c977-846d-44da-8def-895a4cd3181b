#!/bin/bash
# Installation script for System CLI
# Dell Precision 5560 - Python CLI Setup

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check Python version
    if ! command -v python3 >/dev/null 2>&1; then
        error "Python 3 is required but not installed"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    if [[ $(echo "$python_version >= 3.8" | bc -l) -eq 0 ]]; then
        error "Python 3.8 or higher is required (found $python_version)"
        exit 1
    fi
    
    success "Python $python_version found"
    
    # Check pip
    if ! command -v pip3 >/dev/null 2>&1; then
        error "pip3 is required but not installed"
        exit 1
    fi
    
    success "pip3 found"
}

# Install system dependencies
install_system_deps() {
    log "Installing system dependencies..."
    
    # Check if we need to install packages
    missing_packages=()
    
    if ! command -v secret-tool >/dev/null 2>&1; then
        missing_packages+=("libsecret-tools")
    fi
    
    if ! pgrep -f gnome-keyring-daemon >/dev/null 2>&1; then
        missing_packages+=("gnome-keyring")
    fi
    
    if ! command -v python3-venv >/dev/null 2>&1; then
        missing_packages+=("python3-venv")
    fi
    
    if [[ ${#missing_packages[@]} -gt 0 ]]; then
        log "Installing missing packages: ${missing_packages[*]}"
        
        if command -v apt >/dev/null 2>&1; then
            sudo apt update
            sudo apt install -y "${missing_packages[@]}"
        elif command -v dnf >/dev/null 2>&1; then
            sudo dnf install -y "${missing_packages[@]}"
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y "${missing_packages[@]}"
        else
            error "Unsupported package manager. Please install: ${missing_packages[*]}"
            exit 1
        fi
        
        success "System dependencies installed"
    else
        success "All system dependencies are already installed"
    fi
}

# Create virtual environment
create_venv() {
    log "Creating Python virtual environment..."
    
    VENV_DIR="$HOME/.local/share/system-cli/venv"
    
    if [[ -d "$VENV_DIR" ]]; then
        warning "Virtual environment already exists at $VENV_DIR"
        read -p "Remove and recreate? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$VENV_DIR"
        else
            log "Using existing virtual environment"
            return 0
        fi
    fi
    
    mkdir -p "$(dirname "$VENV_DIR")"
    python3 -m venv "$VENV_DIR"
    
    success "Virtual environment created at $VENV_DIR"
}

# Install Python package
install_package() {
    log "Installing System CLI package..."

    VENV_DIR="$HOME/.local/share/system-cli/venv"
    source "$VENV_DIR/bin/activate"
    
    # Upgrade pip
    pip install --upgrade pip
    
    # Install the package
    if [[ -f "pyproject.toml" ]]; then
        # Install from pyproject.toml (modern Python packaging)
        pip install -e .
        success "Installed System CLI from pyproject.toml"
    elif [[ -f "setup.py" ]]; then
        # Install from legacy setup.py
        pip install -e .
        success "Installed System CLI from setup.py"
    elif [[ -f "requirements.txt" ]]; then
        # Install from requirements (fallback)
        pip install -r requirements.txt
        success "Installed System CLI dependencies from requirements.txt"
    else
        error "No pyproject.toml, setup.py, or requirements.txt found"
        exit 1
    fi
}

# Create CLI wrapper script
create_wrapper() {
    log "Creating CLI wrapper script..."

    VENV_DIR="$HOME/.local/share/system-cli/venv"
    BIN_DIR="$HOME/.local/bin"
    WRAPPER_SCRIPT="$BIN_DIR/system-cli"
    
    mkdir -p "$BIN_DIR"
    
    cat > "$WRAPPER_SCRIPT" << EOF
#!/bin/bash
# System CLI wrapper script
source "$VENV_DIR/bin/activate"
python -m system_cli.main "\$@"
EOF
    
    chmod +x "$WRAPPER_SCRIPT"
    
    success "CLI wrapper created at $WRAPPER_SCRIPT"
    
    # Check if ~/.local/bin is in PATH
    if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
        warning "~/.local/bin is not in your PATH"
        echo "Add this to your ~/.bashrc or ~/.zshrc:"
        echo "export PATH=\"\$HOME/.local/bin:\$PATH\""
    fi
}

# Run initial setup
run_initial_setup() {
    log "Running initial setup..."

    VENV_DIR="$HOME/.local/share/system-cli/venv"
    source "$VENV_DIR/bin/activate"

    # Test the installation
    if python -m system_cli.main --help >/dev/null 2>&1; then
        success "System CLI installed successfully!"
        
        echo ""
        echo "🎉 Installation Complete!"
        echo "======================="
        echo ""
        echo "Next steps:"
        echo "1. Add ~/.local/bin to your PATH if not already done"
        echo "2. Run: keyring-cli setup dual-keyring"
        echo "3. Run: keyring-cli test dual-keyring"
        echo "4. Run: keyring-cli status"
        echo ""
        echo "Available commands:"
        echo "  system-cli --help                 # Show all commands"
        echo "  system-cli keyring status          # Check keyring status"
        echo "  system-cli security audit          # Run security audit"
        echo "  system-cli setup dual-keyring      # Set up dual keyring"
        echo ""
        
        # Ask if user wants to run initial setup
        read -p "Run initial dual keyring setup now? (Y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]] || [[ -z $REPLY ]]; then
            python -m system_cli.main setup dual-keyring
        fi
        
    else
        error "Installation verification failed"
        exit 1
    fi
}

# Cleanup function
cleanup() {
    if [[ $? -ne 0 ]]; then
        error "Installation failed"
        echo "Check the error messages above and try again"
    fi
}

# Main installation function
main() {
    echo "🖥️ System CLI Installation Script"
    echo "=================================="
    echo ""
    
    trap cleanup EXIT
    
    check_root
    check_requirements
    install_system_deps
    create_venv
    install_package
    create_wrapper
    run_initial_setup
    
    success "Installation completed successfully!"
}

# Run main function
main "$@"
