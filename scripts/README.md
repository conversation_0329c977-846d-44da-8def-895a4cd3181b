# Scripts Directory

This directory contains development tools, system management scripts, and utilities for the project.

## 📁 Directory Structure

```
scripts/
├── dev.py                          # 🆕 Python development CLI (replaces Makefile)
├── migrate-from-makefile.py        # 🆕 Migration helper tool
├── dev-requirements.txt            # 🆕 Dev CLI dependencies
├── install_system_cli.sh          # 📦 System CLI installation script
├── thermal_monitor.sh             # 🌡️ Real-time thermal monitoring dashboard
├── cron-config.conf                # ⚙️ Monitoring configuration
├── archived/                       # 📁 Archived scripts (integrated into CLIs)
│   ├── integrated-into-system-cli/ # ✅ Scripts integrated into system-cli
│   ├── standalone-utilities/       # 🔧 Standalone utility scripts
│   └── README.md                   # 📝 Archive documentation
└── README.md                       # 📝 This file
```

## 🛠️ Active Development Tools

### `dev.py` - Python Development CLI
**Purpose**: Modern replacement for Makefile with enhanced functionality

```bash
# Install dependencies
pip install -r scripts/dev-requirements.txt

# Basic usage
python3 scripts/dev.py --help

# Common development tasks
python3 scripts/dev.py install dev     # Install with dev dependencies
python3 scripts/dev.py test all        # Run tests with coverage
python3 scripts/dev.py lint all        # Run all linting tools
python3 scripts/dev.py build dist      # Build distribution packages
python3 scripts/dev.py dev-cycle       # Run full development cycle
```

**Features**:
- Rich console output with colors and progress indicators
- Better error handling and cross-platform compatibility
- Organized command groups (install, test, lint, build, docs, etc.)
- Comprehensive help system

**Status**: ✅ **Active** - Primary development tool

### `migrate-from-makefile.py` - Migration Helper
**Purpose**: Assists transition from Makefile to dev.py CLI

```bash
python3 scripts/migrate-from-makefile.py quick-start    # Show common commands
python3 scripts/migrate-from-makefile.py mapping        # Show all mappings
python3 scripts/migrate-from-makefile.py lookup test    # Look up specific command
```

**Status**: ✅ **Active** - Migration assistance tool

### `dev-requirements.txt` - Dependencies
**Purpose**: Minimal dependencies needed for dev.py

```bash
pip install -r scripts/dev-requirements.txt
```

**Status**: ✅ **Active** - Required for development CLI

### `thermal_monitor.sh` - Thermal Monitoring Dashboard
**Purpose**: Real-time thermal monitoring for Dell Precision 5560

```bash
./scripts/thermal_monitor.sh
```

**Features**:
- Real-time CPU package and core temperature monitoring
- Fan speed monitoring (CPU and Video fans)
- CPU frequency and power governor display
- NVMe and GPU temperature tracking
- Thermal status warnings and alerts
- Continuous monitoring with 3-second refresh

**Status**: ✅ **Active** - Critical thermal monitoring tool

## 📦 Installation & Configuration

### `install_system_cli.sh` - System CLI Installation
**Purpose**: Installs the system CLI package

```bash
./scripts/install_system_cli.sh
```

**Features**:
- Automated installation of system-cli
- Dependency checking and installation
- Configuration setup
- Post-installation testing

**Status**: ✅ **Active** - Primary installation method

### `cron-config.conf` - Monitoring Configuration
**Purpose**: Configuration file for automated monitoring

**Features**:
- Monitoring schedule configuration
- Email alert settings
- Security audit parameters
- System health check intervals

**Status**: ✅ **Active** - Used by monitoring system

## 📁 Archived Scripts

Most system administration scripts have been **integrated into the `system-cli` Python CLI tool**.

### ✅ Integrated Scripts (Archived)

The following scripts are now available as CLI commands and have been moved to `archived/integrated-into-system-cli/`:

**Authentication & Security:**
- `auth-flow-simple.sh` → `system-cli auth flow-simple`
- `fix-ssh-hardening-corrected.sh` → `system-cli security ssh-harden`
- `authentication-complexity-audit.sh` → `system-cli auth complexity-audit`

**System Management:**
- `power-management-check.py` → `system-cli system power-check`
- `optimize-etc-default-services.sh` → `system-cli system optimize-services`
- `config-cleanup.sh` → `system-cli maintenance config-cleanup`

**Setup & Configuration:**
- `setup-ssh-keys.sh` → `system-cli setup ssh-keys-interactive`
- `phase2-completion.sh` → `system-cli setup phase2-complete`

**TPM Integration:**
- `tpm-integration-improved.sh` → `system-cli keyring tmp-setup-advanced --method improved`
- `simple-tpm-setup.sh` → `system-cli keyring tpm-setup-advanced --method basic`

**Monitoring:**
- `configure-email-alerts.sh` → `system-cli monitor email-setup`
- `power-monitor.py` → `system-cli monitor power`

### 🔧 Standalone Utilities (Archived)

Some scripts remain available as standalone utilities in `archived/standalone-utilities/`:

- `secure-keyring-manager.sh` - Alternative keyring manager with GUI support

**See `archived/README.md` for complete integration mapping and usage instructions.**

## 📋 Usage Examples

### Development Workflow
```bash
# Set up development environment
python3 scripts/dev.py dev-setup

# Run tests
python3 scripts/dev.py test all

# Format and lint code
python3 scripts/dev.py lint all

# Build distribution
python3 scripts/dev.py build dist
```

### System CLI Usage
```bash
# Install system CLI
./scripts/install_system_cli.sh

# Use integrated commands instead of individual scripts
system-cli auth flow-simple --setup
system-cli security ssh-harden
system-cli system power-check
system-cli monitor email-setup setup --email <EMAIL>
```

### Thermal Monitoring
```bash
# Monitor thermal status in real-time
./scripts/thermal_monitor.sh
```

### Alternative Tools
```bash
# Use standalone keyring manager (archived)
./scripts/archived/standalone-utilities/secure-keyring-manager.sh
```

## 📝 Notes

### Migration to CLI Tools
- ✅ **System management** functionality integrated into `system-cli` command
- ✅ **Development tasks** use `dev.py` script instead of Makefile
- ✅ **Archived scripts** in `archived/` directory kept for reference
- ✅ **Configuration files** support automated monitoring system

### Recommended Usage
- **Use CLI commands** for all system management tasks
- **Use dev.py** for all development workflows
- **Refer to archived scripts** only for reference or emergency fallback
- **Check archived/README.md** for complete migration mapping

### Benefits of CLI Integration
- Consistent interface and help system
- Better error handling and progress indicators
- Rich output formatting and colors
- Integrated testing and validation
- Easier maintenance and updates
