# Archived Scripts

This directory contains scripts that have been integrated into the CLI tools or are no longer actively used.

## Directory Structure

```
archived/
├── integrated-into-system-cli/    # Scripts integrated into system-cli
├── standalone-utilities/          # Standalone utility scripts
└── README.md                      # This file
```

## Integration Status

### ✅ Integrated into system-cli

All scripts in `integrated-into-system-cli/` have been successfully integrated into the `system-cli` Python CLI tool. Use the CLI commands instead of running these scripts directly.

#### Authentication Scripts
- `auth-flow-simple.sh` → `system-cli auth flow-simple`
- `auth-flow-standardization.sh` → `system-cli auth standardize`
- `authentication-complexity-audit.sh` → `system-cli auth complexity-audit`

#### Security Scripts
- `fix-ssh-hardening-corrected.sh` → `system-cli security ssh-harden`
- `fix-ssh-hardening.sh` → (old version, replaced by corrected version)

#### System Management Scripts
- `power-management-check.py` → `system-cli system power-check`
- `power-management-summary.py` → `system-cli system power-summary`
- `power-monitor.py` → `system-cli monitor power`
- `optimize-etc-default-services.sh` → `system-cli system optimize-services`
- `optimize-etc-default.sh` → `system-cli system optimize-defaults`

#### Setup & Configuration Scripts
- `setup-ssh-keys.sh` → `system-cli setup ssh-keys-interactive`
- `phase2-completion.sh` → `system-cli setup phase2-complete`
- `phase2-implementation.sh` → `system-cli setup phase2-implement`
- `phase3-pam-optimization.sh` → `system-cli setup phase3-pam`

#### TPM Integration Scripts
- `simple-tpm-setup.sh` → `system-cli keyring tmp-setup-advanced --method basic`
- `tpm-integration-improved.sh` → `system-cli keyring tpm-setup-advanced --method improved`
- `tpm-keyring-integration.sh` → `system-cli keyring tpm-setup`
- `tmp-setup-fixed.sh` → `system-cli keyring tpm-setup-advanced --method fixed`
- `tpm-setup-simple.sh` → `system-cli keyring tpm-setup-advanced --method simple`
- `tpm-setup-with-permissions.sh` → `system-cli keyring tpm-setup-advanced --permissions`
- `tpm-simple-no-pcr.sh` → `system-cli keyring tpm-setup-advanced --method no-pcr`

#### Monitoring Scripts
- `configure-email-alerts.sh` → `system-cli monitor email-setup`
- `cron-security-wrapper.sh` → `system-cli monitor cron-wrapper`

#### Maintenance Scripts
- `config-cleanup.sh` → `system-cli maintenance config-cleanup`

### 🔧 Standalone Utilities

Scripts in `standalone-utilities/` are alternative implementations or specialized tools that remain available as standalone utilities.

#### Alternative Tools
- `secure-keyring-manager.sh` - Alternative keyring manager with GUI support
  - Provides GUI password prompts using zenity
  - Session timeout management
  - Status caching
  - Can be used as alternative to `system-cli keyring` commands

## Migration Guide

### From Scripts to CLI Commands

**Old way:**
```bash
./scripts/auth-flow-simple.sh setup
./scripts/power-management-check.py
./scripts/tpm-integration-improved.sh setup
```

**New way:**
```bash
system-cli auth flow-simple --setup
system-cli system power-check
system-cli keyring tpm-setup-advanced --method improved
```

### Benefits of CLI Integration

1. **Consistent Interface**: All commands follow the same pattern and help system
2. **Better Error Handling**: Rich error messages and progress indicators
3. **Type Safety**: Python type checking and validation
4. **Documentation**: Built-in help and documentation
5. **Testing**: Integrated testing framework
6. **Maintenance**: Easier to maintain and extend

## Usage

### Using Integrated Commands

```bash
# Show all available commands
system-cli --help

# Show commands for specific category
system-cli auth --help
system-cli security --help
system-cli setup --help

# Run specific commands
system-cli auth flow-simple --setup
system-cli security ssh-harden
system-cli system power-check
```

### Using Standalone Utilities

```bash
# Alternative keyring manager
./scripts/archived/standalone-utilities/secure-keyring-manager.sh
```

## Archive Policy

Scripts are archived when:
1. ✅ Successfully integrated into CLI tools
2. ✅ Functionality is fully replicated
3. ✅ Integration is tested and working
4. ✅ Documentation is updated

Scripts remain available for:
- Reference and comparison
- Emergency fallback if needed
- Understanding implementation details
- Historical context

## Restoration

If you need to restore an archived script:

```bash
# Copy back to active scripts directory
cp scripts/archived/integrated-into-system-cli/script-name.sh scripts/

# Make executable
chmod +x scripts/script-name.sh
```

However, it's recommended to use the integrated CLI commands instead.
