#!/bin/bash
# Authentication Flow Standardization Script
# Ensures consistent keyring unlock across all authentication methods

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AUTH_CONFIG_DIR="$HOME/.config/auth-flow"
PAM_HOOKS_DIR="$AUTH_CONFIG_DIR/pam-hooks"
LOG_FILE="$HOME/.cache/auth-flow.log"
BACKUP_DIR="$HOME/.cache/auth-flow-backup-$(date +%Y%m%d-%H%M%S)"

# Functions
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
    log_message "INFO: $1"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
    log_message "SUCCESS: $1"
}

show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    log_message "WARNING: $1"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
    log_message "ERROR: $1"
}

backup_pam_config() {
    show_status "Backing up PAM configuration..."
    
    mkdir -p "$BACKUP_DIR"
    sudo cp -r /etc/pam.d/ "$BACKUP_DIR/"
    
    show_success "PAM configuration backed up to $BACKUP_DIR"
}

setup_directories() {
    show_status "Setting up directories..."
    
    mkdir -p "$AUTH_CONFIG_DIR"
    mkdir -p "$PAM_HOOKS_DIR"
    chmod 700 "$AUTH_CONFIG_DIR"
    
    mkdir -p "$(dirname "$LOG_FILE")"
    
    show_success "Directories created"
}

create_keyring_unlock_hook() {
    show_status "Creating keyring unlock hook..."
    
    local hook_script="$PAM_HOOKS_DIR/keyring-unlock.sh"
    
    cat > "$hook_script" << 'EOF'
#!/bin/bash
# Keyring unlock hook for all authentication methods
# Ensures consistent keyring unlock behavior

set -euo pipefail

LOG_FILE="$HOME/.cache/auth-flow.log"
TPM_UNLOCK_SCRIPT="$HOME/.config/tpm-keyring/tmp-unlock-keyring.sh"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - AUTH-HOOK: $1" >> "$LOG_FILE" 2>/dev/null || true
}

# Only run for session opening
if [ "${PAM_TYPE:-}" != "open_session" ]; then
    exit 0
fi

# Only run for the actual user
if [ "${PAM_USER:-}" != "$(whoami 2>/dev/null || echo '')" ]; then
    exit 0
fi

log_message "Authentication hook triggered for user: ${PAM_USER:-unknown}"

# Check if keyring is already unlocked
if pgrep -f "gnome-keyring-daemon.*secrets" >/dev/null 2>&1; then
    if secret-tool search keyring login >/dev/null 2>&1; then
        log_message "Keyring already unlocked"
        exit 0
    fi
fi

# Try TPM-based unlock first (if available)
if [ -f "$TPM_UNLOCK_SCRIPT" ]; then
    log_message "Attempting TPM-based keyring unlock"
    if "$TPM_UNLOCK_SCRIPT" >/dev/null 2>&1; then
        log_message "TPM-based keyring unlock successful"
        exit 0
    else
        log_message "TPM-based keyring unlock failed, will try other methods"
    fi
fi

# For fingerprint authentication, we need special handling
# since it bypasses password entry
if [ "${PAM_SERVICE:-}" = "lightdm" ] || [ "${PAM_SERVICE:-}" = "gdm-fingerprint" ]; then
    log_message "Fingerprint authentication detected"
    
    # Try to unlock using cached credentials or prompt user
    # This is a placeholder - actual implementation depends on your setup
    log_message "Fingerprint auth keyring unlock not yet implemented"
fi

log_message "Keyring unlock hook completed"
exit 0
EOF
    
    chmod 755 "$hook_script"
    show_success "Keyring unlock hook created"
}

create_auth_monitor() {
    show_status "Creating authentication monitor..."
    
    local monitor_script="$AUTH_CONFIG_DIR/auth-monitor.sh"
    
    cat > "$monitor_script" << 'EOF'
#!/bin/bash
# Authentication method monitor
# Tracks which authentication methods are being used

LOG_FILE="$HOME/.cache/auth-flow.log"
STATS_FILE="$HOME/.cache/auth-stats.json"

log_auth_event() {
    local method="$1"
    local status="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Log to main log file
    echo "$timestamp - AUTH-EVENT: method=$method status=$status" >> "$LOG_FILE"
    
    # Update statistics
    if [ -f "$STATS_FILE" ]; then
        # Simple JSON update (requires jq for complex operations)
        # For now, just append to a simple format
        echo "{\"timestamp\":\"$timestamp\",\"method\":\"$method\",\"status\":\"$status\"}" >> "${STATS_FILE}.tmp"
    else
        echo "{\"timestamp\":\"$timestamp\",\"method\":\"$method\",\"status\":\"$status\"}" > "${STATS_FILE}.tmp"
    fi
    
    # Keep only last 100 entries
    tail -100 "${STATS_FILE}.tmp" > "$STATS_FILE" 2>/dev/null || true
    rm -f "${STATS_FILE}.tmp"
}

# Monitor authentication events
case "${1:-}" in
    fingerprint)
        log_auth_event "fingerprint" "${2:-unknown}"
        ;;
    password)
        log_auth_event "password" "${2:-unknown}"
        ;;
    tpm)
        log_auth_event "tpm" "${2:-unknown}"
        ;;
    *)
        log_auth_event "unknown" "${2:-unknown}"
        ;;
esac
EOF
    
    chmod 755 "$monitor_script"
    show_success "Authentication monitor created"
}

create_fingerprint_integration() {
    show_status "Creating fingerprint authentication integration..."
    
    local fp_script="$PAM_HOOKS_DIR/fingerprint-post-auth.sh"
    
    cat > "$fp_script" << 'EOF'
#!/bin/bash
# Post-fingerprint authentication script
# Ensures keyring is unlocked after fingerprint authentication

set -euo pipefail

LOG_FILE="$HOME/.cache/auth-flow.log"
AUTH_MONITOR="$HOME/.config/auth-flow/auth-monitor.sh"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - FP-AUTH: $1" >> "$LOG_FILE" 2>/dev/null || true
}

# Log the fingerprint authentication event
if [ -f "$AUTH_MONITOR" ]; then
    "$AUTH_MONITOR" fingerprint success
fi

log_message "Fingerprint authentication successful, checking keyring status"

# Check if keyring is unlocked
if secret-tool search keyring login >/dev/null 2>&1; then
    log_message "Keyring already unlocked"
    exit 0
fi

# Try TPM-based unlock
TPM_UNLOCK_SCRIPT="$HOME/.config/tpm-keyring/tpm-unlock-keyring.sh"
if [ -f "$TPM_UNLOCK_SCRIPT" ]; then
    log_message "Attempting TPM-based keyring unlock after fingerprint auth"
    if "$TPM_UNLOCK_SCRIPT" >/dev/null 2>&1; then
        log_message "TPM-based keyring unlock successful"
        exit 0
    fi
fi

# If TPM unlock fails, we might need to prompt for password
# This is a design decision - do we want seamless unlock or security?
log_message "Keyring remains locked after fingerprint authentication"

# Option 1: Leave keyring locked (more secure)
# Option 2: Prompt for password (less convenient)
# Option 3: Use a separate convenience keyring (current setup)

# For now, just log the event
log_message "Fingerprint authentication completed, keyring status: locked"
exit 0
EOF
    
    chmod 755 "$fp_script"
    show_success "Fingerprint integration script created"
}

create_session_manager() {
    show_status "Creating session manager..."
    
    local session_script="$AUTH_CONFIG_DIR/session-manager.sh"
    
    cat > "$session_script" << 'EOF'
#!/bin/bash
# Session manager for authentication flow
# Manages session state and keyring lifecycle

set -euo pipefail

LOG_FILE="$HOME/.cache/auth-flow.log"
SESSION_STATE_FILE="$HOME/.cache/session-state.json"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - SESSION: $1" >> "$LOG_FILE" 2>/dev/null || true
}

update_session_state() {
    local event="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    cat > "$SESSION_STATE_FILE" << EOF
{
    "last_event": "$event",
    "timestamp": "$timestamp",
    "keyring_status": "$(check_keyring_status)",
    "auth_methods": $(get_auth_methods)
}
EOF
}

check_keyring_status() {
    if secret-tool search keyring login >/dev/null 2>&1; then
        echo "unlocked"
    else
        echo "locked"
    fi
}

get_auth_methods() {
    local methods="[]"
    
    # Check if fingerprint is available
    if fprintd-list "$USER" >/dev/null 2>&1; then
        methods='["fingerprint"]'
    fi
    
    # Check if TPM is available
    if [ -f "$HOME/.config/tpm-keyring/keyring-password.enc" ]; then
        if [ "$methods" = "[]" ]; then
            methods='["tpm"]'
        else
            methods='["fingerprint","tpm"]'
        fi
    fi
    
    echo "$methods"
}

case "\${1:-}" in
    login)
        log_message "Session login event"
        update_session_state "login"
        ;;
    logout)
        log_message "Session logout event"
        update_session_state "logout"
        ;;
    lock)
        log_message "Session lock event"
        update_session_state "lock"
        ;;
    unlock)
        log_message "Session unlock event"
        update_session_state "unlock"
        ;;
    status)
        if [ -f "\$SESSION_STATE_FILE" ]; then
            cat "\$SESSION_STATE_FILE"
        else
            echo '{"status":"no_session_data"}'
        fi
        ;;
    *)
        echo "Usage: \$0 {login|logout|lock|unlock|status}"
        exit 1
        ;;
esac
EOF
    
    chmod 755 "$session_script"
    show_success "Session manager created"
}

create_auth_status_command() {
    show_status "Creating authentication status command..."
    
    local status_script="$AUTH_CONFIG_DIR/auth-status.sh"
    
    cat > "$status_script" << 'EOF'
#!/bin/bash
# Authentication status display
# Shows current authentication and keyring status

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔐 Authentication Status${NC}"
echo "======================="
echo ""

# Keyring status
echo -e "${YELLOW}Keyring Status:${NC}"
if secret-tool search keyring login >/dev/null 2>&1; then
    echo -e "  Login keyring: ${GREEN}✅ Unlocked${NC}"
else
    echo -e "  Login keyring: ${RED}🔒 Locked${NC}"
fi

if secret-tool search keyring convenience >/dev/null 2>&1; then
    echo -e "  Convenience keyring: ${GREEN}✅ Unlocked${NC}"
else
    echo -e "  Convenience keyring: ${YELLOW}⚠️  Not accessible${NC}"
fi

echo ""

# Authentication methods
echo -e "${YELLOW}Available Authentication Methods:${NC}"

# Fingerprint
if systemctl is-active fprintd >/dev/null 2>&1; then
    if fprintd-list "$USER" >/dev/null 2>&1; then
        echo -e "  Fingerprint: ${GREEN}✅ Available and enrolled${NC}"
    else
        echo -e "  Fingerprint: ${YELLOW}⚠️  Available but not enrolled${NC}"
    fi
else
    echo -e "  Fingerprint: ${RED}❌ Service not running${NC}"
fi

# TPM
if [ -e /dev/tpm0 ]; then
    if [ -f "$HOME/.config/tpm-keyring/keyring-password.enc" ]; then
        echo -e "  TPM: ${GREEN}✅ Available and configured${NC}"
    else
        echo -e "  TPM: ${YELLOW}⚠️  Available but not configured${NC}"
    fi
else
    echo -e "  TPM: ${RED}❌ Hardware not available${NC}"
fi

# Password
echo -e "  Password: ${GREEN}✅ Always available${NC}"

echo ""

# Session information
echo -e "${YELLOW}Session Information:${NC}"
SESSION_STATE_FILE="$HOME/.cache/session-state.json"
if [ -f "$SESSION_STATE_FILE" ]; then
    if command -v jq >/dev/null 2>&1; then
        echo "  Last event: $(jq -r '.last_event' "$SESSION_STATE_FILE")"
        echo "  Timestamp: $(jq -r '.timestamp' "$SESSION_STATE_FILE")"
    else
        echo "  Session data available (install jq for details)"
    fi
else
    echo "  No session data available"
fi

echo ""

# Recent authentication events
echo -e "${YELLOW}Recent Authentication Events:${NC}"
LOG_FILE="$HOME/.cache/auth-flow.log"
if [ -f "$LOG_FILE" ]; then
    tail -5 "$LOG_FILE" | while read line; do
        echo "  $line"
    done
else
    echo "  No authentication events logged"
fi
EOF
    
    chmod 755 "$status_script"
    show_success "Authentication status command created"
}

show_usage() {
    echo "Authentication Flow Standardization Tool"
    echo "======================================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     - Setup authentication flow standardization"
    echo "  status    - Show authentication status"
    echo "  monitor   - Show authentication monitoring"
    echo "  test      - Test authentication flows"
    echo "  remove    - Remove standardization setup"
    echo ""
}

cmd_setup() {
    echo "🔄 Authentication Flow Standardization Setup"
    echo "============================================"
    echo ""
    
    backup_pam_config
    setup_directories
    create_keyring_unlock_hook
    create_auth_monitor
    create_fingerprint_integration
    create_session_manager
    create_auth_status_command
    
    show_success "Authentication flow standardization setup complete!"
    echo ""
    echo "Created components:"
    echo "  - Keyring unlock hook"
    echo "  - Authentication monitor"
    echo "  - Fingerprint integration"
    echo "  - Session manager"
    echo "  - Status command"
    echo ""
    echo "Next steps:"
    echo "1. Test the setup: $0 test"
    echo "2. Check status: $0 status"
    echo "3. Monitor logs: tail -f $LOG_FILE"
}

cmd_status() {
    if [ -f "$AUTH_CONFIG_DIR/auth-status.sh" ]; then
        "$AUTH_CONFIG_DIR/auth-status.sh"
    else
        show_error "Authentication status command not found. Run setup first."
        exit 1
    fi
}

cmd_monitor() {
    echo "📊 Authentication Monitoring"
    echo "============================"
    echo ""
    
    if [ -f "$LOG_FILE" ]; then
        echo "Recent authentication events:"
        tail -20 "$LOG_FILE"
    else
        show_warning "No authentication events logged yet"
    fi
    
    echo ""
    
    STATS_FILE="$HOME/.cache/auth-stats.json"
    if [ -f "$STATS_FILE" ]; then
        echo "Authentication statistics:"
        echo "Last 10 events:"
        tail -10 "$STATS_FILE"
    else
        show_warning "No authentication statistics available yet"
    fi
}

cmd_test() {
    echo "🧪 Testing Authentication Flows"
    echo "==============================="
    echo ""
    
    # Test keyring status
    show_status "Testing keyring access..."
    if secret-tool search keyring login >/dev/null 2>&1; then
        show_success "Login keyring is accessible"
    else
        show_warning "Login keyring is not accessible"
    fi
    
    # Test TPM integration
    if [ -f "$HOME/.config/tpm-keyring/tpm-unlock-keyring.sh" ]; then
        show_status "Testing TPM integration..."
        if "$HOME/.config/tpm-keyring/tpm-unlock-keyring.sh" >/dev/null 2>&1; then
            show_success "TPM integration working"
        else
            show_warning "TPM integration not working"
        fi
    else
        show_warning "TPM integration not setup"
    fi
    
    # Test fingerprint
    show_status "Testing fingerprint service..."
    if systemctl is-active fprintd >/dev/null 2>&1; then
        show_success "Fingerprint service is active"
    else
        show_warning "Fingerprint service is not active"
    fi
    
    show_success "Authentication flow test completed"
}

cmd_remove() {
    echo "🗑️  Removing Authentication Flow Standardization"
    echo "==============================================="
    echo ""
    
    read -p "Are you sure you want to remove authentication flow standardization? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Operation cancelled."
        exit 0
    fi
    
    # Remove configuration directory
    rm -rf "$AUTH_CONFIG_DIR"
    
    # Remove log files
    rm -f "$LOG_FILE"
    rm -f "$HOME/.cache/auth-stats.json"
    rm -f "$HOME/.cache/session-state.json"
    
    show_success "Authentication flow standardization removed"
}

# Main command handling
case "${1:-}" in
    setup)
        cmd_setup
        ;;
    status)
        cmd_status
        ;;
    monitor)
        cmd_monitor
        ;;
    test)
        cmd_test
        ;;
    remove)
        cmd_remove
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
