#!/bin/bash
# Configuration Cleanup Script
# Based on System Configuration Audit findings from June 8, 2025

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging
LOG_FILE="/tmp/config-cleanup-$(date +%Y%m%d-%H%M%S).log"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Configuration Cleanup Script  ${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    echo -e "${YELLOW}Based on System Configuration Audit findings${NC}"
    echo -e "${YELLOW}Date: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo ""
}

check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        echo -e "${RED}❌ This script should NOT be run as root${NC}"
        echo -e "${YELLOW}Please run as regular user - sudo will be used when needed${NC}"
        exit 1
    fi
}

backup_file() {
    local file="$1"
    if [[ -f "$file" ]]; then
        local backup="${file}.backup-$(date +%Y%m%d-%H%M%S)"
        sudo cp "$file" "$backup"
        log "Created backup: $backup"
        echo -e "${GREEN}✅ Backup created: $backup${NC}"
    fi
}

cleanup_empty_tuned_conf() {
    local file="/etc/modprobe.d/tuned.conf"
    
    echo -e "${BLUE}🧹 Checking empty tuned.conf file...${NC}"
    
    if [[ -f "$file" ]]; then
        local size=$(stat -c%s "$file")
        if [[ $size -eq 0 ]]; then
            echo -e "${YELLOW}Found empty file: $file${NC}"
            echo -e "${YELLOW}This file is safe to remove (leftover from tuned)${NC}"
            
            read -p "Remove empty tuned.conf file? (y/N): " -n 1 -r
            echo
            
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                backup_file "$file"
                sudo rm "$file"
                log "Removed empty file: $file"
                echo -e "${GREEN}✅ Removed empty tuned.conf file${NC}"
            else
                echo -e "${YELLOW}⏭️  Skipped removing tuned.conf${NC}"
            fi
        else
            echo -e "${GREEN}✅ tuned.conf is not empty (size: $size bytes)${NC}"
        fi
    else
        echo -e "${GREEN}✅ tuned.conf does not exist${NC}"
    fi
    echo ""
}

review_bluetooth_service() {
    echo -e "${BLUE}🔍 Reviewing Bluetooth service...${NC}"
    
    local status=$(systemctl is-enabled bluetooth 2>/dev/null || echo "not-found")
    local active=$(systemctl is-active bluetooth 2>/dev/null || echo "inactive")
    
    echo -e "${YELLOW}Current status: $status, $active${NC}"
    
    if [[ "$status" == "enabled" ]]; then
        echo -e "${YELLOW}Bluetooth is currently enabled${NC}"
        echo -e "${YELLOW}Consider disabling if not needed to reduce attack surface${NC}"
        
        read -p "Do you use Bluetooth devices? (y/N): " -n 1 -r
        echo
        
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            read -p "Disable Bluetooth service? (y/N): " -n 1 -r
            echo
            
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                sudo systemctl disable bluetooth
                sudo systemctl stop bluetooth
                log "Disabled Bluetooth service"
                echo -e "${GREEN}✅ Bluetooth service disabled${NC}"
            else
                echo -e "${YELLOW}⏭️  Keeping Bluetooth enabled${NC}"
            fi
        else
            echo -e "${GREEN}✅ Keeping Bluetooth enabled for your devices${NC}"
        fi
    else
        echo -e "${GREEN}✅ Bluetooth is already disabled${NC}"
    fi
    echo ""
}

review_avahi_service() {
    echo -e "${BLUE}🔍 Reviewing Avahi daemon...${NC}"
    
    local status=$(systemctl is-enabled avahi-daemon 2>/dev/null || echo "not-found")
    local active=$(systemctl is-active avahi-daemon 2>/dev/null || echo "inactive")
    
    echo -e "${YELLOW}Current status: $status, $active${NC}"
    echo -e "${YELLOW}Avahi provides network service discovery (mDNS/DNS-SD)${NC}"
    
    if [[ "$status" == "enabled" ]]; then
        echo -e "${YELLOW}Avahi is currently enabled${NC}"
        echo -e "${YELLOW}Used for: Network printers, file sharing, service discovery${NC}"
        
        read -p "Do you use network printers or file sharing? (y/N): " -n 1 -r
        echo
        
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            read -p "Disable Avahi daemon? (y/N): " -n 1 -r
            echo
            
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                sudo systemctl disable avahi-daemon
                sudo systemctl stop avahi-daemon
                log "Disabled Avahi daemon"
                echo -e "${GREEN}✅ Avahi daemon disabled${NC}"
            else
                echo -e "${YELLOW}⏭️  Keeping Avahi enabled${NC}"
            fi
        else
            echo -e "${GREEN}✅ Keeping Avahi enabled for network services${NC}"
        fi
    else
        echo -e "${GREEN}✅ Avahi is already disabled${NC}"
    fi
    echo ""
}

show_apparmor_suggestions() {
    echo -e "${BLUE}🛡️  AppArmor optimization suggestions...${NC}"
    
    local unconfined_count=$(sudo aa-status | grep "profiles are in unconfined mode" | awk '{print $1}' || echo "0")
    
    echo -e "${YELLOW}Currently $unconfined_count profiles are unconfined${NC}"
    echo -e "${YELLOW}Key applications that could benefit from enforcement:${NC}"
    echo -e "${YELLOW}  • Firefox (web browser security)${NC}"
    echo -e "${YELLOW}  • 1Password (credential manager security)${NC}"
    echo -e "${YELLOW}  • Development tools${NC}"
    echo ""
    echo -e "${BLUE}To enable enforcement for specific applications:${NC}"
    echo -e "${YELLOW}  sudo aa-enforce /etc/apparmor.d/firefox${NC}"
    echo -e "${YELLOW}  sudo aa-enforce /etc/apparmor.d/1password${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  Test in complain mode first:${NC}"
    echo -e "${YELLOW}  sudo aa-complain /etc/apparmor.d/firefox${NC}"
    echo -e "${YELLOW}  # Use application normally, then check logs${NC}"
    echo -e "${YELLOW}  sudo aa-enforce /etc/apparmor.d/firefox${NC}"
    echo ""
}

show_monitoring_suggestions() {
    echo -e "${BLUE}📊 Configuration monitoring suggestions...${NC}"
    echo ""
    echo -e "${YELLOW}Consider setting up automated monitoring for:${NC}"
    echo -e "${YELLOW}  • Configuration file changes in /etc/${NC}"
    echo -e "${YELLOW}  • Service status changes${NC}"
    echo -e "${YELLOW}  • Security policy modifications${NC}"
    echo ""
    echo -e "${BLUE}Available tools:${NC}"
    echo -e "${YELLOW}  • system-cli system config-audit (monthly)${NC}"
    echo -e "${YELLOW}  • system-cli security audit (weekly)${NC}"
    echo -e "${YELLOW}  • system-cli monitor dashboard (daily)${NC}"
    echo ""
}

show_summary() {
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}     Cleanup Summary Complete    ${NC}"
    echo -e "${GREEN}================================${NC}"
    echo ""
    echo -e "${GREEN}✅ Configuration audit findings addressed${NC}"
    echo -e "${GREEN}✅ System security posture maintained${NC}"
    echo -e "${GREEN}✅ Cleanup opportunities reviewed${NC}"
    echo ""
    echo -e "${BLUE}Log file: $LOG_FILE${NC}"
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo -e "${YELLOW}  1. Review AppArmor enforcement opportunities${NC}"
    echo -e "${YELLOW}  2. Set up regular configuration monitoring${NC}"
    echo -e "${YELLOW}  3. Schedule monthly configuration audits${NC}"
    echo ""
}

main() {
    print_header
    check_permissions
    
    log "Starting configuration cleanup based on audit findings"
    
    # Cleanup tasks
    cleanup_empty_tuned_conf
    review_bluetooth_service
    review_avahi_service
    
    # Suggestions (no changes)
    show_apparmor_suggestions
    show_monitoring_suggestions
    
    log "Configuration cleanup completed"
    show_summary
}

# Run main function
main "$@"
