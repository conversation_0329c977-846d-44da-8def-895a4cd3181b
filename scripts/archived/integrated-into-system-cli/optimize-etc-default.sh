#!/bin/bash
# /etc/default/ Configuration Optimization Script
# Based on analysis findings from June 8, 2025

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging
LOG_FILE="/tmp/etc-default-optimization-$(date +%Y%m%d-%H%M%S).log"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  /etc/default/ Configuration Optimizer ${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    echo -e "${YELLOW}Based on configuration analysis findings${NC}"
    echo -e "${YELLOW}Date: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo ""
}

check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        echo -e "${RED}❌ This script should NOT be run as root${NC}"
        echo -e "${YELLOW}Please run as regular user - sudo will be used when needed${NC}"
        exit 1
    fi
}

backup_file() {
    local file="$1"
    if [[ -f "$file" ]]; then
        local backup="${file}.backup-$(date +%Y%m%d-%H%M%S)"
        sudo cp "$file" "$backup"
        log "Created backup: $backup"
        echo -e "${GREEN}✅ Backup created: $backup${NC}"
    fi
}

fix_ipv6_configuration() {
    echo -e "${BLUE}🔍 Analyzing IPv6 configuration conflict...${NC}"
    
    local grub_ipv6_disabled=$(grep -c "ipv6.disable=1" /etc/default/grub || echo "0")
    local ufw_ipv6_enabled=$(grep -c "IPV6=yes" /etc/default/ufw || echo "0")
    
    if [[ $grub_ipv6_disabled -gt 0 && $ufw_ipv6_enabled -gt 0 ]]; then
        echo -e "${YELLOW}⚠️ IPv6 Configuration Conflict Detected:${NC}"
        echo -e "${YELLOW}   - Kernel: IPv6 disabled (ipv6.disable=1)${NC}"
        echo -e "${YELLOW}   - UFW: IPv6 enabled (IPV6=yes)${NC}"
        echo ""
        echo -e "${YELLOW}Recommended fix: Disable IPv6 in UFW to match kernel${NC}"
        
        read -p "Fix IPv6 configuration conflict? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            backup_file "/etc/default/ufw"
            sudo sed -i 's/IPV6=yes/IPV6=no/' /etc/default/ufw
            log "Disabled IPv6 in UFW configuration"
            echo -e "${GREEN}✅ IPv6 disabled in UFW to match kernel setting${NC}"
            
            echo -e "${YELLOW}Reloading UFW configuration...${NC}"
            sudo ufw reload
            echo -e "${GREEN}✅ UFW configuration reloaded${NC}"
        else
            echo -e "${YELLOW}⏭️ Skipped IPv6 configuration fix${NC}"
        fi
    else
        echo -e "${GREEN}✅ No IPv6 configuration conflict detected${NC}"
    fi
    echo ""
}

optimize_power_efficiency() {
    echo -e "${BLUE}⚡ Analyzing power efficiency settings...${NC}"
    
    local power_disabled=$(grep -c "workqueue.power_efficient=0" /etc/default/grub || echo "0")
    
    if [[ $power_disabled -gt 0 ]]; then
        echo -e "${YELLOW}⚠️ Power efficiency is disabled in kernel${NC}"
        echo -e "${YELLOW}Current setting: workqueue.power_efficient=0${NC}"
        echo -e "${YELLOW}Enabling this can improve battery life${NC}"
        
        read -p "Enable kernel power efficiency? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            backup_file "/etc/default/grub"
            sudo sed -i 's/workqueue.power_efficient=0/workqueue.power_efficient=1/' /etc/default/grub
            log "Enabled power efficiency in GRUB configuration"
            echo -e "${GREEN}✅ Power efficiency enabled in kernel parameters${NC}"
            echo -e "${YELLOW}⚠️ Reboot required for changes to take effect${NC}"
        else
            echo -e "${YELLOW}⏭️ Skipped power efficiency optimization${NC}"
        fi
    else
        echo -e "${GREEN}✅ Power efficiency already optimized or not explicitly disabled${NC}"
    fi
    echo ""
}

remove_usb_debug() {
    echo -e "${BLUE}🔍 Checking for unnecessary USB-C debug mode...${NC}"
    
    local debug_enabled=$(grep -c "ucsi_acpi.dyndbg=+p" /etc/default/grub || echo "0")
    
    if [[ $debug_enabled -gt 0 ]]; then
        echo -e "${YELLOW}⚠️ USB-C debug mode is enabled${NC}"
        echo -e "${YELLOW}Current setting: ucsi_acpi.dyndbg=+p${NC}"
        echo -e "${YELLOW}This increases kernel log verbosity unnecessarily${NC}"
        
        read -p "Remove USB-C debug mode? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            backup_file "/etc/default/grub"
            sudo sed -i 's/ucsi_acpi.dyndbg=+p //' /etc/default/grub
            log "Removed USB-C debug mode from GRUB configuration"
            echo -e "${GREEN}✅ USB-C debug mode removed${NC}"
            echo -e "${YELLOW}⚠️ Reboot required for changes to take effect${NC}"
        else
            echo -e "${YELLOW}⏭️ Skipped USB-C debug mode removal${NC}"
        fi
    else
        echo -e "${GREEN}✅ No unnecessary USB-C debug mode detected${NC}"
    fi
    echo ""
}

update_grub_if_needed() {
    local grub_modified=$(find /etc/default/grub -newer /boot/grub/grub.cfg 2>/dev/null | wc -l)
    
    if [[ $grub_modified -gt 0 ]]; then
        echo -e "${BLUE}🔄 GRUB configuration was modified...${NC}"
        echo -e "${YELLOW}Running update-grub to apply changes...${NC}"
        
        sudo update-grub
        log "Updated GRUB configuration"
        echo -e "${GREEN}✅ GRUB configuration updated${NC}"
        echo -e "${YELLOW}⚠️ Reboot required for kernel parameter changes to take effect${NC}"
    fi
}

show_configuration_summary() {
    echo -e "${BLUE}📊 Current /etc/default/ Configuration Summary${NC}"
    echo ""
    
    echo -e "${YELLOW}Key Configurations:${NC}"
    echo "  IPv6 Status:"
    if grep -q "ipv6.disable=1" /etc/default/grub; then
        echo "    Kernel: Disabled"
    else
        echo "    Kernel: Enabled"
    fi
    
    if grep -q "IPV6=yes" /etc/default/ufw; then
        echo "    UFW: Enabled"
    else
        echo "    UFW: Disabled"
    fi
    
    echo ""
    echo "  Power Management:"
    if grep -q "workqueue.power_efficient=0" /etc/default/grub; then
        echo "    Kernel Power Efficiency: Disabled"
    elif grep -q "workqueue.power_efficient=1" /etc/default/grub; then
        echo "    Kernel Power Efficiency: Enabled"
    else
        echo "    Kernel Power Efficiency: Default"
    fi
    
    echo ""
    echo "  Debug Settings:"
    if grep -q "ucsi_acpi.dyndbg=+p" /etc/default/grub; then
        echo "    USB-C Debug: Enabled"
    else
        echo "    USB-C Debug: Disabled"
    fi
    
    echo ""
}

show_summary() {
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}     Optimization Summary Complete     ${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo ""
    echo -e "${GREEN}✅ /etc/default/ configuration analysis completed${NC}"
    echo -e "${GREEN}✅ Optimization opportunities addressed${NC}"
    echo -e "${GREEN}✅ Configuration consistency improved${NC}"
    echo ""
    echo -e "${BLUE}Log file: $LOG_FILE${NC}"
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo -e "${YELLOW}  1. Reboot if GRUB changes were made${NC}"
    echo -e "${YELLOW}  2. Verify configurations after reboot${NC}"
    echo -e "${YELLOW}  3. Monitor system behavior for improvements${NC}"
    echo ""
}

main() {
    print_header
    check_permissions
    
    log "Starting /etc/default/ configuration optimization"
    
    # Run optimization checks
    fix_ipv6_configuration
    optimize_power_efficiency
    remove_usb_debug
    
    # Update GRUB if needed
    update_grub_if_needed
    
    # Show summary
    show_configuration_summary
    
    log "/etc/default/ configuration optimization completed"
    show_summary
}

# Run main function
main "$@"
