#!/usr/bin/env python3
"""
Power Management Monitor
Real-time monitoring of power management status and performance.
"""

import subprocess
import time
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any


def run_command(cmd: str) -> str:
    """Run command and return output."""
    try:
        result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=5)
        return result.stdout.strip() if result.returncode == 0 else "N/A"
    except:
        return "N/A"


def get_power_metrics() -> Dict[str, Any]:
    """Collect current power management metrics."""
    metrics = {
        "timestamp": datetime.now().isoformat(),
        "power_source": "unknown",
        "cpu_governor": "unknown",
        "cpu_freq": "unknown",
        "cpu_usage": "unknown",
        "temperature": "unknown",
        "battery_level": "unknown",
        "power_consumption": "unknown",
        "tlp_mode": "unknown"
    }
    
    # Power source (AC/Battery)
    ac_online = run_command("cat /sys/class/power_supply/ADP1/online")
    metrics["power_source"] = "AC" if ac_online == "1" else "Battery"
    
    # CPU Governor
    try:
        with open("/sys/devices/system/cpu/cpu0/cpufreq/scaling_governor", "r") as f:
            metrics["cpu_governor"] = f.read().strip()
    except:
        pass
    
    # CPU Frequency
    try:
        with open("/sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq", "r") as f:
            freq_khz = int(f.read().strip())
            metrics["cpu_freq"] = f"{freq_khz / 1000:.0f} MHz"
    except:
        pass
    
    # CPU Usage
    cpu_usage = run_command("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1")
    if cpu_usage != "N/A":
        metrics["cpu_usage"] = f"{cpu_usage}%"
    
    # Temperature
    temp = run_command("cat /sys/class/thermal/thermal_zone0/temp")
    if temp != "N/A" and temp.isdigit():
        metrics["temperature"] = f"{int(temp) / 1000:.1f}°C"
    
    # Battery level
    try:
        with open("/sys/class/power_supply/BAT0/capacity", "r") as f:
            metrics["battery_level"] = f"{f.read().strip()}%"
    except:
        pass
    
    # TLP Mode
    tlp_mode = run_command("sudo tlp-stat -s | grep 'Mode' | awk '{print $3}'")
    if tlp_mode != "N/A":
        metrics["tlp_mode"] = tlp_mode
    
    return metrics


def display_metrics(metrics: Dict[str, Any]):
    """Display metrics in a formatted way."""
    print(f"\n⚡ Power Management Status - {metrics['timestamp'][:19]}")
    print("=" * 60)
    
    # Power source with appropriate icon
    source_icon = "🔌" if metrics["power_source"] == "AC" else "🔋"
    print(f"{source_icon} Power Source:    {metrics['power_source']}")
    
    # CPU information
    print(f"🖥️  CPU Governor:     {metrics['cpu_governor']}")
    print(f"⚡ CPU Frequency:   {metrics['cpu_freq']}")
    print(f"📊 CPU Usage:       {metrics['cpu_usage']}")
    
    # Thermal
    print(f"🌡️  Temperature:     {metrics['temperature']}")
    
    # Battery
    if metrics["battery_level"] != "unknown":
        battery_level = int(metrics["battery_level"].rstrip('%'))
        battery_icon = "🔋" if battery_level > 20 else "🪫"
        print(f"{battery_icon} Battery Level:   {metrics['battery_level']}")
    
    # TLP Mode
    if metrics["tlp_mode"] != "unknown":
        mode_icon = "⚡" if "AC" in metrics["tlp_mode"] else "🔋"
        print(f"{mode_icon} TLP Mode:        {metrics['tlp_mode']}")


def save_metrics_log(metrics: Dict[str, Any], log_file: Path):
    """Save metrics to log file."""
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    # Append to JSON log
    with open(log_file, "a") as f:
        f.write(json.dumps(metrics) + "\n")


def monitor_continuous(interval: int = 5, log_file: Path = None):
    """Monitor power management continuously."""
    print("🔋 Starting Power Management Monitor")
    print(f"📊 Update interval: {interval} seconds")
    if log_file:
        print(f"📝 Logging to: {log_file}")
    print("Press Ctrl+C to stop")
    
    try:
        while True:
            metrics = get_power_metrics()
            display_metrics(metrics)
            
            if log_file:
                save_metrics_log(metrics, log_file)
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n\n👋 Monitoring stopped")


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Power Management Monitor")
    parser.add_argument("--interval", "-i", type=int, default=5, 
                       help="Update interval in seconds (default: 5)")
    parser.add_argument("--log", "-l", type=str, 
                       help="Log file path (optional)")
    parser.add_argument("--once", action="store_true", 
                       help="Run once and exit")
    
    args = parser.parse_args()
    
    log_file = Path(args.log) if args.log else None
    
    if args.once:
        metrics = get_power_metrics()
        display_metrics(metrics)
        if log_file:
            save_metrics_log(metrics, log_file)
    else:
        monitor_continuous(args.interval, log_file)


if __name__ == "__main__":
    main()
