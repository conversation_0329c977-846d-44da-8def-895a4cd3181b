#!/bin/bash
# Simplified Authentication Flow Setup
# Sets up basic authentication monitoring and standardization

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AUTH_CONFIG_DIR="$HOME/.config/auth-flow"
LOG_FILE="$HOME/.cache/auth-flow.log"

# Functions
show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
}

setup_directories() {
    show_status "Setting up directories..."
    
    mkdir -p "$AUTH_CONFIG_DIR"
    chmod 700 "$AUTH_CONFIG_DIR"
    
    mkdir -p "$(dirname "$LOG_FILE")"
    
    show_success "Directories created"
}

create_auth_status_script() {
    show_status "Creating authentication status script..."
    
    local status_script="$AUTH_CONFIG_DIR/auth-status.sh"
    
    cat > "$status_script" << 'EOF'
#!/bin/bash
# Authentication status display

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔐 Authentication Status${NC}"
echo "======================="
echo ""

# Keyring status
echo -e "${YELLOW}Keyring Status:${NC}"
if secret-tool search keyring login >/dev/null 2>&1; then
    echo -e "  Login keyring: ${GREEN}✅ Unlocked${NC}"
else
    echo -e "  Login keyring: ${RED}🔒 Locked${NC}"
fi

if secret-tool search keyring convenience >/dev/null 2>&1; then
    echo -e "  Convenience keyring: ${GREEN}✅ Unlocked${NC}"
else
    echo -e "  Convenience keyring: ${YELLOW}⚠️  Not accessible${NC}"
fi

echo ""

# Authentication methods
echo -e "${YELLOW}Available Authentication Methods:${NC}"

# Fingerprint
if systemctl is-active fprintd >/dev/null 2>&1; then
    if fprintd-list "$USER" >/dev/null 2>&1; then
        echo -e "  Fingerprint: ${GREEN}✅ Available and enrolled${NC}"
    else
        echo -e "  Fingerprint: ${YELLOW}⚠️  Available but not enrolled${NC}"
    fi
else
    echo -e "  Fingerprint: ${RED}❌ Service not running${NC}"
fi

# TPM
if [ -e /dev/tpm0 ]; then
    if [ -f "$HOME/.config/tpm-keyring/keyring-password.enc" ]; then
        echo -e "  TPM: ${GREEN}✅ Available and configured${NC}"
    else
        echo -e "  TPM: ${YELLOW}⚠️  Available but not configured${NC}"
    fi
else
    echo -e "  TPM: ${RED}❌ Hardware not available${NC}"
fi

# Password
echo -e "  Password: ${GREEN}✅ Always available${NC}"

echo ""

# Recent authentication events
echo -e "${YELLOW}Recent Authentication Events:${NC}"
LOG_FILE="$HOME/.cache/auth-flow.log"
if [ -f "$LOG_FILE" ]; then
    tail -5 "$LOG_FILE" | while read line; do
        echo "  $line"
    done
else
    echo "  No authentication events logged"
fi
EOF
    
    chmod 755 "$status_script"
    show_success "Authentication status script created"
}

create_monitoring_script() {
    show_status "Creating monitoring script..."
    
    local monitor_script="$AUTH_CONFIG_DIR/monitor.sh"
    
    cat > "$monitor_script" << 'EOF'
#!/bin/bash
# Authentication monitoring

LOG_FILE="$HOME/.cache/auth-flow.log"

log_auth_event() {
    local method="$1"
    local status="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "$timestamp - AUTH: method=$method status=$status" >> "$LOG_FILE"
}

case "${1:-}" in
    fingerprint)
        log_auth_event "fingerprint" "${2:-success}"
        ;;
    password)
        log_auth_event "password" "${2:-success}"
        ;;
    tpm)
        log_auth_event "tpm" "${2:-success}"
        ;;
    *)
        log_auth_event "unknown" "${2:-unknown}"
        ;;
esac
EOF
    
    chmod 755 "$monitor_script"
    show_success "Monitoring script created"
}

create_keyring_helper() {
    show_status "Creating keyring helper script..."
    
    local helper_script="$AUTH_CONFIG_DIR/keyring-helper.sh"
    
    cat > "$helper_script" << 'EOF'
#!/bin/bash
# Keyring unlock helper

LOG_FILE="$HOME/.cache/auth-flow.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - KEYRING: $1" >> "$LOG_FILE"
}

# Check if keyring is already unlocked
if secret-tool search keyring login >/dev/null 2>&1; then
    log_message "Keyring already unlocked"
    exit 0
fi

# Try TPM-based unlock if available
TPM_UNLOCK_SCRIPT="$HOME/.config/tmp-keyring/tpm-unlock-keyring.sh"
if [ -f "$TPM_UNLOCK_SCRIPT" ]; then
    log_message "Attempting TPM-based keyring unlock"
    if "$TPM_UNLOCK_SCRIPT" >/dev/null 2>&1; then
        log_message "TPM-based keyring unlock successful"
        exit 0
    else
        log_message "TPM-based keyring unlock failed"
    fi
fi

log_message "Keyring unlock attempt completed"
EOF
    
    chmod 755 "$helper_script"
    show_success "Keyring helper script created"
}

test_setup() {
    show_status "Testing authentication flow setup..."
    
    # Test keyring access
    if secret-tool search keyring login >/dev/null 2>&1; then
        show_success "Login keyring is accessible"
    else
        show_warning "Login keyring is not accessible"
    fi
    
    # Test fingerprint service
    if systemctl is-active fprintd >/dev/null 2>&1; then
        show_success "Fingerprint service is active"
    else
        show_warning "Fingerprint service is not active"
    fi
    
    # Test scripts
    if [ -f "$AUTH_CONFIG_DIR/auth-status.sh" ]; then
        show_success "Authentication status script created"
    else
        show_error "Authentication status script missing"
    fi
    
    show_success "Authentication flow test completed"
}

show_usage() {
    echo "Simplified Authentication Flow Setup"
    echo "==================================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     - Setup authentication flow"
    echo "  status    - Show authentication status"
    echo "  test      - Test authentication setup"
    echo "  remove    - Remove setup"
    echo ""
}

cmd_setup() {
    echo "🔄 Authentication Flow Setup"
    echo "============================"
    echo ""
    
    setup_directories
    create_auth_status_script
    create_monitoring_script
    create_keyring_helper
    
    show_success "Authentication flow setup complete!"
    echo ""
    echo "Created components:"
    echo "  - Authentication status script"
    echo "  - Monitoring script"
    echo "  - Keyring helper script"
    echo ""
    echo "Usage:"
    echo "  Check status: $AUTH_CONFIG_DIR/auth-status.sh"
    echo "  Monitor logs: tail -f $LOG_FILE"
}

cmd_status() {
    if [ -f "$AUTH_CONFIG_DIR/auth-status.sh" ]; then
        "$AUTH_CONFIG_DIR/auth-status.sh"
    else
        show_error "Authentication status script not found. Run setup first."
        exit 1
    fi
}

cmd_test() {
    test_setup
}

cmd_remove() {
    echo "🗑️  Removing Authentication Flow Setup"
    echo "====================================="
    echo ""
    
    read -p "Are you sure you want to remove authentication flow setup? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Operation cancelled."
        exit 0
    fi
    
    rm -rf "$AUTH_CONFIG_DIR"
    rm -f "$LOG_FILE"
    
    show_success "Authentication flow setup removed"
}

# Main command handling
case "${1:-}" in
    setup)
        cmd_setup
        ;;
    status)
        cmd_status
        ;;
    test)
        cmd_test
        ;;
    remove)
        cmd_remove
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
