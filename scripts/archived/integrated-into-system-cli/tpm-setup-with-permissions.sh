#!/bin/bash
# TPM Setup with Permissions Fix
# Handles TPM group membership and permissions

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
}

check_tpm_access() {
    show_status "Checking TPM access..."
    
    # Check if user is in tss group
    if groups "$USER" | grep -q "\btss\b"; then
        show_success "User is in tss group"
    else
        show_error "User is not in tss group"
        return 1
    fi
    
    # Test TPM access with newgrp
    if newgrp tss -c "test -r /dev/tpmrm0 && test -w /dev/tpmrm0" 2>/dev/null; then
        show_success "TPM device is accessible"
        return 0
    else
        show_warning "TPM device access test failed, but group membership is correct"
        show_status "This may require a logout/login or system restart"
        return 1
    fi
}

setup_tpm_with_newgrp() {
    show_status "Setting up TPM integration with proper group context..."
    
    # Create a wrapper script that runs in the tss group context
    local wrapper_script="/tmp/tpm-setup-wrapper.sh"
    
    cat > "$wrapper_script" << 'EOF'
#!/bin/bash
# TPM setup wrapper that runs in tss group context

set -euo pipefail

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}Running TPM setup in tss group context...${NC}"

# Test TPM access first
if ! test -r /dev/tpmrm0 || ! test -w /dev/tpmrm0; then
    echo -e "${RED}❌ TPM device not accessible even in tss group${NC}"
    echo "This may require a system restart or logout/login"
    exit 1
fi

echo -e "${GREEN}✅ TPM device is accessible${NC}"

# Run the original TPM integration script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TPM_SCRIPT="$HOME/cursor-system/scripts/tpm-keyring-integration.sh"

if [ -f "$TPM_SCRIPT" ]; then
    echo -e "${BLUE}Running TPM integration setup...${NC}"
    "$TPM_SCRIPT" setup
else
    echo -e "${RED}❌ TPM integration script not found at $TPM_SCRIPT${NC}"
    exit 1
fi
EOF
    
    chmod +x "$wrapper_script"
    
    # Run the wrapper script in the tss group context
    if newgrp tss -c "$wrapper_script"; then
        show_success "TPM integration setup completed successfully"
        rm -f "$wrapper_script"
        return 0
    else
        show_error "TPM integration setup failed"
        rm -f "$wrapper_script"
        return 1
    fi
}

alternative_setup() {
    show_status "Attempting alternative TPM setup..."
    
    # Alternative approach: modify the clevis command to use sudo
    show_status "Creating TPM integration with sudo access..."
    
    # Create a modified version of the TPM script that uses sudo for TPM operations
    local modified_script="$HOME/.config/tpm-keyring/tpm-setup-sudo.sh"
    mkdir -p "$(dirname "$modified_script")"
    
    cat > "$modified_script" << 'EOF'
#!/bin/bash
# Modified TPM setup that uses sudo for TPM operations

set -euo pipefail

TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
CLEVIS_CONFIG_FILE="$TPM_KEYRING_DIR/clevis-config.json"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"

# Setup directories
mkdir -p "$TPM_KEYRING_DIR"
chmod 700 "$TPM_KEYRING_DIR"

# Get keyring password
echo -n "Enter your keyring password: "
read -s password
echo

if [ -z "$password" ]; then
    echo "❌ Password cannot be empty"
    exit 1
fi

# Test password
if echo "$password" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
    echo "✅ Password verified"
else
    echo "❌ Invalid password or keyring unlock failed"
    exit 1
fi

# Create clevis configuration
clevis_config='{"pcr_ids":"0,1,7"}'
echo "$clevis_config" > "$CLEVIS_CONFIG_FILE"

# Encrypt password using clevis-tpm2 with sudo
echo "🔐 Encrypting password with TPM..."
if echo "$password" | sudo clevis encrypt tpm2 "$clevis_config" > "$KEYRING_PASSWORD_FILE"; then
    chmod 600 "$KEYRING_PASSWORD_FILE"
    echo "✅ Password encrypted and stored securely"
    
    # Test decryption
    echo "🧪 Testing decryption..."
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" >/dev/null 2>&1; then
        echo "✅ TPM decryption test successful"
        echo "✅ TPM-keyring integration setup complete!"
    else
        echo "❌ TPM decryption test failed"
        exit 1
    fi
else
    echo "❌ Failed to encrypt password with TPM"
    exit 1
fi
EOF
    
    chmod +x "$modified_script"
    
    if "$modified_script"; then
        show_success "Alternative TPM setup completed"
        return 0
    else
        show_error "Alternative TPM setup failed"
        return 1
    fi
}

main() {
    echo "🔧 TPM Setup with Permissions Fix"
    echo "================================="
    echo ""
    
    if check_tpm_access; then
        # TPM access is working, try normal setup
        if setup_tpm_with_newgrp; then
            show_success "TPM setup completed successfully!"
            exit 0
        fi
    fi
    
    # If normal setup fails, try alternative
    show_warning "Normal TPM setup failed, trying alternative approach..."
    echo ""
    
    if alternative_setup; then
        show_success "TPM setup completed using alternative method!"
        echo ""
        echo "Note: This setup uses sudo for TPM operations."
        echo "You may need to enter your password when unlocking the keyring."
    else
        show_error "All TPM setup methods failed"
        echo ""
        echo "Possible solutions:"
        echo "1. Restart your system to activate tss group membership"
        echo "2. Log out and log back in"
        echo "3. Check TPM hardware status"
        exit 1
    fi
}

main "$@"
