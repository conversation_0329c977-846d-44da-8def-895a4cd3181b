#!/bin/bash
# Simple TPM Setup with Visible Prompts
# Hardware-backed keyring security using TPM 2.0

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"
LOG_FILE="$HOME/.cache/tpm-keyring.log"

show_header() {
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}$(echo "$1" | sed 's/./=/g')${NC}"
}

show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

show_success() {
    echo -e "${GRE<PERSON>}✅ $1${NC}"
}

show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
}

show_prompt() {
    echo -e "${CYAN}👤 $1${NC}"
}

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE" 2>/dev/null || true
}

get_keyring_password_simple() {
    show_header "Keyring Password Required"
    echo ""
    echo "We need your keyring password to set up TPM integration."
    echo "This password will be encrypted using your TPM hardware."
    echo ""
    show_warning "Your password will be stored securely and encrypted by TPM"
    echo ""
    
    # Use a simple approach that works better in terminals
    show_prompt "Enter your keyring password and press ENTER:"
    echo -n "Password: "
    
    # Use stty to hide input instead of read -s
    stty -echo
    read password
    stty echo
    echo ""
    
    if [ -z "$password" ]; then
        show_error "Password cannot be empty"
        return 1
    fi
    
    # Test password
    show_status "Testing password with keyring..."
    if echo "$password" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        show_success "Password verified successfully"
        echo "$password"
        return 0
    else
        show_error "Password verification failed"
        return 1
    fi
}

setup_tpm_simple() {
    show_header "🔒 Simple TPM Integration Setup"
    echo ""
    
    # Create directories
    show_status "Creating TPM directories..."
    mkdir -p "$TPM_KEYRING_DIR"
    chmod 700 "$TPM_KEYRING_DIR"
    mkdir -p "$(dirname "$LOG_FILE")"
    show_success "Directories created"
    
    echo ""
    
    # Get password
    if password=$(get_keyring_password_simple); then
        echo ""
        show_status "Encrypting password with TPM..."
        show_warning "You may be prompted for sudo password for TPM access"
        echo ""
        
        # Create clevis config and encrypt
        local clevis_config='{"pcr_ids":"0,1,7"}'
        
        if echo "$password" | sudo clevis encrypt tpm2 "$clevis_config" > "$KEYRING_PASSWORD_FILE" 2>/dev/null; then
            chmod 600 "$KEYRING_PASSWORD_FILE"
            show_success "Password encrypted with TPM successfully"
            
            echo ""
            show_status "Testing TPM decryption..."
            
            if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" >/dev/null 2>&1; then
                show_success "TPM decryption test successful"
                
                # Create unlock script
                create_unlock_script_simple
                
                echo ""
                show_success "🎉 TPM integration setup complete!"
                echo ""
                echo "Created:"
                echo "  📁 Config directory: $TPM_KEYRING_DIR"
                echo "  🔐 Encrypted password: $(basename "$KEYRING_PASSWORD_FILE")"
                echo "  🔓 Unlock script: tpm-unlock.sh"
                echo ""
                echo "Test with: $0 test"
                echo "Status: $0 status"
                
            else
                show_error "TPM decryption test failed"
                return 1
            fi
        else
            show_error "Failed to encrypt password with TPM"
            return 1
        fi
    else
        show_error "Failed to get valid password"
        return 1
    fi
}

create_unlock_script_simple() {
    local unlock_script="$TPM_KEYRING_DIR/tpm-unlock.sh"
    
    cat > "$unlock_script" << 'EOF'
#!/bin/bash
# Simple TPM keyring unlock

TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"

echo "🔓 TPM Keyring Unlock"

# Check if already unlocked
if secret-tool search keyring login >/dev/null 2>&1; then
    echo "✅ Keyring already unlocked"
    exit 0
fi

# Try TPM unlock
if [ -f "$KEYRING_PASSWORD_FILE" ]; then
    echo "🔒 Unlocking with TPM..."
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        echo "✅ TPM unlock successful"
        exit 0
    else
        echo "❌ TPM unlock failed"
        exit 1
    fi
else
    echo "❌ TPM password file not found"
    exit 1
fi
EOF
    
    chmod 755 "$unlock_script"
    show_success "Unlock script created"
}

test_tpm() {
    show_header "🧪 Testing TPM Integration"
    echo ""
    
    if [ ! -f "$KEYRING_PASSWORD_FILE" ]; then
        show_error "TPM integration not setup. Run: $0 setup"
        return 1
    fi
    
    show_status "Testing TPM decryption..."
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" >/dev/null 2>&1; then
        show_success "TPM decryption working"
        
        echo ""
        show_status "Testing keyring unlock..."
        if "$TPM_KEYRING_DIR/tpm-unlock.sh"; then
            show_success "Complete TPM integration test passed"
        else
            show_warning "TPM decryption works but keyring unlock failed"
        fi
    else
        show_error "TPM decryption failed"
        return 1
    fi
}

show_status_simple() {
    show_header "📊 TPM Integration Status"
    echo ""
    
    # Check hardware
    if [ -e /dev/tpm0 ]; then
        echo "✅ TPM Hardware: Available"
    else
        echo "❌ TPM Hardware: Not found"
    fi
    
    # Check group
    if groups "$USER" | grep -q "\btss\b"; then
        echo "✅ User Access: In tss group"
    else
        echo "❌ User Access: Not in tss group"
    fi
    
    # Check setup
    if [ -f "$KEYRING_PASSWORD_FILE" ]; then
        echo "✅ TPM Setup: Complete"
        echo "  📁 Config: $TPM_KEYRING_DIR"
        echo "  🔐 Password file: $(du -h "$KEYRING_PASSWORD_FILE" | cut -f1)"
    else
        echo "❌ TPM Setup: Not configured"
    fi
    
    # Check unlock script
    if [ -f "$TPM_KEYRING_DIR/tpm-unlock.sh" ]; then
        echo "✅ Unlock Script: Available"
    else
        echo "❌ Unlock Script: Not found"
    fi
    
    echo ""
    echo "Commands:"
    echo "  Setup: $0 setup"
    echo "  Test:  $0 test"
    echo "  Unlock: $TPM_KEYRING_DIR/tpm-unlock.sh"
}

show_usage() {
    echo "Simple TPM Integration"
    echo "====================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup   - Setup TPM integration (with visible prompts)"
    echo "  test    - Test TPM integration"
    echo "  status  - Show TPM status"
    echo "  remove  - Remove TPM integration"
    echo ""
}

case "${1:-}" in
    setup)
        setup_tpm_simple
        ;;
    test)
        test_tpm
        ;;
    status)
        show_status_simple
        ;;
    remove)
        echo "🗑️  Removing TPM integration..."
        rm -rf "$TPM_KEYRING_DIR"
        rm -f "$LOG_FILE"
        echo "✅ TPM integration removed"
        ;;
    *)
        show_usage
        ;;
esac
