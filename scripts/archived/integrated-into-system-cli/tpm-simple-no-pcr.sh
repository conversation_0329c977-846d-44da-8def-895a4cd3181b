#!/bin/bash
# Simple TPM Setup Without PCR Binding
# Hardware-backed keyring security using TPM 2.0 (no PCR complications)

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"

show_header() {
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}$(echo "$1" | sed 's/./=/g')${NC}"
}

show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
}

setup_tpm_simple() {
    show_header "🔒 Simple TPM Integration (No PCR)"
    echo ""
    echo "This sets up TPM-based keyring encryption without PCR binding."
    echo "This is more compatible but slightly less secure than PCR binding."
    echo ""
    
    # Create directories
    show_status "Creating directories..."
    mkdir -p "$TPM_KEYRING_DIR"
    chmod 700 "$TPM_KEYRING_DIR"
    show_success "Directories created"
    
    echo ""
    echo "=========================================="
    echo "KEYRING PASSWORD REQUIRED"
    echo "=========================================="
    echo ""
    echo "Please enter your keyring password:"
    echo "(This will be encrypted using TPM hardware)"
    echo ""
    echo -n "Keyring Password: "
    
    # Read password
    read -s password
    echo ""
    
    if [ -z "$password" ]; then
        show_error "Password cannot be empty"
        exit 1
    fi
    
    echo ""
    show_status "Testing password with keyring..."
    
    # Test the password
    if echo "$password" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        show_success "Password verified successfully"
    else
        show_error "Password verification failed"
        exit 1
    fi
    
    echo ""
    show_status "Encrypting password with TPM (no PCR binding)..."
    echo "This may prompt for sudo password..."
    echo ""
    
    # Use clevis without PCR binding (simpler approach)
    local clevis_config='{}'
    
    if echo "$password" | sudo clevis encrypt tpm2 "$clevis_config" > "$KEYRING_PASSWORD_FILE" 2>/dev/null; then
        chmod 600 "$KEYRING_PASSWORD_FILE"
        show_success "Password encrypted with TPM successfully"
        
        echo ""
        show_status "Testing TPM decryption..."
        
        if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" >/dev/null 2>&1; then
            show_success "TPM decryption test successful"
            
            # Create unlock script
            create_unlock_script
            
            echo ""
            show_success "🎉 TPM integration setup complete!"
            echo ""
            echo "What was created:"
            echo "  📁 Config directory: $TPM_KEYRING_DIR"
            echo "  🔐 Encrypted password file"
            echo "  🔓 Unlock script: tpm-unlock.sh"
            echo ""
            echo "Test it:"
            echo "  $0 test"
            echo "  $0 unlock"
            echo ""
            echo "Note: This setup uses TPM without PCR binding for maximum compatibility."
            
        else
            show_error "TPM decryption test failed"
            exit 1
        fi
    else
        show_error "Failed to encrypt password with TPM"
        echo ""
        echo "This may be due to TPM configuration issues."
        echo "Try checking: sudo tpm2_startup -c"
        exit 1
    fi
}

create_unlock_script() {
    local unlock_script="$TPM_KEYRING_DIR/tpm-unlock.sh"
    
    cat > "$unlock_script" << 'EOF'
#!/bin/bash
# Simple TPM keyring unlock

TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"

echo "🔓 TPM Keyring Unlock"

# Check if already unlocked
if secret-tool search keyring login >/dev/null 2>&1; then
    echo "✅ Keyring already unlocked"
    exit 0
fi

# Try TPM unlock
if [ -f "$KEYRING_PASSWORD_FILE" ]; then
    echo "🔒 Unlocking keyring with TPM..."
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        echo "✅ TPM keyring unlock successful"
        exit 0
    else
        echo "❌ TPM keyring unlock failed"
        exit 1
    fi
else
    echo "❌ TPM password file not found"
    exit 1
fi
EOF
    
    chmod 755 "$unlock_script"
    show_success "Unlock script created"
}

test_tpm() {
    show_header "🧪 Testing Simple TPM Integration"
    echo ""
    
    if [ ! -f "$KEYRING_PASSWORD_FILE" ]; then
        show_error "TPM integration not setup"
        echo "Run: $0 setup"
        exit 1
    fi
    
    show_status "Testing TPM decryption..."
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" >/dev/null 2>&1; then
        show_success "TPM decryption working"
        
        echo ""
        show_status "Testing keyring unlock..."
        if "$TPM_KEYRING_DIR/tpm-unlock.sh"; then
            show_success "Complete TPM integration test passed"
        else
            echo "⚠️  TPM decryption works but keyring unlock had issues"
        fi
    else
        show_error "TPM decryption failed"
        exit 1
    fi
}

unlock_keyring() {
    if [ -f "$TPM_KEYRING_DIR/tpm-unlock.sh" ]; then
        "$TPM_KEYRING_DIR/tpm-unlock.sh"
    else
        show_error "TPM unlock script not found"
        echo "Run: $0 setup"
        exit 1
    fi
}

show_status() {
    show_header "📊 Simple TPM Integration Status"
    echo ""
    
    # Check hardware
    if [ -e /dev/tpm0 ]; then
        echo "✅ TPM Hardware: Available (STM TPM 2.0)"
    else
        echo "❌ TPM Hardware: Not found"
    fi
    
    # Check setup
    if [ -f "$KEYRING_PASSWORD_FILE" ]; then
        echo "✅ TPM Setup: Complete (no PCR binding)"
        echo "  📁 Directory: $TPM_KEYRING_DIR"
        echo "  🔐 Password file: $(du -h "$KEYRING_PASSWORD_FILE" | cut -f1)"
        
        if [ -f "$TPM_KEYRING_DIR/tpm-unlock.sh" ]; then
            echo "  🔓 Unlock script: Available"
        else
            echo "  ❌ Unlock script: Missing"
        fi
    else
        echo "❌ TPM Setup: Not configured"
    fi
    
    echo ""
    echo "Commands:"
    echo "  $0 setup   - Setup simple TPM integration"
    echo "  $0 test    - Test TPM functionality"
    echo "  $0 unlock  - Unlock keyring with TPM"
    echo "  $0 remove  - Remove TPM integration"
}

remove_tpm() {
    show_header "🗑️  Removing TPM Integration"
    echo ""
    echo -n "Remove TPM integration? (y/N): "
    read -n 1 confirm
    echo ""
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        rm -rf "$TPM_KEYRING_DIR"
        show_success "TPM integration removed"
    else
        echo "Operation cancelled"
    fi
}

show_usage() {
    echo "Simple TPM Integration (No PCR)"
    echo "=============================="
    echo ""
    echo "This script sets up TPM-based keyring encryption without PCR binding"
    echo "for maximum compatibility with your STM TPM 2.0 hardware."
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup   - Interactive TPM setup (simplified)"
    echo "  test    - Test TPM integration"
    echo "  unlock  - Unlock keyring using TPM"
    echo "  status  - Show TPM integration status"
    echo "  remove  - Remove TPM integration"
    echo ""
}

# Main command handling
case "${1:-}" in
    setup)
        setup_tpm_simple
        ;;
    test)
        test_tpm
        ;;
    unlock)
        unlock_keyring
        ;;
    status)
        show_status
        ;;
    remove)
        remove_tpm
        ;;
    *)
        show_usage
        ;;
esac
