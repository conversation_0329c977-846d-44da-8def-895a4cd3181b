#!/bin/bash
# Fixed TPM Setup with Guaranteed Visible Prompts
# Hardware-backed keyring security using TPM 2.0

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"

show_header() {
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}$(echo "$1" | sed 's/./=/g')${NC}"
}

show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
}

show_prompt() {
    echo -e "${CYAN}👤 $1${NC}"
}

setup_tpm_interactive() {
    show_header "🔒 TPM Integration Setup"
    echo ""
    echo "This will encrypt your keyring password using TPM hardware."
    echo ""
    
    # Create directories
    show_status "Creating directories..."
    mkdir -p "$TPM_KEYRING_DIR"
    chmod 700 "$TPM_KEYRING_DIR"
    show_success "Directories created"
    
    echo ""
    echo "=========================================="
    echo "KEYRING PASSWORD REQUIRED"
    echo "=========================================="
    echo ""
    echo "We need your keyring password to encrypt it with TPM."
    echo "This is the password you use to unlock your keyring."
    echo ""
    echo "⚠️  Your password will be encrypted and stored securely"
    echo "⚠️  You can remove this setup anytime with: $0 remove"
    echo ""
    
    # Force a visible prompt
    echo "Please enter your keyring password below:"
    echo -n "Keyring Password: "
    
    # Read password with echo disabled
    read -s password
    echo ""
    
    if [ -z "$password" ]; then
        show_error "Password cannot be empty"
        echo ""
        echo "Please run the script again and enter your password."
        exit 1
    fi
    
    echo ""
    show_status "Testing password with keyring..."
    
    # Test the password
    if echo "$password" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        show_success "Password verified successfully"
    else
        show_error "Password verification failed"
        echo ""
        echo "The password you entered doesn't unlock your keyring."
        echo "Please run the script again with the correct password."
        exit 1
    fi
    
    echo ""
    show_status "Encrypting password with TPM..."
    echo "You may be prompted for sudo password for TPM access..."
    echo ""
    
    # Create clevis config and encrypt
    local clevis_config='{"pcr_ids":"0,1,7"}'
    
    if echo "$password" | sudo clevis encrypt tpm2 "$clevis_config" > "$KEYRING_PASSWORD_FILE" 2>/dev/null; then
        chmod 600 "$KEYRING_PASSWORD_FILE"
        show_success "Password encrypted with TPM successfully"
        
        echo ""
        show_status "Testing TPM decryption..."
        
        if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" >/dev/null 2>&1; then
            show_success "TPM decryption test successful"
            
            # Create simple unlock script
            create_unlock_script
            
            echo ""
            show_success "🎉 TPM integration setup complete!"
            echo ""
            echo "What was created:"
            echo "  📁 Config directory: $TPM_KEYRING_DIR"
            echo "  🔐 Encrypted password file"
            echo "  🔓 Unlock script: tpm-unlock.sh"
            echo ""
            echo "Test it with:"
            echo "  $0 test"
            echo "  $0 unlock"
            echo ""
            echo "Check status with:"
            echo "  $0 status"
            echo "  auth-monitor tpm"
            
        else
            show_error "TPM decryption test failed"
            echo ""
            echo "The password was encrypted but TPM decryption failed."
            echo "This may indicate TPM configuration issues."
            exit 1
        fi
    else
        show_error "Failed to encrypt password with TPM"
        echo ""
        echo "Possible causes:"
        echo "  - TPM hardware issues"
        echo "  - PCR configuration problems"
        echo "  - Insufficient permissions"
        echo ""
        echo "You can check TPM status with: sudo tpm2_getcap properties-fixed"
        exit 1
    fi
}

create_unlock_script() {
    local unlock_script="$TPM_KEYRING_DIR/tpm-unlock.sh"
    
    cat > "$unlock_script" << 'EOF'
#!/bin/bash
# TPM keyring unlock script

TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"

echo "🔓 TPM Keyring Unlock"

# Check if already unlocked
if secret-tool search keyring login >/dev/null 2>&1; then
    echo "✅ Keyring already unlocked"
    exit 0
fi

# Try TPM unlock
if [ -f "$KEYRING_PASSWORD_FILE" ]; then
    echo "🔒 Unlocking keyring with TPM..."
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        echo "✅ TPM keyring unlock successful"
        exit 0
    else
        echo "❌ TPM keyring unlock failed"
        exit 1
    fi
else
    echo "❌ TPM password file not found"
    exit 1
fi
EOF
    
    chmod 755 "$unlock_script"
    show_success "Unlock script created"
}

test_tpm() {
    show_header "🧪 Testing TPM Integration"
    echo ""
    
    if [ ! -f "$KEYRING_PASSWORD_FILE" ]; then
        show_error "TPM integration not setup"
        echo "Run: $0 setup"
        exit 1
    fi
    
    show_status "Testing TPM decryption..."
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" >/dev/null 2>&1; then
        show_success "TPM decryption working"
        
        echo ""
        show_status "Testing complete keyring unlock..."
        if "$TPM_KEYRING_DIR/tmp-unlock.sh"; then
            show_success "Complete TPM integration test passed"
        else
            echo "⚠️  TPM decryption works but keyring unlock had issues"
        fi
    else
        show_error "TPM decryption failed"
        echo "This may indicate TPM state changes or configuration issues"
        exit 1
    fi
}

unlock_keyring() {
    if [ -f "$TPM_KEYRING_DIR/tpm-unlock.sh" ]; then
        "$TPM_KEYRING_DIR/tpm-unlock.sh"
    else
        show_error "TPM unlock script not found"
        echo "Run: $0 setup"
        exit 1
    fi
}

show_status() {
    show_header "📊 TPM Integration Status"
    echo ""
    
    # Check hardware
    if [ -e /dev/tpm0 ]; then
        echo "✅ TPM Hardware: Available"
    else
        echo "❌ TPM Hardware: Not found"
    fi
    
    # Check group membership
    if groups "$USER" | grep -q "\btss\b"; then
        echo "✅ User Access: In tss group"
    else
        echo "❌ User Access: Not in tss group"
    fi
    
    # Check setup
    if [ -f "$KEYRING_PASSWORD_FILE" ]; then
        echo "✅ TPM Setup: Complete"
        echo "  📁 Directory: $TPM_KEYRING_DIR"
        echo "  🔐 Password file: $(du -h "$KEYRING_PASSWORD_FILE" | cut -f1)"
        
        if [ -f "$TPM_KEYRING_DIR/tpm-unlock.sh" ]; then
            echo "  🔓 Unlock script: Available"
        else
            echo "  ❌ Unlock script: Missing"
        fi
    else
        echo "❌ TPM Setup: Not configured"
    fi
    
    echo ""
    echo "Available commands:"
    echo "  $0 setup   - Setup TPM integration"
    echo "  $0 test    - Test TPM functionality"
    echo "  $0 unlock  - Unlock keyring with TPM"
    echo "  $0 remove  - Remove TPM integration"
}

remove_tpm() {
    show_header "🗑️  Removing TPM Integration"
    echo ""
    echo "This will remove all TPM integration files."
    echo "Your keyring will continue to work with manual password entry."
    echo ""
    echo -n "Are you sure? (y/N): "
    read -n 1 confirm
    echo ""
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        rm -rf "$TPM_KEYRING_DIR"
        show_success "TPM integration removed"
    else
        echo "Operation cancelled"
    fi
}

show_usage() {
    echo "Fixed TPM Integration Setup"
    echo "=========================="
    echo ""
    echo "This script sets up hardware-backed keyring security using TPM 2.0"
    echo "with guaranteed visible prompts for user input."
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup   - Interactive TPM setup (with visible prompts)"
    echo "  test    - Test TPM integration"
    echo "  unlock  - Unlock keyring using TPM"
    echo "  status  - Show TPM integration status"
    echo "  remove  - Remove TPM integration"
    echo ""
    echo "Prerequisites:"
    echo "  - TPM 2.0 hardware (/dev/tpm0)"
    echo "  - User in 'tss' group"
    echo "  - clevis and tpm2-tools installed"
    echo ""
}

# Main command handling
case "${1:-}" in
    setup)
        setup_tpm_interactive
        ;;
    test)
        test_tpm
        ;;
    unlock)
        unlock_keyring
        ;;
    status)
        show_status
        ;;
    remove)
        remove_tpm
        ;;
    *)
        show_usage
        ;;
esac
