#!/bin/bash
# TPM-Keyring Integration Script
# Implements hardware-backed keyring unlock using TPM 2.0

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
CLEVIS_CONFIG_FILE="$TPM_KEYRING_DIR/clevis-config.json"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"
LOG_FILE="$HOME/.cache/tpm-keyring.log"

# PCR values to bind to (system state)
# PCR 0: BIOS/UEFI firmware
# PCR 1: BIOS/UEFI configuration
# PCR 7: Secure Boot state
DEFAULT_PCRS="0,1,7"

# Functions
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
    log_message "INFO: $1"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
    log_message "SUCCESS: $1"
}

show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    log_message "WARNING: $1"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
    log_message "ERROR: $1"
}

check_prerequisites() {
    show_status "Checking prerequisites..."
    
    # Check TPM availability
    if [ ! -e /dev/tpm0 ]; then
        show_error "TPM device not found at /dev/tpm0"
        return 1
    fi
    
    # Check required commands
    local required_commands=("clevis" "tpm2_getcap" "secret-tool" "gnome-keyring-daemon")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            show_error "Required command not found: $cmd"
            return 1
        fi
    done
    
    # Check TPM functionality
    if ! sudo tpm2_getcap properties-fixed >/dev/null 2>&1; then
        show_error "TPM is not accessible or functional"
        return 1
    fi
    
    show_success "All prerequisites met"
    return 0
}

setup_directories() {
    show_status "Setting up directories..."
    
    mkdir -p "$TPM_KEYRING_DIR"
    chmod 700 "$TPM_KEYRING_DIR"
    
    mkdir -p "$(dirname "$LOG_FILE")"
    
    show_success "Directories created"
}

get_keyring_password() {
    show_status "Getting keyring password..."
    
    # Try to get password from user
    echo -n "Enter your keyring password: "
    read -s password
    echo
    
    if [ -z "$password" ]; then
        show_error "Password cannot be empty"
        return 1
    fi
    
    # Test password by trying to unlock keyring
    if echo "$password" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        show_success "Password verified"
        echo "$password"
        return 0
    else
        show_error "Invalid password or keyring unlock failed"
        return 1
    fi
}

encrypt_password_with_tpm() {
    local password="$1"
    local pcrs="${2:-$DEFAULT_PCRS}"
    
    show_status "Encrypting password with TPM (PCRs: $pcrs)..."
    
    # Create clevis configuration
    local clevis_config="{\"pcr_ids\":\"$pcrs\"}"
    echo "$clevis_config" > "$CLEVIS_CONFIG_FILE"
    
    # Encrypt password using clevis-tpm2
    if echo "$password" | clevis encrypt tpm2 "$clevis_config" > "$KEYRING_PASSWORD_FILE"; then
        chmod 600 "$KEYRING_PASSWORD_FILE"
        show_success "Password encrypted and stored securely"
        return 0
    else
        show_error "Failed to encrypt password with TPM"
        return 1
    fi
}

test_tpm_unlock() {
    show_status "Testing TPM-based keyring unlock..."
    
    if [ ! -f "$KEYRING_PASSWORD_FILE" ]; then
        show_error "Encrypted password file not found"
        return 1
    fi
    
    # Try to decrypt and unlock keyring
    if clevis decrypt < "$KEYRING_PASSWORD_FILE" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        show_success "TPM-based keyring unlock successful"
        return 0
    else
        show_error "TPM-based keyring unlock failed"
        return 1
    fi
}

create_unlock_script() {
    show_status "Creating TPM unlock script..."
    
    local unlock_script="$TPM_KEYRING_DIR/tpm-unlock-keyring.sh"
    
    cat > "$unlock_script" << 'EOF'
#!/bin/bash
# TPM-based keyring unlock script
# Auto-generated by tpm-keyring-integration.sh

set -euo pipefail

TPM_KEYRING_DIR="$HOME/.config/tmp-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"
LOG_FILE="$HOME/.cache/tpm-keyring.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Check if keyring is already unlocked
if pgrep -f "gnome-keyring-daemon.*secrets" >/dev/null; then
    # Check if login keyring is accessible
    if secret-tool search keyring login >/dev/null 2>&1; then
        log_message "Keyring already unlocked"
        exit 0
    fi
fi

# Try TPM-based unlock
if [ -f "$KEYRING_PASSWORD_FILE" ]; then
    log_message "Attempting TPM-based keyring unlock"
    
    if clevis decrypt < "$KEYRING_PASSWORD_FILE" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        log_message "TPM-based keyring unlock successful"
        exit 0
    else
        log_message "TPM-based keyring unlock failed"
        exit 1
    fi
else
    log_message "TPM encrypted password file not found"
    exit 1
fi
EOF
    
    chmod 755 "$unlock_script"
    show_success "TPM unlock script created at $unlock_script"
}

create_pam_integration() {
    show_status "Creating PAM integration script..."
    
    local pam_script="$TPM_KEYRING_DIR/pam-tpm-unlock.sh"
    
    cat > "$pam_script" << 'EOF'
#!/bin/bash
# PAM integration script for TPM-based keyring unlock
# Called after successful authentication to unlock keyring

# Only run for interactive sessions
if [ "$PAM_TYPE" != "open_session" ]; then
    exit 0
fi

# Only run for the user's session
if [ "$PAM_USER" != "$(whoami)" ]; then
    exit 0
fi

# Run TPM unlock in background
nohup "$HOME/.config/tpm-keyring/tpm-unlock-keyring.sh" >/dev/null 2>&1 &

exit 0
EOF
    
    chmod 755 "$pam_script"
    show_success "PAM integration script created at $pam_script"
}

setup_systemd_service() {
    show_status "Setting up systemd user service..."
    
    local service_dir="$HOME/.config/systemd/user"
    mkdir -p "$service_dir"
    
    local service_file="$service_dir/tpm-keyring-unlock.service"
    
    cat > "$service_file" << EOF
[Unit]
Description=TPM-based Keyring Unlock
After=graphical-session.target

[Service]
Type=oneshot
ExecStart=$TPM_KEYRING_DIR/tpm-unlock-keyring.sh
RemainAfterExit=yes
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=default.target
EOF
    
    # Reload systemd and enable service
    systemctl --user daemon-reload
    systemctl --user enable tpm-keyring-unlock.service
    
    show_success "Systemd service created and enabled"
}

show_usage() {
    echo "TPM-Keyring Integration Tool"
    echo "============================"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     - Initial setup of TPM-keyring integration"
    echo "  test      - Test TPM-based keyring unlock"
    echo "  unlock    - Unlock keyring using TPM"
    echo "  status    - Show integration status"
    echo "  remove    - Remove TPM integration"
    echo ""
}

cmd_setup() {
    echo "🔐 TPM-Keyring Integration Setup"
    echo "================================"
    echo ""
    
    if ! check_prerequisites; then
        show_error "Prerequisites not met. Please install required packages."
        exit 1
    fi
    
    setup_directories
    
    # Get and encrypt keyring password
    if password=$(get_keyring_password); then
        if encrypt_password_with_tpm "$password"; then
            create_unlock_script
            create_pam_integration
            setup_systemd_service
            
            show_success "TPM-keyring integration setup complete!"
            echo ""
            echo "Next steps:"
            echo "1. Test the integration: $0 test"
            echo "2. The keyring will now unlock automatically after authentication"
            echo "3. Monitor logs: tail -f $LOG_FILE"
        else
            show_error "Failed to encrypt password with TPM"
            exit 1
        fi
    else
        show_error "Failed to get valid keyring password"
        exit 1
    fi
}

cmd_test() {
    echo "🧪 Testing TPM-Keyring Integration"
    echo "=================================="
    echo ""
    
    if test_tpm_unlock; then
        show_success "TPM-keyring integration is working correctly"
    else
        show_error "TPM-keyring integration test failed"
        exit 1
    fi
}

cmd_unlock() {
    "$TPM_KEYRING_DIR/tpm-unlock-keyring.sh"
}

cmd_status() {
    echo "📊 TPM-Keyring Integration Status"
    echo "================================="
    echo ""
    
    if [ -f "$KEYRING_PASSWORD_FILE" ]; then
        show_success "TPM encrypted password file exists"
    else
        show_warning "TPM encrypted password file not found"
    fi
    
    if [ -f "$TPM_KEYRING_DIR/tpm-unlock-keyring.sh" ]; then
        show_success "TPM unlock script exists"
    else
        show_warning "TPM unlock script not found"
    fi
    
    if systemctl --user is-enabled tpm-keyring-unlock.service >/dev/null 2>&1; then
        show_success "Systemd service is enabled"
    else
        show_warning "Systemd service not enabled"
    fi
    
    # Test TPM functionality
    if sudo tpm2_getcap properties-fixed >/dev/null 2>&1; then
        show_success "TPM is functional"
    else
        show_warning "TPM is not accessible"
    fi
}

cmd_remove() {
    echo "🗑️  Removing TPM-Keyring Integration"
    echo "==================================="
    echo ""
    
    read -p "Are you sure you want to remove TPM-keyring integration? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Operation cancelled."
        exit 0
    fi
    
    # Disable and remove systemd service
    systemctl --user disable tpm-keyring-unlock.service 2>/dev/null || true
    rm -f "$HOME/.config/systemd/user/tpm-keyring-unlock.service"
    systemctl --user daemon-reload
    
    # Remove TPM keyring directory
    rm -rf "$TPM_KEYRING_DIR"
    
    show_success "TPM-keyring integration removed"
}

# Main command handling
case "${1:-}" in
    setup)
        cmd_setup
        ;;
    test)
        cmd_test
        ;;
    unlock)
        cmd_unlock
        ;;
    status)
        cmd_status
        ;;
    remove)
        cmd_remove
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
