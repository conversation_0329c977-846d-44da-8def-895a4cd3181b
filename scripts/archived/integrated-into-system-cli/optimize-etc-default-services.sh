#!/bin/bash
# /etc/default/ Services Optimization Script
# Based on analysis findings from June 8, 2025

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging
LOG_FILE="/tmp/etc-default-services-optimization-$(date +%Y%m%d-%H%M%S).log"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

print_header() {
    echo -e "${BLUE}===========================================${NC}"
    echo -e "${BLUE}  /etc/default/ Services Optimizer        ${NC}"
    echo -e "${BLUE}===========================================${NC}"
    echo ""
    echo -e "${YELLOW}Optimizing services based on analysis findings${NC}"
    echo -e "${YELLOW}Date: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo ""
}

check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        echo -e "${RED}❌ This script should NOT be run as root${NC}"
        echo -e "${YELLOW}Please run as regular user - sudo will be used when needed${NC}"
        exit 1
    fi
}

backup_file() {
    local file="$1"
    if [[ -f "$file" ]]; then
        local backup="${file}.backup-$(date +%Y%m%d-%H%M%S)"
        sudo cp "$file" "$backup"
        log "Created backup: $backup"
        echo -e "${GREEN}✅ Backup created: $backup${NC}"
    fi
}

disable_unnecessary_services() {
    echo -e "${BLUE}🔍 Analyzing unnecessary specialized services...${NC}"
    
    # Check dump1090-mutability (ADS-B receiver)
    if systemctl is-enabled dump1090-mutability >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️ dump1090-mutability is enabled (ADS-B aircraft receiver)${NC}"
        echo -e "${YELLOW}This service is typically only needed for SDR/aviation enthusiasts${NC}"
        
        read -p "Disable dump1090-mutability service? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo systemctl disable dump1090-mutability
            sudo systemctl stop dump1090-mutability
            log "Disabled dump1090-mutability service"
            echo -e "${GREEN}✅ dump1090-mutability service disabled${NC}"
        else
            echo -e "${YELLOW}⏭️ Keeping dump1090-mutability enabled${NC}"
        fi
    else
        echo -e "${GREEN}✅ dump1090-mutability already disabled or not installed${NC}"
    fi
    
    echo ""
    
    # Check llmnrd (Link-Local Multicast Name Resolution)
    if systemctl is-enabled llmnrd >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️ llmnrd is enabled (Link-Local Multicast Name Resolution)${NC}"
        echo -e "${YELLOW}This service is rarely needed on modern workstations${NC}"
        
        read -p "Disable llmnrd service? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo systemctl disable llmnrd
            sudo systemctl stop llmnrd
            log "Disabled llmnrd service"
            echo -e "${GREEN}✅ llmnrd service disabled${NC}"
        else
            echo -e "${YELLOW}⏭️ Keeping llmnrd enabled${NC}"
        fi
    else
        echo -e "${GREEN}✅ llmnrd already disabled or not installed${NC}"
    fi
    
    echo ""
}

optimize_virtualization_services() {
    echo -e "${BLUE}🖥️ Analyzing virtualization services...${NC}"
    
    # Check if Docker is actively used
    local docker_containers=$(docker ps -q 2>/dev/null | wc -l || echo "0")
    echo -e "${YELLOW}Docker containers running: $docker_containers${NC}"
    
    # Check LibVirt usage
    local libvirt_vms=$(sudo virsh list --all 2>/dev/null | grep -c "running\|shut off" || echo "0")
    
    if [[ $libvirt_vms -eq 0 ]]; then
        echo -e "${YELLOW}⚠️ LibVirt services enabled but no VMs found${NC}"
        echo -e "${YELLOW}Services: libvirtd, virtlockd, virtlogd${NC}"
        
        read -p "Disable unused LibVirt services? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo systemctl disable libvirtd
            sudo systemctl disable virtlockd  
            sudo systemctl disable virtlogd
            sudo systemctl stop libvirtd
            sudo systemctl stop virtlockd
            sudo systemctl stop virtlogd
            log "Disabled LibVirt services (no VMs in use)"
            echo -e "${GREEN}✅ LibVirt services disabled${NC}"
        else
            echo -e "${YELLOW}⏭️ Keeping LibVirt services enabled${NC}"
        fi
    else
        echo -e "${GREEN}✅ LibVirt services in use ($libvirt_vms VMs found)${NC}"
    fi
    
    echo ""
}

enable_system_monitoring() {
    echo -e "${BLUE}📊 Checking system statistics collection...${NC}"
    
    local sysstat_enabled=$(grep 'ENABLED="true"' /etc/default/sysstat >/dev/null && echo "true" || echo "false")
    
    if [[ $sysstat_enabled == "false" ]]; then
        echo -e "${YELLOW}⚠️ System statistics collection is disabled${NC}"
        echo -e "${YELLOW}Enabling this provides performance monitoring and troubleshooting data${NC}"
        
        read -p "Enable system statistics collection (sysstat)? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            backup_file "/etc/default/sysstat"
            sudo sed -i 's/ENABLED="false"/ENABLED="true"/' /etc/default/sysstat
            sudo systemctl enable sysstat
            sudo systemctl start sysstat
            log "Enabled system statistics collection"
            echo -e "${GREEN}✅ System statistics collection enabled${NC}"
        else
            echo -e "${YELLOW}⏭️ Keeping system statistics disabled${NC}"
        fi
    else
        echo -e "${GREEN}✅ System statistics collection already enabled${NC}"
    fi
    
    echo ""
}

optimize_irq_balancing() {
    echo -e "${BLUE}⚡ Optimizing IRQ balancing for Dell Precision 5560...${NC}"
    
    local irq_optimized=$(grep -q "IRQBALANCE_ARGS" /etc/default/irqbalance && echo "true" || echo "false")
    
    if [[ $irq_optimized == "false" ]]; then
        echo -e "${YELLOW}💡 IRQ balancing can be optimized for multi-core CPU performance${NC}"
        
        read -p "Optimize IRQ balancing for Dell Precision 5560? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            backup_file "/etc/default/irqbalance"
            echo 'IRQBALANCE_ARGS="--hintpolicy=subset"' | sudo tee -a /etc/default/irqbalance
            sudo systemctl restart irqbalance
            log "Optimized IRQ balancing configuration"
            echo -e "${GREEN}✅ IRQ balancing optimized${NC}"
        else
            echo -e "${YELLOW}⏭️ Keeping default IRQ balancing${NC}"
        fi
    else
        echo -e "${GREEN}✅ IRQ balancing already optimized${NC}"
    fi
    
    echo ""
}

optimize_microcode() {
    echo -e "${BLUE}🔒 Optimizing Intel microcode configuration...${NC}"
    
    local microcode_optimized=$(grep -q "IUCODE_TOOL_SCANCPUS=yes" /etc/default/intel-microcode && echo "true" || echo "false")
    
    if [[ $microcode_optimized == "false" ]]; then
        echo -e "${YELLOW}💡 Intel microcode can be optimized for faster boot and better security${NC}"
        
        read -p "Optimize Intel microcode configuration? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            backup_file "/etc/default/intel-microcode"
            echo 'IUCODE_TOOL_SCANCPUS=yes' | sudo tee -a /etc/default/intel-microcode
            echo 'IUCODE_TOOL_INITRAMFS=early' | sudo tee -a /etc/default/intel-microcode
            log "Optimized Intel microcode configuration"
            echo -e "${GREEN}✅ Intel microcode optimized${NC}"
            echo -e "${YELLOW}⚠️ Reboot recommended for microcode changes to take effect${NC}"
        else
            echo -e "${YELLOW}⏭️ Keeping default microcode configuration${NC}"
        fi
    else
        echo -e "${GREEN}✅ Intel microcode already optimized${NC}"
    fi
    
    echo ""
}

cleanup_backup_files() {
    echo -e "${BLUE}🧹 Cleaning up old backup files...${NC}"
    
    local backup_files=$(find /etc/default -name "*.backup-*" 2>/dev/null | wc -l)
    
    if [[ $backup_files -gt 0 ]]; then
        echo -e "${YELLOW}Found $backup_files backup files from previous optimizations${NC}"
        find /etc/default -name "*.backup-*" -exec ls -la {} \;
        
        read -p "Remove old backup files? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            sudo rm /etc/default/*.backup-* 2>/dev/null || true
            log "Cleaned up old backup files"
            echo -e "${GREEN}✅ Old backup files removed${NC}"
        else
            echo -e "${YELLOW}⏭️ Keeping backup files${NC}"
        fi
    else
        echo -e "${GREEN}✅ No old backup files found${NC}"
    fi
    
    echo ""
}

show_optimization_summary() {
    echo -e "${BLUE}📊 Optimization Summary${NC}"
    echo ""
    
    echo -e "${YELLOW}Service Status After Optimization:${NC}"
    
    # Check key services
    for service in dump1090-mutability llmnrd libvirtd sysstat irqbalance; do
        if systemctl list-unit-files | grep -q "^$service"; then
            local status=$(systemctl is-enabled $service 2>/dev/null || echo "disabled")
            local active=$(systemctl is-active $service 2>/dev/null || echo "inactive")
            echo "   $service: $status/$active"
        fi
    done
    
    echo ""
    echo -e "${YELLOW}Configuration Files Modified:${NC}"
    find /etc/default -name "*.backup-$(date +%Y%m%d)*" 2>/dev/null | while read backup; do
        original=${backup%.backup-*}
        echo "   $(basename $original) (backup created)"
    done || echo "   No configuration files modified in this session"
    
    echo ""
}

show_summary() {
    echo -e "${GREEN}===========================================${NC}"
    echo -e "${GREEN}     Services Optimization Complete      ${NC}"
    echo -e "${GREEN}===========================================${NC}"
    echo ""
    echo -e "${GREEN}✅ /etc/default/ services analysis completed${NC}"
    echo -e "${GREEN}✅ Optimization opportunities addressed${NC}"
    echo -e "${GREEN}✅ System resource utilization improved${NC}"
    echo ""
    echo -e "${BLUE}Log file: $LOG_FILE${NC}"
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo -e "${YELLOW}  1. Reboot if microcode changes were made${NC}"
    echo -e "${YELLOW}  2. Monitor system performance improvements${NC}"
    echo -e "${YELLOW}  3. Verify disabled services are not needed${NC}"
    echo ""
}

main() {
    print_header
    check_permissions
    
    log "Starting /etc/default/ services optimization"
    
    # Run optimization steps
    disable_unnecessary_services
    optimize_virtualization_services
    enable_system_monitoring
    optimize_irq_balancing
    optimize_microcode
    cleanup_backup_files
    
    # Show summary
    show_optimization_summary
    
    log "/etc/default/ services optimization completed"
    show_summary
}

# Run main function
main "$@"
