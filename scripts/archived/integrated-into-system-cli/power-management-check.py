#!/usr/bin/env python3
"""
Power Management Configuration Checker
Verifies optimal power management setup and detects conflicts.
"""

import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Tuple


def run_command(cmd: str) -> Tuple[int, str, str]:
    """Run a command and return exit code, stdout, stderr."""
    try:
        result = subprocess.run(
            cmd.split(), 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return 1, "", "Command timed out"
    except Exception as e:
        return 1, "", str(e)


def check_service_status(service: str) -> Dict[str, str]:
    """Check systemd service status."""
    is_active_code, is_active_out, _ = run_command(f"systemctl is-active {service}")
    is_enabled_code, is_enabled_out, _ = run_command(f"systemctl is-enabled {service}")
    
    return {
        "active": is_active_out if is_active_code == 0 else "inactive",
        "enabled": is_enabled_out if is_enabled_code == 0 else "disabled"
    }


def check_power_management() -> Dict[str, Dict]:
    """Check all power management services."""
    services = {
        "tlp": "Primary power management",
        "tuned": "System tuning daemon", 
        "thermald": "Thermal management",
        "intel_lpmd": "Intel Low Power Mode Daemon",
        "powertop": "Power optimization service"
    }
    
    results = {}
    for service, description in services.items():
        status = check_service_status(service)
        status["description"] = description
        results[service] = status
    
    return results


def check_installed_packages() -> List[str]:
    """Check which power management packages are installed."""
    code, output, _ = run_command("dpkg -l")
    if code != 0:
        return []
    
    packages = []
    power_packages = ["tlp", "tuned", "thermald", "intel-lpmd", "powertop"]
    
    for line in output.split('\n'):
        if line.startswith('ii '):
            for pkg in power_packages:
                if pkg in line:
                    packages.append(pkg)
    
    return packages


def analyze_conflicts(services: Dict[str, Dict]) -> List[str]:
    """Analyze potential conflicts between power management services."""
    conflicts = []
    
    # Check for multiple active power managers
    active_power_managers = []
    if services.get("tlp", {}).get("active") == "active":
        active_power_managers.append("TLP")
    if services.get("tuned", {}).get("active") == "active":
        active_power_managers.append("tuned")
    if services.get("powertop", {}).get("active") == "active":
        active_power_managers.append("powertop")
    
    if len(active_power_managers) > 1:
        conflicts.append(f"Multiple power managers active: {', '.join(active_power_managers)}")
    
    # Check for Intel LPMD conflicts with TLP
    if (services.get("tlp", {}).get("active") == "active" and 
        services.get("intel_lpmd", {}).get("active") == "active"):
        conflicts.append("TLP and intel_lpmd both active (may conflict)")
    
    return conflicts


def get_power_profile() -> str:
    """Get current power profile."""
    code, output, _ = run_command("powerprofilesctl get")
    return output if code == 0 else "unknown"


def get_cpu_governor() -> str:
    """Get current CPU governor."""
    try:
        with open("/sys/devices/system/cpu/cpu0/cpufreq/scaling_governor", "r") as f:
            return f.read().strip()
    except:
        return "unknown"


def get_tlp_detailed_status() -> Dict[str, str]:
    """Get detailed TLP status information."""
    tlp_info = {}

    # Get TLP status
    code, output, _ = run_command("sudo tlp-stat -s")
    if code == 0:
        for line in output.split('\n'):
            if 'Mode' in line and '=' in line:
                tlp_info['mode'] = line.split('=')[1].strip()
            elif 'Power source' in line and '=' in line:
                tlp_info['power_source'] = line.split('=')[1].strip()
            elif 'State' in line and '=' in line:
                tlp_info['state'] = line.split('=')[1].strip()

    return tlp_info


def main():
    """Main analysis function."""
    print("🔋 Power Management Configuration Analysis")
    print("=" * 50)

    # Check services
    print("\n📊 Service Status:")
    services = check_power_management()
    for service, info in services.items():
        status_icon = "✅" if info["active"] == "active" else "❌"
        enabled_icon = "🔄" if info["enabled"] == "enabled" else "⏸️"
        print(f"  {status_icon} {service:12} | Active: {info['active']:8} | Enabled: {info['enabled']:8} {enabled_icon}")
        print(f"     └─ {info['description']}")

    # Check installed packages
    print("\n📦 Installed Packages:")
    packages = check_installed_packages()
    for pkg in packages:
        print(f"  ✅ {pkg}")

    # Get detailed TLP status
    print("\n🔋 TLP Detailed Status:")
    tlp_status = get_tlp_detailed_status()
    for key, value in tlp_status.items():
        print(f"  {key.replace('_', ' ').title()}: {value}")

    # Analyze conflicts
    print("\n⚠️  Conflict Analysis:")
    conflicts = analyze_conflicts(services)
    if conflicts:
        for conflict in conflicts:
            print(f"  🚨 {conflict}")
    else:
        print("  ✅ No conflicts detected")

    # Current power state
    print("\n⚡ Current Power State:")
    print(f"  Power Profile: {get_power_profile()}")
    print(f"  CPU Governor:  {get_cpu_governor()}")

    # Recommendations
    print("\n💡 Recommendations:")

    # Optimal configuration check
    tlp_active = services.get("tlp", {}).get("active") == "active"
    tuned_inactive = services.get("tuned", {}).get("active") != "active"
    powertop_service_inactive = services.get("powertop", {}).get("active") != "active"
    thermald_active = services.get("thermald", {}).get("active") == "active"

    if tlp_active and tuned_inactive and powertop_service_inactive and thermald_active:
        print("  ✅ Configuration is OPTIMAL!")
        print("  ✅ TLP 1.6.1 is primary power manager (Dell Precision 5560 optimized)")
        print("  ✅ thermald provides thermal management without conflicts")
        print("  ✅ No conflicting services active (tuned not installed)")
        print("  ✅ powertop available for analysis only (service disabled)")
        print("  ✅ intel_lpmd inactive (TLP handles Intel power management)")
        print("\n  🏆 RESULT: Perfect power management configuration achieved!")
    else:
        if not tlp_active:
            print("  🔧 Consider enabling TLP for better power management")
        if not tuned_inactive:
            print("  🔧 Consider disabling tuned to avoid conflicts with TLP")
        if not powertop_service_inactive:
            print("  🔧 Consider disabling powertop.service (use for analysis only)")
        if not thermald_active:
            print("  🔧 Consider enabling thermald for thermal management")

    print("\n🔍 Analysis Tools:")
    print("  • Generate power report: sudo powertop --html=power-report.html --time=10")
    print("  • Check TLP status: sudo tlp-stat -s")
    print("  • Check TLP config: sudo tlp-stat -c")
    print("  • Monitor power: python3 scripts/power-monitor.py")
    print("  • View power report: firefox power-analysis-report.html")


if __name__ == "__main__":
    main()
