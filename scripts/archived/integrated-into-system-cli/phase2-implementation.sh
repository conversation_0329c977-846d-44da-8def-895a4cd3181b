#!/bin/bash
# Phase 2 Implementation Script
# Orchestrates TPM integration and authentication flow standardization

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Log file
LOG_FILE="$HOME/.cache/phase2-implementation-$(date +%Y%m%d-%H%M%S).log"

# Functions
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

show_header() {
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}$(echo "$1" | sed 's/./=/g')${NC}"
    log_message "HEADER: $1"
}

show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
    log_message "INFO: $1"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
    log_message "SUCCESS: $1"
}

show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    log_message "WARNING: $1"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
    log_message "ERROR: $1"
}

check_prerequisites() {
    show_status "Checking Phase 2 prerequisites..."
    
    local missing_deps=()
    
    # Check required commands
    local required_commands=("clevis" "tpm2_getcap" "secret-tool" "gnome-keyring-daemon" "fprintd-list")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_deps+=("$cmd")
        fi
    done
    
    # Check TPM device
    if [ ! -e /dev/tpm0 ]; then
        show_error "TPM device not found at /dev/tpm0"
        return 1
    fi
    
    # Check required scripts
    local required_scripts=("tpm-keyring-integration.sh" "auth-flow-standardization.sh")
    for script in "${required_scripts[@]}"; do
        if [ ! -f "$SCRIPT_DIR/$script" ]; then
            missing_deps+=("$script")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        show_error "Missing dependencies: ${missing_deps[*]}"
        return 1
    fi
    
    show_success "All prerequisites met"
    return 0
}

verify_phase1_cleanup() {
    show_status "Verifying Phase 1 cleanup was successful..."
    
    # Check keyring daemon count
    local keyring_count=$(ps aux | grep gnome-keyring-daemon | grep -v grep | wc -l)
    if [ "$keyring_count" -le 2 ]; then
        show_success "Keyring daemon cleanup successful ($keyring_count processes)"
    else
        show_warning "Multiple keyring daemons still running ($keyring_count processes)"
    fi
    
    # Check legacy biometric system removal
    if [ ! -d /etc/biometric-auth ]; then
        show_success "Legacy biometric system removed"
    else
        show_warning "Legacy biometric system still present"
    fi
    
    return 0
}

setup_tpm_integration() {
    show_header "Setting up TPM-Keyring Integration"
    
    local tpm_script="$SCRIPT_DIR/tpm-keyring-integration.sh"
    
    if [ ! -f "$tpm_script" ]; then
        show_error "TPM integration script not found"
        return 1
    fi
    
    show_status "Running TPM integration setup..."
    
    # Make script executable
    chmod +x "$tpm_script"
    
    # Run setup with user interaction
    if "$tpm_script" setup; then
        show_success "TPM integration setup completed"
        return 0
    else
        show_error "TPM integration setup failed"
        return 1
    fi
}

setup_auth_flow_standardization() {
    show_header "Setting up Authentication Flow Standardization"
    
    local auth_script="$SCRIPT_DIR/auth-flow-standardization.sh"
    
    if [ ! -f "$auth_script" ]; then
        show_error "Authentication flow script not found"
        return 1
    fi
    
    show_status "Running authentication flow standardization setup..."
    
    # Make script executable
    chmod +x "$auth_script"
    
    # Run setup
    if "$auth_script" setup; then
        show_success "Authentication flow standardization completed"
        return 0
    else
        show_error "Authentication flow standardization failed"
        return 1
    fi
}

test_integrations() {
    show_header "Testing Phase 2 Integrations"
    
    local all_tests_passed=true
    
    # Test TPM integration
    show_status "Testing TPM integration..."
    if "$SCRIPT_DIR/tpm-keyring-integration.sh" test >/dev/null 2>&1; then
        show_success "TPM integration test passed"
    else
        show_warning "TPM integration test failed"
        all_tests_passed=false
    fi
    
    # Test authentication flow
    show_status "Testing authentication flow..."
    if "$SCRIPT_DIR/auth-flow-standardization.sh" test >/dev/null 2>&1; then
        show_success "Authentication flow test passed"
    else
        show_warning "Authentication flow test failed"
        all_tests_passed=false
    fi
    
    # Test system CLI integration
    show_status "Testing system CLI integration..."
    if command -v system-cli >/dev/null 2>&1; then
        if system-cli keyring status >/dev/null 2>&1; then
            show_success "System CLI integration working"
        else
            show_warning "System CLI keyring commands not working"
            all_tests_passed=false
        fi
    else
        show_warning "System CLI not available"
        all_tests_passed=false
    fi
    
    if $all_tests_passed; then
        show_success "All integration tests passed"
        return 0
    else
        show_warning "Some integration tests failed"
        return 1
    fi
}

create_monitoring_setup() {
    show_header "Setting up Monitoring and Logging"
    
    # Create monitoring script
    local monitor_script="$HOME/.local/bin/auth-monitor"
    mkdir -p "$(dirname "$monitor_script")"
    
    cat > "$monitor_script" << 'EOF'
#!/bin/bash
# Authentication monitoring wrapper script

AUTH_FLOW_SCRIPT="$HOME/.config/auth-flow/auth-status.sh"
TPM_SCRIPT="$HOME/cursor-system/scripts/tpm-keyring-integration.sh"

case "${1:-status}" in
    status|auth)
        if [ -f "$AUTH_FLOW_SCRIPT" ]; then
            "$AUTH_FLOW_SCRIPT"
        else
            echo "Authentication flow not setup"
        fi
        ;;
    tpm)
        if [ -f "$TPM_SCRIPT" ]; then
            "$TPM_SCRIPT" status
        else
            echo "TPM integration not setup"
        fi
        ;;
    logs)
        echo "=== Authentication Logs ==="
        tail -20 "$HOME/.cache/auth-flow.log" 2>/dev/null || echo "No auth logs found"
        echo ""
        echo "=== TPM Logs ==="
        tail -20 "$HOME/.cache/tpm-keyring.log" 2>/dev/null || echo "No TPM logs found"
        ;;
    *)
        echo "Usage: $0 {status|auth|tpm|logs}"
        ;;
esac
EOF
    
    chmod +x "$monitor_script"
    show_success "Monitoring script created at $monitor_script"
    
    # Add to PATH if not already there
    if [[ ":$PATH:" != *":$HOME/.local/bin:"* ]]; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$HOME/.bashrc"
        show_status "Added $HOME/.local/bin to PATH in .bashrc"
    fi
}

create_phase2_summary() {
    show_header "Phase 2 Implementation Summary"
    
    local summary_file="$HOME/.cache/phase2-summary-$(date +%Y%m%d-%H%M%S).md"
    
    cat > "$summary_file" << EOF
# Phase 2 Implementation Summary

**Date**: $(date '+%Y-%m-%d %H:%M:%S')
**Status**: Implementation Complete

## Components Implemented

### 1. TPM-Keyring Integration
- ✅ Hardware-backed keyring password encryption
- ✅ Automatic keyring unlock using TPM
- ✅ Systemd service for session integration
- ✅ PCR-based security binding

### 2. Authentication Flow Standardization
- ✅ Unified keyring unlock across all auth methods
- ✅ Authentication event monitoring
- ✅ Session state management
- ✅ Fingerprint integration hooks

### 3. System CLI Enhancement
- ✅ TPM setup commands added
- ✅ TPM status and testing commands
- ✅ Integration with existing keyring management

### 4. Monitoring and Logging
- ✅ Comprehensive authentication logging
- ✅ Authentication method usage tracking
- ✅ Status monitoring commands
- ✅ Centralized monitoring script

## Usage Commands

### TPM Integration
\`\`\`bash
# Setup TPM integration
./scripts/tpm-keyring-integration.sh setup

# Test TPM integration
./scripts/tpm-keyring-integration.sh test

# Check TPM status
./scripts/tpm-keyring-integration.sh status
\`\`\`

### Authentication Flow
\`\`\`bash
# Setup authentication flow standardization
./scripts/auth-flow-standardization.sh setup

# Check authentication status
./scripts/auth-flow-standardization.sh status

# Monitor authentication events
./scripts/auth-flow-standardization.sh monitor
\`\`\`

### System CLI
\`\`\`bash
# Check keyring status
system-cli keyring status

# Setup TPM integration (if system-cli available)
system-cli keyring tpm-setup

# Test TPM integration
system-cli keyring tpm-test
\`\`\`

### Monitoring
\`\`\`bash
# Check overall authentication status
auth-monitor status

# Check TPM status
auth-monitor tpm

# View recent logs
auth-monitor logs
\`\`\`

## Security Improvements

1. **Hardware Security**: Keyring passwords now protected by TPM hardware
2. **Consistent Authentication**: All auth methods now properly unlock keyring
3. **Monitoring**: Comprehensive logging of authentication events
4. **Session Management**: Proper session state tracking

## Next Steps

1. **Test all authentication methods** (fingerprint, password, TPM)
2. **Monitor logs** for any issues or conflicts
3. **Proceed to Phase 3** (PAM optimization) when ready

## Troubleshooting

- **Logs**: Check \`~/.cache/auth-flow.log\` and \`~/.cache/tpm-keyring.log\`
- **Status**: Use \`auth-monitor status\` for overall status
- **Reset**: Use respective \`remove\` commands to reset if needed

---
*Phase 2 implementation completed successfully*
EOF
    
    show_success "Summary created at $summary_file"
    echo ""
    echo "📄 Implementation summary saved to:"
    echo "   $summary_file"
}

show_next_steps() {
    show_header "Next Steps and Usage"
    
    echo ""
    echo "🎯 Phase 2 Implementation Complete!"
    echo ""
    echo "Key improvements achieved:"
    echo "  ✅ TPM hardware security integration"
    echo "  ✅ Standardized authentication flows"
    echo "  ✅ Enhanced monitoring and logging"
    echo "  ✅ System CLI integration"
    echo ""
    echo "Quick start commands:"
    echo "  📊 Check status: auth-monitor status"
    echo "  🔍 View logs: auth-monitor logs"
    echo "  🧪 Test TPM: ./scripts/tpm-keyring-integration.sh test"
    echo "  🔄 Test auth flow: ./scripts/auth-flow-standardization.sh test"
    echo ""
    echo "What to test:"
    echo "  1. Login with fingerprint - keyring should unlock automatically"
    echo "  2. Login with password - keyring should unlock automatically"
    echo "  3. Check that TPM integration is working"
    echo "  4. Monitor authentication events in logs"
    echo ""
    echo "When ready for Phase 3 (PAM optimization):"
    echo "  - Ensure Phase 2 is working correctly"
    echo "  - Review authentication logs for any issues"
    echo "  - Proceed with PAM stack simplification"
    echo ""
}

# Main execution
main() {
    show_header "🚀 Phase 2 Implementation: TPM Integration & Auth Flow Standardization"
    
    echo ""
    echo "This script will implement:"
    echo "  1. TPM-Keyring Integration"
    echo "  2. Authentication Flow Standardization"
    echo "  3. Enhanced System CLI Integration"
    echo "  4. Monitoring and Logging Setup"
    echo ""
    
    read -p "Continue with Phase 2 implementation? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        echo "Implementation cancelled."
        exit 0
    fi
    
    # Create log file
    mkdir -p "$(dirname "$LOG_FILE")"
    log_message "Phase 2 implementation started"
    
    # Execute implementation steps
    if ! check_prerequisites; then
        show_error "Prerequisites not met. Please install required packages."
        exit 1
    fi
    
    verify_phase1_cleanup
    
    if ! setup_tpm_integration; then
        show_error "TPM integration setup failed"
        exit 1
    fi
    
    if ! setup_auth_flow_standardization; then
        show_error "Authentication flow setup failed"
        exit 1
    fi
    
    test_integrations
    create_monitoring_setup
    create_phase2_summary
    
    show_success "Phase 2 implementation completed successfully!"
    log_message "Phase 2 implementation completed"
    
    show_next_steps
}

# Run main function
main "$@"
