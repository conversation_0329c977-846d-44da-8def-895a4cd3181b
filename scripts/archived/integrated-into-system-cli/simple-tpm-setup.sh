#!/bin/bash
# Simple TPM Setup for Keyring Integration
# Works around group permission issues

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"
LOG_FILE="$HOME/.cache/tpm-keyring.log"

# Functions
show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

setup_directories() {
    show_status "Setting up TPM directories..."
    
    mkdir -p "$TPM_KEYRING_DIR"
    chmod 700 "$TPM_KEYRING_DIR"
    
    mkdir -p "$(dirname "$LOG_FILE")"
    
    show_success "Directories created"
}

get_keyring_password() {
    show_status "Getting keyring password..."
    
    echo -n "Enter your keyring password: "
    read -s password
    echo
    
    if [ -z "$password" ]; then
        show_error "Password cannot be empty"
        return 1
    fi
    
    # Test password by trying to unlock keyring
    if echo "$password" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        show_success "Password verified"
        echo "$password"
        return 0
    else
        show_error "Invalid password or keyring unlock failed"
        return 1
    fi
}

encrypt_with_sudo_tpm() {
    local password="$1"
    
    show_status "Encrypting password with TPM using sudo..."
    
    # Create clevis configuration for TPM
    local clevis_config='{"pcr_ids":"0,1,7"}'
    
    # Use sudo to run clevis encrypt with TPM access
    if echo "$password" | sudo clevis encrypt tpm2 "$clevis_config" > "$KEYRING_PASSWORD_FILE"; then
        chmod 600 "$KEYRING_PASSWORD_FILE"
        show_success "Password encrypted and stored securely"
        log_message "Password encrypted with TPM using sudo"
        return 0
    else
        show_error "Failed to encrypt password with TPM"
        return 1
    fi
}

test_tpm_decrypt() {
    show_status "Testing TPM decryption..."
    
    if [ ! -f "$KEYRING_PASSWORD_FILE" ]; then
        show_error "Encrypted password file not found"
        return 1
    fi
    
    # Test decryption using sudo
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" >/dev/null 2>&1; then
        show_success "TPM decryption test successful"
        log_message "TPM decryption test successful"
        return 0
    else
        show_error "TPM decryption test failed"
        return 1
    fi
}

create_unlock_script() {
    show_status "Creating TPM unlock script..."
    
    local unlock_script="$TPM_KEYRING_DIR/tpm-unlock-keyring.sh"
    
    cat > "$unlock_script" << 'EOF'
#!/bin/bash
# TPM-based keyring unlock script (using sudo)

set -euo pipefail

TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"
LOG_FILE="$HOME/.cache/tmp-keyring.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE" 2>/dev/null || true
}

# Check if keyring is already unlocked
if pgrep -f "gnome-keyring-daemon.*secrets" >/dev/null; then
    if secret-tool search keyring login >/dev/null 2>&1; then
        log_message "Keyring already unlocked"
        exit 0
    fi
fi

# Try TPM-based unlock
if [ -f "$KEYRING_PASSWORD_FILE" ]; then
    log_message "Attempting TPM-based keyring unlock"
    
    # Use sudo for TPM access
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        log_message "TPM-based keyring unlock successful"
        exit 0
    else
        log_message "TPM-based keyring unlock failed"
        exit 1
    fi
else
    log_message "TPM encrypted password file not found"
    exit 1
fi
EOF
    
    chmod 755 "$unlock_script"
    show_success "TPM unlock script created"
}

create_status_script() {
    show_status "Creating TPM status script..."
    
    local status_script="$TPM_KEYRING_DIR/tpm-status.sh"
    
    cat > "$status_script" << 'EOF'
#!/bin/bash
# TPM integration status

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔒 TPM Integration Status${NC}"
echo "========================="
echo ""

TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"

# Check TPM hardware
if [ -e /dev/tpm0 ]; then
    echo -e "TPM Hardware: ${GREEN}✅ Available${NC}"
else
    echo -e "TPM Hardware: ${RED}❌ Not found${NC}"
fi

# Check encrypted password file
if [ -f "$KEYRING_PASSWORD_FILE" ]; then
    echo -e "Encrypted Password: ${GREEN}✅ Stored${NC}"
else
    echo -e "Encrypted Password: ${RED}❌ Not found${NC}"
fi

# Check unlock script
if [ -f "$TPM_KEYRING_DIR/tpm-unlock-keyring.sh" ]; then
    echo -e "Unlock Script: ${GREEN}✅ Available${NC}"
else
    echo -e "Unlock Script: ${RED}❌ Not found${NC}"
fi

# Test TPM access
echo -n "TPM Access: "
if sudo tpm2_getcap properties-fixed >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Working${NC}"
else
    echo -e "${RED}❌ Failed${NC}"
fi

echo ""
echo "Note: This setup uses sudo for TPM operations"
EOF
    
    chmod 755 "$status_script"
    show_success "TPM status script created"
}

show_usage() {
    echo "Simple TPM Setup for Keyring Integration"
    echo "======================================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     - Setup TPM integration"
    echo "  test      - Test TPM integration"
    echo "  status    - Show TPM status"
    echo "  unlock    - Unlock keyring using TPM"
    echo "  remove    - Remove TPM integration"
    echo ""
}

cmd_setup() {
    echo "🔒 Simple TPM Integration Setup"
    echo "==============================="
    echo ""
    
    setup_directories
    
    # Get and encrypt keyring password
    if password=$(get_keyring_password); then
        if encrypt_with_sudo_tpm "$password"; then
            if test_tpm_decrypt; then
                create_unlock_script
                create_status_script
                
                show_success "TPM integration setup complete!"
                echo ""
                echo "Created components:"
                echo "  - Encrypted keyring password"
                echo "  - TPM unlock script"
                echo "  - TPM status script"
                echo ""
                echo "Usage:"
                echo "  Test: $0 test"
                echo "  Status: $0 status"
                echo "  Unlock: $0 unlock"
                echo ""
                echo "Note: This setup uses sudo for TPM operations"
            else
                show_error "TPM decryption test failed"
                return 1
            fi
        else
            show_error "Failed to encrypt password"
            return 1
        fi
    else
        show_error "Failed to get valid keyring password"
        return 1
    fi
}

cmd_test() {
    echo "🧪 Testing TPM Integration"
    echo "=========================="
    echo ""
    
    if test_tpm_decrypt; then
        show_success "TPM integration is working"
        
        # Test full unlock
        show_status "Testing keyring unlock..."
        if "$TPM_KEYRING_DIR/tpm-unlock-keyring.sh"; then
            show_success "Keyring unlock test successful"
        else
            show_warning "Keyring unlock test failed"
        fi
    else
        show_error "TPM integration test failed"
        return 1
    fi
}

cmd_status() {
    if [ -f "$TPM_KEYRING_DIR/tpm-status.sh" ]; then
        "$TPM_KEYRING_DIR/tpm-status.sh"
    else
        show_error "TPM status script not found. Run setup first."
        return 1
    fi
}

cmd_unlock() {
    if [ -f "$TPM_KEYRING_DIR/tpm-unlock-keyring.sh" ]; then
        "$TPM_KEYRING_DIR/tpm-unlock-keyring.sh"
    else
        show_error "TPM unlock script not found. Run setup first."
        return 1
    fi
}

cmd_remove() {
    echo "🗑️  Removing TPM Integration"
    echo "============================"
    echo ""
    
    read -p "Are you sure you want to remove TPM integration? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Operation cancelled."
        exit 0
    fi
    
    rm -rf "$TPM_KEYRING_DIR"
    rm -f "$LOG_FILE"
    
    show_success "TPM integration removed"
}

# Main command handling
case "${1:-}" in
    setup)
        cmd_setup
        ;;
    test)
        cmd_test
        ;;
    status)
        cmd_status
        ;;
    unlock)
        cmd_unlock
        ;;
    remove)
        cmd_remove
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
