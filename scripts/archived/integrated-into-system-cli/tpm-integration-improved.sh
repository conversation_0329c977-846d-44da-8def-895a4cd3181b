#!/bin/bash
# Improved TPM Integration Script with Better Prompts
# Hardware-backed keyring security using TPM 2.0

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"
LOG_FILE="$HOME/.cache/tpm-keyring.log"

# Functions
show_header() {
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}$(echo "$1" | sed 's/./=/g')${NC}"
}

show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
}

show_prompt() {
    echo -e "${CYAN}👤 $1${NC}"
}

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE" 2>/dev/null || true
}

check_prerequisites() {
    show_status "Checking TPM integration prerequisites..."
    
    local missing_deps=()
    
    # Check TPM hardware
    if [ ! -e /dev/tpm0 ]; then
        show_error "TPM hardware not found at /dev/tpm0"
        return 1
    fi
    
    # Check required commands
    local required_commands=("clevis" "tpm2_getcap" "secret-tool" "gnome-keyring-daemon")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_deps+=("$cmd")
        fi
    done
    
    # Check user is in tss group
    if ! groups "$USER" | grep -q "\btss\b"; then
        show_error "User $USER is not in the 'tss' group"
        show_status "Run: sudo usermod -a -G tss $USER"
        show_status "Then restart your session"
        return 1
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        show_error "Missing dependencies: ${missing_deps[*]}"
        return 1
    fi
    
    show_success "All prerequisites met"
    return 0
}

test_tpm_access() {
    show_status "Testing TPM access..."
    
    # Test basic TPM access
    if sudo tpm2_getcap properties-fixed >/dev/null 2>&1; then
        show_success "TPM hardware is accessible"
        return 0
    else
        show_error "TPM hardware is not accessible"
        show_status "This may require a system restart or TPM configuration"
        return 1
    fi
}

setup_directories() {
    show_status "Setting up TPM directories..."
    
    mkdir -p "$TPM_KEYRING_DIR"
    chmod 700 "$TPM_KEYRING_DIR"
    
    mkdir -p "$(dirname "$LOG_FILE")"
    
    show_success "Directories created"
}

get_keyring_password() {
    show_header "Keyring Password Setup"
    echo ""
    echo "To enable TPM-based keyring unlock, we need to encrypt your"
    echo "keyring password using the TPM hardware security module."
    echo ""
    show_warning "Your password will be encrypted and stored securely using TPM."
    show_warning "You'll still need to enter it manually if TPM unlock fails."
    echo ""
    
    local password
    local confirm_password
    local attempts=0
    local max_attempts=3
    
    while [ $attempts -lt $max_attempts ]; do
        show_prompt "Please enter your keyring password:"
        echo -n "Password: "
        read -s password
        echo ""
        
        if [ -z "$password" ]; then
            show_error "Password cannot be empty"
            ((attempts++))
            continue
        fi
        
        show_prompt "Please confirm your keyring password:"
        echo -n "Confirm: "
        read -s confirm_password
        echo ""
        
        if [ "$password" != "$confirm_password" ]; then
            show_error "Passwords do not match"
            ((attempts++))
            continue
        fi
        
        # Test password by trying to unlock keyring
        show_status "Verifying password with keyring..."
        if echo "$password" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
            show_success "Password verified successfully"
            echo "$password"
            return 0
        else
            show_error "Password verification failed - keyring unlock unsuccessful"
            ((attempts++))
        fi
    done
    
    show_error "Maximum password attempts exceeded"
    return 1
}

encrypt_with_tpm() {
    local password="$1"
    
    show_status "Encrypting password with TPM hardware..."
    echo ""
    echo "This process will:"
    echo "  1. Create a TPM-based encryption key"
    echo "  2. Bind the key to system state (PCRs 0,1,7)"
    echo "  3. Encrypt your keyring password"
    echo ""
    show_warning "You may be prompted for sudo password for TPM access"
    echo ""
    
    # Create clevis configuration for TPM
    local clevis_config='{"pcr_ids":"0,1,7"}'
    
    # Use sudo to run clevis encrypt with TPM access
    show_status "Running TPM encryption (may prompt for sudo)..."
    if echo "$password" | sudo clevis encrypt tpm2 "$clevis_config" > "$KEYRING_PASSWORD_FILE" 2>/dev/null; then
        chmod 600 "$KEYRING_PASSWORD_FILE"
        show_success "Password encrypted and stored securely"
        log_message "Password encrypted with TPM using PCRs 0,1,7"
        return 0
    else
        show_error "Failed to encrypt password with TPM"
        show_status "This may be due to TPM configuration or PCR issues"
        return 1
    fi
}

test_tpm_decrypt() {
    show_status "Testing TPM decryption..."
    
    if [ ! -f "$KEYRING_PASSWORD_FILE" ]; then
        show_error "Encrypted password file not found"
        return 1
    fi
    
    show_warning "Testing decryption (may prompt for sudo)..."
    
    # Test decryption using sudo
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" >/dev/null 2>&1; then
        show_success "TPM decryption test successful"
        log_message "TPM decryption test successful"
        return 0
    else
        show_error "TPM decryption test failed"
        show_status "This may indicate PCR state changes or TPM issues"
        return 1
    fi
}

create_unlock_script() {
    show_status "Creating TPM unlock script..."
    
    local unlock_script="$TPM_KEYRING_DIR/tpm-unlock-keyring.sh"
    
    cat > "$unlock_script" << 'EOF'
#!/bin/bash
# TPM-based keyring unlock script

set -euo pipefail

TPM_KEYRING_DIR="$HOME/.config/tmp-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"
LOG_FILE="$HOME/.cache/tpm-keyring.log"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE" 2>/dev/null || true
}

echo -e "${BLUE}🔒 TPM Keyring Unlock${NC}"

# Check if keyring is already unlocked
if pgrep -f "gnome-keyring-daemon.*secrets" >/dev/null; then
    if secret-tool search keyring login >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Keyring already unlocked${NC}"
        log_message "Keyring already unlocked"
        exit 0
    fi
fi

# Try TPM-based unlock
if [ -f "$KEYRING_PASSWORD_FILE" ]; then
    echo "🔓 Attempting TPM-based unlock..."
    log_message "Attempting TPM-based keyring unlock"
    
    # Use sudo for TPM access
    if sudo clevis decrypt < "$KEYRING_PASSWORD_FILE" | gnome-keyring-daemon --unlock >/dev/null 2>&1; then
        echo -e "${GREEN}✅ TPM keyring unlock successful${NC}"
        log_message "TPM-based keyring unlock successful"
        exit 0
    else
        echo -e "${RED}❌ TPM keyring unlock failed${NC}"
        log_message "TPM-based keyring unlock failed"
        exit 1
    fi
else
    echo -e "${RED}❌ TPM encrypted password file not found${NC}"
    log_message "TPM encrypted password file not found"
    exit 1
fi
EOF
    
    chmod 755 "$unlock_script"
    show_success "TPM unlock script created"
}

create_status_script() {
    show_status "Creating TPM status script..."
    
    local status_script="$TPM_KEYRING_DIR/tpm-status.sh"
    
    cat > "$status_script" << 'EOF'
#!/bin/bash
# TPM integration status display

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔒 TPM Integration Status${NC}"
echo "========================="
echo ""

TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"

# Check TPM hardware
if [ -e /dev/tpm0 ]; then
    echo -e "TPM Hardware: ${GREEN}✅ Available${NC}"
else
    echo -e "TPM Hardware: ${RED}❌ Not found${NC}"
fi

# Check user group membership
if groups "$USER" | grep -q "\btss\b"; then
    echo -e "User Access: ${GREEN}✅ In tss group${NC}"
else
    echo -e "User Access: ${RED}❌ Not in tss group${NC}"
fi

# Check encrypted password file
if [ -f "$KEYRING_PASSWORD_FILE" ]; then
    echo -e "Encrypted Password: ${GREEN}✅ Stored${NC}"
    echo -e "  File size: $(du -h "$KEYRING_PASSWORD_FILE" | cut -f1)"
else
    echo -e "Encrypted Password: ${RED}❌ Not found${NC}"
fi

# Check unlock script
if [ -f "$TPM_KEYRING_DIR/tpm-unlock-keyring.sh" ]; then
    echo -e "Unlock Script: ${GREEN}✅ Available${NC}"
else
    echo -e "Unlock Script: ${RED}❌ Not found${NC}"
fi

# Test TPM access
echo -n "TPM Access: "
if sudo tpm2_getcap properties-fixed >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Working${NC}"
else
    echo -e "${RED}❌ Failed${NC}"
fi

echo ""
echo -e "${YELLOW}Note:${NC} This setup uses sudo for TPM operations"
echo -e "${YELLOW}Usage:${NC} Run 'tpm-unlock-keyring.sh' to unlock keyring with TPM"
EOF
    
    chmod 755 "$status_script"
    show_success "TPM status script created"
}

show_usage() {
    echo "TPM Integration for Keyring Security"
    echo "==================================="
    echo ""
    echo "This script sets up hardware-backed keyring security using TPM 2.0"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     - Setup TPM integration (interactive)"
    echo "  test      - Test TPM integration"
    echo "  status    - Show TPM integration status"
    echo "  unlock    - Unlock keyring using TPM"
    echo "  remove    - Remove TPM integration"
    echo ""
    echo "Prerequisites:"
    echo "  - TPM 2.0 hardware"
    echo "  - User in 'tss' group"
    echo "  - clevis, tpm2-tools installed"
    echo ""
}

cmd_setup() {
    show_header "🔒 TPM Integration Setup"
    echo ""
    echo "This will set up hardware-backed keyring security using your TPM 2.0 chip."
    echo ""
    echo "Benefits:"
    echo "  ✅ Hardware-backed security"
    echo "  ✅ Automatic keyring unlock"
    echo "  ✅ Protection against software attacks"
    echo "  ✅ System state binding (secure boot, etc.)"
    echo ""
    echo "Requirements:"
    echo "  ⚠️  You'll need to enter your keyring password"
    echo "  ⚠️  Sudo access required for TPM operations"
    echo "  ⚠️  System restart may be needed if setup fails"
    echo ""
    
    read -p "Continue with TPM integration setup? (Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
    
    echo ""
    
    if ! check_prerequisites; then
        show_error "Prerequisites not met. Please install required packages and configure user access."
        exit 1
    fi
    
    if ! test_tpm_access; then
        show_error "TPM access test failed. You may need to restart your system."
        exit 1
    fi
    
    setup_directories
    
    echo ""
    # Get and encrypt keyring password
    if password=$(get_keyring_password); then
        echo ""
        if encrypt_with_tpm "$password"; then
            echo ""
            if test_tpm_decrypt; then
                echo ""
                create_unlock_script
                create_status_script
                
                echo ""
                show_success "🎉 TPM integration setup complete!"
                echo ""
                echo "Created components:"
                echo "  📁 Configuration directory: $TPM_KEYRING_DIR"
                echo "  🔐 Encrypted password file: $(basename "$KEYRING_PASSWORD_FILE")"
                echo "  🔓 Unlock script: tpm-unlock-keyring.sh"
                echo "  📊 Status script: tpm-status.sh"
                echo ""
                echo "Usage:"
                echo "  🧪 Test: $0 test"
                echo "  📊 Status: $0 status"
                echo "  🔓 Unlock: $0 unlock"
                echo ""
                echo "Integration with auth-monitor:"
                echo "  The TPM unlock will be available through the authentication system"
                echo ""
                show_warning "Note: TPM operations require sudo access"
            else
                show_error "TPM decryption test failed"
                return 1
            fi
        else
            show_error "Failed to encrypt password with TPM"
            return 1
        fi
    else
        show_error "Failed to get valid keyring password"
        return 1
    fi
}

cmd_test() {
    show_header "🧪 Testing TPM Integration"
    echo ""
    
    if ! check_prerequisites; then
        show_error "Prerequisites not met"
        return 1
    fi
    
    if test_tpm_decrypt; then
        show_success "TPM decryption working correctly"
        
        echo ""
        # Test full unlock
        show_status "Testing complete keyring unlock..."
        if "$TPM_KEYRING_DIR/tpm-unlock-keyring.sh"; then
            show_success "Complete TPM keyring unlock test successful"
        else
            show_warning "Keyring unlock test failed"
        fi
    else
        show_error "TPM integration test failed"
        return 1
    fi
}

cmd_status() {
    if [ -f "$TPM_KEYRING_DIR/tpm-status.sh" ]; then
        "$TPM_KEYRING_DIR/tpm-status.sh"
    else
        show_error "TPM status script not found. Run setup first."
        return 1
    fi
}

cmd_unlock() {
    if [ -f "$TPM_KEYRING_DIR/tpm-unlock-keyring.sh" ]; then
        "$TPM_KEYRING_DIR/tpm-unlock-keyring.sh"
    else
        show_error "TPM unlock script not found. Run setup first."
        return 1
    fi
}

cmd_remove() {
    show_header "🗑️  Removing TPM Integration"
    echo ""
    echo "This will remove:"
    echo "  - Encrypted keyring password"
    echo "  - TPM unlock scripts"
    echo "  - TPM configuration"
    echo ""
    show_warning "Your keyring will still work with manual password entry"
    echo ""
    
    read -p "Are you sure you want to remove TPM integration? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Operation cancelled."
        exit 0
    fi
    
    rm -rf "$TPM_KEYRING_DIR"
    rm -f "$LOG_FILE"
    
    show_success "TPM integration removed successfully"
}

# Main command handling
case "${1:-}" in
    setup)
        cmd_setup
        ;;
    test)
        cmd_test
        ;;
    status)
        cmd_status
        ;;
    unlock)
        cmd_unlock
        ;;
    remove)
        cmd_remove
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
