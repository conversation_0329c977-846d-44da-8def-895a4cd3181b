#!/bin/bash

# Cron Security Monitoring Wrapper
# This script is called by cron to execute security monitoring tasks

set -euo pipefail

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/cron-config.conf"

# Load configuration
if [[ -f "$CONFIG_FILE" ]]; then
    source "$CONFIG_FILE"
else
    echo "ERROR: Configuration file not found: $CONFIG_FILE" >&2
    exit 1
fi

# Set defaults
AUDIT_BASE_DIR="${AUDIT_BASE_DIR:-/home/<USER>/security-audits}"
LOG_FILE="$AUDIT_BASE_DIR/logs/cron-execution.log"

# Logging function
log_execution() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# Notification function
send_notification() {
    local level="$1"
    local message="$2"
    local title="Security Monitor - $level"
    
    # Log notification
    log_execution "$level: $message"
    
    # Desktop notification
    if [[ "$NOTIFY_DESKTOP" == "true" ]] && command -v notify-send >/dev/null; then
        notify-send -t "${DESKTOP_NOTIFICATION_TIMEOUT:-10000}" "$title" "$message"
    fi
    
    # Email notification
    if [[ "$ENABLE_EMAIL" == "true" ]] && [[ "$NOTIFY_EMAIL" == "true" ]] && command -v mail >/dev/null; then
        echo "$message" | mail -s "$title" "$EMAIL_RECIPIENT"
    fi
}

# Execute monitoring task
execute_task() {
    local task_type="$1"
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local output_dir="$AUDIT_BASE_DIR/$task_type"
    local report_file="$output_dir/${task_type}_report_$timestamp.txt"
    
    log_execution "Starting $task_type monitoring task"
    
    case "$task_type" in
        "daily")
            execute_daily_checks "$report_file"
            ;;
        "weekly")
            execute_weekly_audit "$report_file"
            ;;
        "monthly")
            execute_monthly_audit "$report_file"
            ;;
        *)
            log_execution "ERROR: Unknown task type: $task_type"
            exit 1
            ;;
    esac
    
    # Set file permissions
    chmod "${REPORT_FILE_PERMISSIONS:-600}" "$report_file"
    
    log_execution "Completed $task_type monitoring task: $report_file"
}

# Daily monitoring checks
execute_daily_checks() {
    local report_file="$1"
    local has_warnings=false
    
    {
        echo "=== DAILY SECURITY MONITORING REPORT ==="
        echo "Date: $(date)"
        echo "System: $(hostname)"
        echo "User: $(whoami)"
        echo ""
        
        # Keyring status check
        if [[ "$DAILY_KEYRING_CHECK" == "true" ]]; then
            echo "=== KEYRING STATUS ==="
            if [[ -x "$KEYRING_MONITOR" ]]; then
                "$KEYRING_MONITOR" || has_warnings=true
            else
                echo "WARNING: Keyring monitor script not found or not executable"
                has_warnings=true
            fi
            echo ""
        fi
        
        # Storage check
        if [[ "$DAILY_STORAGE_CHECK" == "true" ]]; then
            echo "=== STORAGE STATUS ==="
            df -h /
            local storage_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
            if [[ $storage_usage -gt ${STORAGE_CRITICAL_THRESHOLD:-95} ]]; then
                echo "CRITICAL: Storage usage is ${storage_usage}% (threshold: ${STORAGE_CRITICAL_THRESHOLD:-95}%)"
                has_warnings=true
            elif [[ $storage_usage -gt ${STORAGE_WARNING_THRESHOLD:-85} ]]; then
                echo "WARNING: Storage usage is ${storage_usage}% (threshold: ${STORAGE_WARNING_THRESHOLD:-85}%)"
                has_warnings=true
            fi
            echo ""
        fi
        
        # Memory check
        if [[ "$DAILY_MEMORY_CHECK" == "true" ]]; then
            echo "=== MEMORY STATUS ==="
            free -h
            local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
            if [[ $memory_usage -gt ${MEMORY_WARNING_THRESHOLD:-80} ]]; then
                echo "WARNING: Memory usage is ${memory_usage}% (threshold: ${MEMORY_WARNING_THRESHOLD:-80}%)"
                has_warnings=true
            fi
            echo ""
        fi
        
        # Firewall check
        if [[ "$DAILY_FIREWALL_CHECK" == "true" ]]; then
            echo "=== FIREWALL STATUS ==="
            sudo ufw status || {
                echo "WARNING: Unable to check firewall status"
                has_warnings=true
            }
            echo ""
        fi
        
        # Failed login attempts
        if [[ "$DAILY_FAILED_LOGINS" == "true" ]]; then
            echo "=== FAILED LOGIN ATTEMPTS (last 24 hours) ==="
            local failed_logins=$(journalctl --since "24 hours ago" | grep -c "Failed password" || echo "0")
            echo "Failed login attempts: $failed_logins"
            if [[ $failed_logins -gt ${FAILED_LOGIN_THRESHOLD:-5} ]]; then
                echo "WARNING: High number of failed login attempts: $failed_logins"
                has_warnings=true
            fi
            echo ""
        fi
        
        echo "=== SUMMARY ==="
        if [[ "$has_warnings" == "true" ]]; then
            echo "Status: WARNINGS DETECTED - Review required"
        else
            echo "Status: ALL CHECKS PASSED"
        fi
        
    } > "$report_file"
    
    # Send notifications if needed
    if [[ "$has_warnings" == "true" ]]; then
        send_notification "WARNING" "Daily security check detected issues. Check report: $report_file"
    elif [[ "$NOTIFY_ON_SUCCESS" == "true" ]]; then
        send_notification "INFO" "Daily security check completed successfully"
    fi
}

# Weekly security audit
execute_weekly_audit() {
    local report_file="$1"
    
    {
        echo "=== WEEKLY SECURITY AUDIT REPORT ==="
        echo "Date: $(date)"
        echo "System: $(hostname)"
        echo ""
        
        # Security audit
        if [[ "$WEEKLY_SECURITY_AUDIT" == "true" ]] && [[ -x "$SECURITY_AUDIT" ]]; then
            echo "=== SECURITY AUDIT ==="
            "$SECURITY_AUDIT"
            echo ""
        fi
        
        # Power analysis
        if [[ "$WEEKLY_POWER_ANALYSIS" == "true" ]] && command -v powertop >/dev/null; then
            echo "=== POWER ANALYSIS ==="
            local power_report="$AUDIT_BASE_DIR/weekly/power_analysis_$(date '+%Y%m%d_%H%M%S').html"
            sudo powertop --time="${POWERTOP_ANALYSIS_TIME:-60}" --html="$power_report" >/dev/null 2>&1 || true
            echo "Power analysis report generated: $power_report"
            echo ""
        fi
        
        # Service health check
        if [[ "$WEEKLY_SERVICE_CHECK" == "true" ]] && [[ -x "$SYSTEM_HEALTH" ]]; then
            echo "=== SYSTEM SERVICE HEALTH ==="
            "$SYSTEM_HEALTH"
            echo ""
        fi
        
        # Package updates check
        if [[ "$WEEKLY_PACKAGE_UPDATES" == "true" ]]; then
            echo "=== AVAILABLE PACKAGE UPDATES ==="
            apt list --upgradable 2>/dev/null | grep -v "WARNING" | head -20
            echo ""
        fi
        
    } > "$report_file"
    
    send_notification "INFO" "Weekly security audit completed. Report: $report_file"
}

# Monthly comprehensive audit
execute_monthly_audit() {
    local report_file="$1"
    
    {
        echo "=== MONTHLY COMPREHENSIVE SECURITY AUDIT ==="
        echo "Date: $(date)"
        echo "System: $(hostname)"
        echo ""
        
        # Comprehensive security audit
        if [[ "$MONTHLY_FULL_SECURITY_AUDIT" == "true" ]] && [[ -x "$COMPREHENSIVE_AUDIT" ]]; then
            echo "=== COMPREHENSIVE SECURITY AUDIT ==="
            "$COMPREHENSIVE_AUDIT"
            echo ""
        fi
        
        # System health summary
        if [[ "$MONTHLY_SYSTEM_HEALTH" == "true" ]]; then
            echo "=== SYSTEM HEALTH SUMMARY ==="
            echo "Uptime: $(uptime)"
            echo "Load average: $(cat /proc/loadavg)"
            echo "Disk usage:"
            df -h
            echo "Memory usage:"
            free -h
            echo ""
        fi
        
        # Configuration backup
        if [[ "$MONTHLY_CONFIGURATION_BACKUP" == "true" ]]; then
            echo "=== CONFIGURATION BACKUP ==="
            local backup_dir="$AUDIT_BASE_DIR/monthly/config_backup_$(date '+%Y%m%d')"
            mkdir -p "$backup_dir"
            
            # Backup important configurations
            cp -r ~/.config/keyring-manager "$backup_dir/" 2>/dev/null || true
            cp /etc/pam.d/common-auth "$backup_dir/" 2>/dev/null || true
            
            echo "Configuration backup created: $backup_dir"
            echo ""
        fi
        
    } > "$report_file"
    
    # Cleanup old reports if enabled
    if [[ "$MONTHLY_CLEANUP_OLD_REPORTS" == "true" ]]; then
        cleanup_old_reports
    fi
    
    send_notification "INFO" "Monthly comprehensive audit completed. Report: $report_file"
}

# Cleanup old reports
cleanup_old_reports() {
    log_execution "Starting cleanup of old reports"
    
    # Clean daily reports
    find "$AUDIT_BASE_DIR/daily" -name "*.txt" -mtime +${DAILY_RETENTION:-30} -delete 2>/dev/null || true
    
    # Clean weekly reports
    find "$AUDIT_BASE_DIR/weekly" -name "*.txt" -mtime +${WEEKLY_RETENTION:-90} -delete 2>/dev/null || true
    find "$AUDIT_BASE_DIR/weekly" -name "*.html" -mtime +${WEEKLY_RETENTION:-90} -delete 2>/dev/null || true
    
    # Clean monthly reports (keep longer)
    find "$AUDIT_BASE_DIR/monthly" -name "*.txt" -mtime +${MONTHLY_RETENTION:-365} -delete 2>/dev/null || true
    
    log_execution "Cleanup of old reports completed"
}

# Main execution
main() {
    local task_type="${1:-}"
    
    if [[ -z "$task_type" ]]; then
        echo "Usage: $0 [daily|weekly|monthly]"
        exit 1
    fi
    
    execute_task "$task_type"
}

# Execute main function with all arguments
main "$@"
