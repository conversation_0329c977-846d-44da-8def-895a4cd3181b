#!/bin/bash

# Email Alert Configuration Script
# Dell Precision 5560 - Security Monitoring Email Setup
# Usage: ./configure-email-alerts.sh [setup|test|disable]

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/cron-config.conf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if mail command is available
check_mail_command() {
    if ! command -v mail >/dev/null 2>&1; then
        warning "Mail command not found. Installing mailutils..."
        
        if command -v apt >/dev/null 2>&1; then
            sudo apt update && sudo apt install -y mailutils
        else
            error "Unable to install mail command. Please install mailutils manually."
            exit 1
        fi
    fi
    
    success "Mail command is available"
}

# Setup email configuration
setup_email() {
    log "Setting up email alerts configuration..."
    
    # Check for mail command
    check_mail_command
    
    # Get email configuration from user
    echo ""
    echo "Email Alert Configuration"
    echo "========================="
    echo ""
    
    read -p "Enter recipient email address: " email_recipient
    read -p "Enter sender email (or press Enter for default): " email_sender
    
    if [[ -z "$email_sender" ]]; then
        email_sender="security-monitor@$(hostname)"
    fi
    
    echo ""
    echo "Email Configuration:"
    echo "  Recipient: $email_recipient"
    echo "  Sender: $email_sender"
    echo ""
    
    read -p "Is this correct? (y/N): " confirm
    if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
        echo "Configuration cancelled."
        exit 0
    fi
    
    # Update configuration file
    if [[ -f "$CONFIG_FILE" ]]; then
        # Create backup
        cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
        
        # Update email settings
        sed -i "s/^ENABLE_EMAIL=.*/ENABLE_EMAIL=true/" "$CONFIG_FILE"
        sed -i "s/^EMAIL_RECIPIENT=.*/EMAIL_RECIPIENT=\"$email_recipient\"/" "$CONFIG_FILE"
        sed -i "s/^EMAIL_SENDER=.*/EMAIL_SENDER=\"$email_sender\"/" "$CONFIG_FILE"
        sed -i "s/^NOTIFY_EMAIL=.*/NOTIFY_EMAIL=true/" "$CONFIG_FILE"
        
        success "Email configuration updated in $CONFIG_FILE"
    else
        error "Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
    
    # Test email configuration
    echo ""
    read -p "Send test email? (Y/n): " send_test
    if [[ "$send_test" != "n" && "$send_test" != "N" ]]; then
        test_email
    fi
}

# Test email configuration
test_email() {
    log "Testing email configuration..."
    
    # Load configuration
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
    else
        error "Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
    
    if [[ "$ENABLE_EMAIL" != "true" ]]; then
        error "Email is not enabled in configuration"
        exit 1
    fi
    
    # Send test email
    local test_message="This is a test email from the Dell Precision 5560 security monitoring system.

System Information:
- Hostname: $(hostname)
- Date: $(date)
- User: $(whoami)
- Uptime: $(uptime)

If you received this email, the email alert system is working correctly.

Security Monitoring System
Dell Precision 5560"
    
    echo "$test_message" | mail -s "Security Monitor Test - $(hostname)" "$EMAIL_RECIPIENT"
    
    if [[ $? -eq 0 ]]; then
        success "Test email sent to $EMAIL_RECIPIENT"
        echo ""
        echo "Please check your email inbox (and spam folder) for the test message."
    else
        error "Failed to send test email"
        exit 1
    fi
}

# Disable email alerts
disable_email() {
    log "Disabling email alerts..."
    
    if [[ -f "$CONFIG_FILE" ]]; then
        # Create backup
        cp "$CONFIG_FILE" "$CONFIG_FILE.backup.$(date +%Y%m%d_%H%M%S)"
        
        # Disable email settings
        sed -i "s/^ENABLE_EMAIL=.*/ENABLE_EMAIL=false/" "$CONFIG_FILE"
        sed -i "s/^NOTIFY_EMAIL=.*/NOTIFY_EMAIL=false/" "$CONFIG_FILE"
        
        success "Email alerts disabled in $CONFIG_FILE"
    else
        error "Configuration file not found: $CONFIG_FILE"
        exit 1
    fi
}

# Show current email configuration
show_status() {
    log "Email Alert Configuration Status"
    echo ""
    
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
        
        echo "Configuration file: $CONFIG_FILE"
        echo "Email enabled: $ENABLE_EMAIL"
        echo "Email notifications: ${NOTIFY_EMAIL:-false}"
        echo "Recipient: ${EMAIL_RECIPIENT:-not set}"
        echo "Sender: ${EMAIL_SENDER:-not set}"
        echo ""
        
        if command -v mail >/dev/null 2>&1; then
            success "Mail command is available"
        else
            warning "Mail command not found - install mailutils"
        fi
    else
        error "Configuration file not found: $CONFIG_FILE"
    fi
}

# Setup Gmail SMTP (optional advanced configuration)
setup_gmail_smtp() {
    log "Setting up Gmail SMTP configuration..."
    
    warning "This requires an App Password from your Google Account"
    echo "1. Go to https://myaccount.google.com/apppasswords"
    echo "2. Generate an app password for 'Mail'"
    echo "3. Use that password (not your regular Gmail password)"
    echo ""
    
    read -p "Enter your Gmail address: " gmail_address
    read -s -p "Enter your Gmail app password: " gmail_password
    echo ""
    
    # Install ssmtp if not available
    if ! command -v ssmtp >/dev/null 2>&1; then
        log "Installing ssmtp for Gmail SMTP..."
        sudo apt update && sudo apt install -y ssmtp
    fi
    
    # Configure ssmtp
    sudo tee /etc/ssmtp/ssmtp.conf > /dev/null << EOF
# Gmail SMTP Configuration for Security Monitoring
root=$gmail_address
mailhub=smtp.gmail.com:587
rewriteDomain=gmail.com
AuthUser=$gmail_address
AuthPass=$gmail_password
FromLineOverride=YES
UseSTARTTLS=YES
EOF
    
    sudo chmod 640 /etc/ssmtp/ssmtp.conf
    
    success "Gmail SMTP configured"
    
    # Update mail command to use ssmtp
    sudo update-alternatives --install /usr/sbin/sendmail sendmail /usr/sbin/ssmtp 60
    
    # Test configuration
    echo ""
    read -p "Send test email via Gmail? (Y/n): " send_test
    if [[ "$send_test" != "n" && "$send_test" != "N" ]]; then
        echo "Test email from Dell Precision 5560 security monitoring via Gmail SMTP" | mail -s "Gmail SMTP Test" "$gmail_address"
        success "Test email sent via Gmail SMTP"
    fi
}

# Main function
main() {
    local action="${1:-help}"
    
    case "$action" in
        "setup")
            setup_email
            ;;
        "test")
            test_email
            ;;
        "disable")
            disable_email
            ;;
        "status")
            show_status
            ;;
        "gmail")
            setup_gmail_smtp
            ;;
        "help"|*)
            echo "Email Alert Configuration for Security Monitoring"
            echo ""
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  setup    - Configure email alerts"
            echo "  test     - Send test email"
            echo "  disable  - Disable email alerts"
            echo "  status   - Show current configuration"
            echo "  gmail    - Setup Gmail SMTP (advanced)"
            echo "  help     - Show this help message"
            echo ""
            echo "Prerequisites:"
            echo "  - mailutils package (will be installed automatically)"
            echo "  - Working SMTP server or Gmail account (for gmail option)"
            echo ""
            echo "Configuration file: $CONFIG_FILE"
            ;;
    esac
}

# Execute main function
main "$@"
