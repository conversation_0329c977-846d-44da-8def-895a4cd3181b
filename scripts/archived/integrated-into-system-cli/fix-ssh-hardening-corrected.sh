#!/bin/bash
# Corrected SSH Hardening Script
# Uses correct service names for Ubuntu/Debian

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 SSH Hardening (Corrected Version)${NC}"
echo "===================================="
echo ""

# Function to confirm actions
confirm_action() {
    local message="$1"
    echo -e "${YELLOW}⚠️  $message${NC}"
    read -p "Do you want to proceed? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        return 0
    else
        return 1
    fi
}

# Check SSH service status
check_ssh_service() {
    echo -e "${BLUE}🔍 Checking SSH Service${NC}"
    echo "----------------------"
    
    if systemctl is-active --quiet ssh; then
        echo -e "${GREEN}✅ SSH service (ssh.service) is active${NC}"
    elif systemctl is-active --quiet sshd; then
        echo -e "${GREEN}✅ SSH service (sshd.service) is active${NC}"
        SSH_SERVICE="sshd"
    else
        echo -e "${YELLOW}⚠️  SSH service is not active${NC}"
        echo "Available SSH-related services:"
        systemctl list-units --all | grep -i ssh
    fi
    
    # Default to ssh.service for Ubuntu/Debian
    SSH_SERVICE=${SSH_SERVICE:-"ssh"}
    echo "Using SSH service: $SSH_SERVICE.service"
    echo ""
}

# Check if SSH is actually needed
check_ssh_necessity() {
    echo -e "${BLUE}❓ SSH Service Analysis${NC}"
    echo "----------------------"
    
    echo "Current SSH connections:"
    ss -tuln | grep :22 || echo "No SSH port 22 listeners found"
    
    echo ""
    echo "SSH socket status:"
    systemctl status ssh.socket --no-pager -l | head -5
    
    echo ""
    if confirm_action "Do you actually need SSH server running on this desktop system?"; then
        echo "SSH server will be configured and hardened."
        return 0
    else
        echo ""
        echo -e "${YELLOW}💡 Consider disabling SSH if not needed:${NC}"
        echo "  sudo systemctl disable ssh.service"
        echo "  sudo systemctl stop ssh.service"
        echo "  sudo systemctl disable ssh.socket"
        echo "  sudo systemctl stop ssh.socket"
        echo ""
        if confirm_action "Disable SSH services since they're not needed?"; then
            sudo systemctl disable ssh.service ssh.socket
            sudo systemctl stop ssh.service ssh.socket
            echo -e "${GREEN}✅ SSH services disabled${NC}"
            return 1
        fi
        return 0
    fi
}

# Apply SSH hardening
apply_ssh_hardening() {
    echo -e "${BLUE}🔐 Applying SSH Hardening${NC}"
    echo "-------------------------"
    
    # Remove any existing hardening config
    sudo rm -f /etc/ssh/sshd_config.d/99-security-hardening.conf
    
    # Create hardening configuration
    sudo tee /etc/ssh/sshd_config.d/99-security-hardening.conf << 'EOF'
# SSH Security Hardening Configuration
# Compatible with Ubuntu/Debian systems

# Disable root login
PermitRootLogin no

# Use key-based authentication only
PasswordAuthentication no
ChallengeResponseAuthentication no
PubkeyAuthentication yes

# Limit authentication attempts
MaxAuthTries 3

# Session management
ClientAliveInterval 300
ClientAliveCountMax 2
MaxSessions 4

# Disable risky features
X11Forwarding no
AllowTcpForwarding no
GatewayPorts no
PermitTunnel no
PermitEmptyPasswords no

# Enhanced logging
LogLevel VERBOSE

# Strong cryptography
Ciphers <EMAIL>,<EMAIL>,<EMAIL>
MACs <EMAIL>,<EMAIL>
KexAlgorithms <EMAIL>,diffie-hellman-group16-sha512
EOF
    
    echo "Testing SSH configuration..."
    if sudo sshd -t; then
        echo -e "${GREEN}✅ SSH configuration is valid${NC}"
        
        if confirm_action "Apply SSH hardening by restarting SSH service?"; then
            echo "Restarting SSH service..."
            sudo systemctl restart $SSH_SERVICE
            
            if systemctl is-active --quiet $SSH_SERVICE; then
                echo -e "${GREEN}✅ SSH service restarted successfully${NC}"
                echo ""
                echo "SSH hardening applied:"
                echo "  • Root login: DISABLED"
                echo "  • Password auth: DISABLED"
                echo "  • Key auth only: ENABLED"
                echo "  • Max attempts: 3"
                echo "  • Strong crypto: ENABLED"
                return 0
            else
                echo -e "${RED}❌ SSH service failed to restart${NC}"
                echo "Rolling back configuration..."
                sudo rm /etc/ssh/sshd_config.d/99-security-hardening.conf
                sudo systemctl restart $SSH_SERVICE
                return 1
            fi
        else
            echo -e "${YELLOW}⏭️  SSH hardening saved but not applied${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ SSH configuration has errors${NC}"
        sudo sshd -t
        sudo rm /etc/ssh/sshd_config.d/99-security-hardening.conf
        return 1
    fi
}

# Setup SSH keys if needed
setup_ssh_keys() {
    echo -e "${BLUE}🔑 SSH Key Setup${NC}"
    echo "---------------"
    
    if [[ -f ~/.ssh/authorized_keys ]] && [[ -s ~/.ssh/authorized_keys ]]; then
        echo -e "${GREEN}✅ SSH authorized_keys found${NC}"
        echo "Your SSH public keys:"
        grep -v "^#" ~/.ssh/authorized_keys | head -3 | while read line; do
            key_type=$(echo "$line" | awk '{print $1}')
            key_comment=$(echo "$line" | awk '{print $3}')
            echo "  • $key_type: $key_comment"
        done
        return 0
    else
        echo -e "${YELLOW}⚠️  No SSH keys found${NC}"
        echo ""
        echo "You need SSH keys before disabling password authentication!"
        echo ""
        
        if confirm_action "Generate SSH key pair now?"; then
            echo "Generating SSH key pair..."
            ssh-keygen -t ed25519 -C "$USER@$(hostname)" -f ~/.ssh/id_ed25519
            
            # Add to authorized_keys for local testing
            mkdir -p ~/.ssh
            chmod 700 ~/.ssh
            cat ~/.ssh/id_ed25519.pub >> ~/.ssh/authorized_keys
            chmod 600 ~/.ssh/authorized_keys
            
            echo -e "${GREEN}✅ SSH key pair generated and added to authorized_keys${NC}"
            return 0
        else
            echo -e "${YELLOW}⏭️  SSH hardening skipped - set up keys first${NC}"
            return 1
        fi
    fi
}

# Test SSH configuration
test_ssh_config() {
    echo -e "${BLUE}🧪 Testing SSH Configuration${NC}"
    echo "----------------------------"
    
    echo "SSH service status:"
    systemctl status $SSH_SERVICE --no-pager -l | head -8
    
    echo ""
    echo "SSH configuration test:"
    if sudo sshd -T >/dev/null 2>&1; then
        echo -e "${GREEN}✅ SSH configuration is valid${NC}"
    else
        echo -e "${RED}❌ SSH configuration has issues${NC}"
    fi
    
    echo ""
    echo -e "${YELLOW}IMPORTANT TEST:${NC}"
    echo "Open a NEW terminal and test SSH access:"
    echo "  ssh -o PreferredAuthentications=publickey $USER@localhost"
    echo ""
    echo "If you can't connect, rollback with:"
    echo "  sudo rm /etc/ssh/sshd_config.d/99-security-hardening.conf"
    echo "  sudo systemctl restart $SSH_SERVICE"
}

# Main execution
echo "This script will properly configure SSH hardening for your system."
echo ""

# Check SSH service
check_ssh_service

# Check if SSH is needed
if ! check_ssh_necessity; then
    echo -e "${GREEN}SSH services disabled - no hardening needed${NC}"
    exit 0
fi

# Setup SSH keys
if setup_ssh_keys; then
    # Apply hardening
    if apply_ssh_hardening; then
        test_ssh_config
        echo ""
        echo -e "${GREEN}🎯 SSH Hardening Complete!${NC}"
        echo "=========================="
        echo ""
        echo "Your SSH server is now hardened with:"
        echo "  ✅ Key-based authentication only"
        echo "  ✅ Root login disabled"
        echo "  ✅ Strong cryptography"
        echo "  ✅ Session timeouts"
        echo "  ✅ Enhanced logging"
    else
        echo -e "${RED}❌ SSH hardening failed${NC}"
    fi
else
    echo -e "${YELLOW}SSH hardening skipped - please set up SSH keys first${NC}"
fi
