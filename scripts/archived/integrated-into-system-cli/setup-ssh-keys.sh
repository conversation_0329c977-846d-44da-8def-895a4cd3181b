#!/bin/bash
# SSH Key Setup Helper
# Guides you through setting up SSH keys for remote access

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔑 SSH Key Setup Helper${NC}"
echo "======================="
echo ""

# Function to confirm actions
confirm_action() {
    local message="$1"
    echo -e "${YELLOW}❓ $message${NC}"
    read -p "Do you want to proceed? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        return 0
    else
        return 1
    fi
}

# Check if SSH keys already exist
check_existing_keys() {
    echo -e "${BLUE}🔍 Checking for existing SSH keys${NC}"
    echo "--------------------------------"
    
    if [[ -d ~/.ssh ]]; then
        echo "SSH directory exists. Current keys:"
        ls -la ~/.ssh/*.pub 2>/dev/null || echo "No public keys found"
        
        if ls ~/.ssh/id_* >/dev/null 2>&1; then
            echo ""
            echo "Existing SSH key files:"
            ls -la ~/.ssh/id_*
            echo ""
            
            if confirm_action "You have existing SSH keys. Do you want to create new ones anyway?"; then
                return 0
            else
                echo -e "${GREEN}✅ Using existing SSH keys${NC}"
                show_existing_keys
                return 1
            fi
        fi
    else
        echo "No SSH directory found. Will create new keys."
    fi
    return 0
}

# Show existing keys
show_existing_keys() {
    echo -e "${BLUE}📋 Your existing SSH keys:${NC}"
    echo ""
    
    for key_file in ~/.ssh/id_*.pub; do
        if [[ -f "$key_file" ]]; then
            echo "Key file: $key_file"
            echo "Content:"
            cat "$key_file"
            echo ""
        fi
    done
    
    echo -e "${GREEN}✅ You can use these existing keys${NC}"
}

# Generate new SSH key
generate_ssh_key() {
    echo -e "${BLUE}🔧 Generating new SSH key${NC}"
    echo "-------------------------"
    
    # Get email for key comment
    read -p "Enter your email address for the key comment: " email
    if [[ -z "$email" ]]; then
        email="$USER@$(hostname)"
        echo "Using default: $email"
    fi
    
    # Choose key type
    echo ""
    echo "Choose SSH key type:"
    echo "1. Ed25519 (recommended, modern, secure)"
    echo "2. RSA 4096-bit (compatible with older systems)"
    read -p "Choice (1 or 2): " key_choice
    
    case $key_choice in
        1)
            key_type="ed25519"
            key_file="$HOME/.ssh/id_ed25519"
            ;;
        2)
            key_type="rsa"
            key_file="$HOME/.ssh/id_rsa"
            key_bits="-b 4096"
            ;;
        *)
            echo "Invalid choice, using Ed25519"
            key_type="ed25519"
            key_file="$HOME/.ssh/id_ed25519"
            ;;
    esac
    
    echo ""
    echo "Generating $key_type SSH key..."
    
    # Create SSH directory if it doesn't exist
    mkdir -p ~/.ssh
    chmod 700 ~/.ssh
    
    # Generate the key
    if [[ "$key_type" == "ed25519" ]]; then
        ssh-keygen -t ed25519 -C "$email" -f "$key_file"
    else
        ssh-keygen -t rsa $key_bits -C "$email" -f "$key_file"
    fi
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}✅ SSH key generated successfully!${NC}"
        echo ""
        echo "Key files created:"
        echo "  Private key: $key_file"
        echo "  Public key: $key_file.pub"
        echo ""
        
        # Set proper permissions
        chmod 600 "$key_file"
        chmod 644 "$key_file.pub"
        
        echo "Your public key:"
        echo "================"
        cat "$key_file.pub"
        echo ""
        
        return 0
    else
        echo -e "${RED}❌ Failed to generate SSH key${NC}"
        return 1
    fi
}

# Show how to use SSH keys
show_usage_instructions() {
    echo -e "${BLUE}📚 How to Use Your SSH Keys${NC}"
    echo "============================"
    echo ""
    
    echo -e "${YELLOW}For connecting to remote servers:${NC}"
    echo "1. Copy your public key to the server:"
    echo "   ssh-copy-id username@server-address"
    echo ""
    echo "2. Or manually add your public key to the server:"
    echo "   - Copy the public key content (shown above)"
    echo "   - On the server: echo 'your-public-key' >> ~/.ssh/authorized_keys"
    echo ""
    echo "3. Test the connection:"
    echo "   ssh username@server-address"
    echo ""
    
    echo -e "${YELLOW}For Git repositories (GitHub/GitLab):${NC}"
    echo "1. Copy your public key (shown above)"
    echo "2. Add it to your Git service:"
    echo "   - GitHub: Settings → SSH and GPG keys → New SSH key"
    echo "   - GitLab: Preferences → SSH Keys → Add key"
    echo "3. Test with: ssh -T **************"
    echo ""
    
    echo -e "${YELLOW}For development environments:${NC}"
    echo "- Docker containers with SSH"
    echo "- Virtual machines"
    echo "- Cloud development environments"
    echo ""
}

# Add key to SSH agent
setup_ssh_agent() {
    echo -e "${BLUE}🔄 Setting up SSH agent${NC}"
    echo "----------------------"
    
    if confirm_action "Add your SSH key to the SSH agent for easier use?"; then
        # Start SSH agent if not running
        if ! pgrep -x ssh-agent >/dev/null; then
            eval "$(ssh-agent -s)"
        fi
        
        # Add keys to agent
        for key_file in ~/.ssh/id_*; do
            if [[ -f "$key_file" && ! "$key_file" =~ \.pub$ ]]; then
                ssh-add "$key_file" 2>/dev/null && echo "Added: $key_file"
            fi
        done
        
        echo -e "${GREEN}✅ SSH keys added to agent${NC}"
        echo ""
        echo "Current keys in agent:"
        ssh-add -l 2>/dev/null || echo "No keys in agent"
    fi
    echo ""
}

# Main execution
echo "This script will help you set up SSH keys for connecting to remote servers."
echo ""

# Check what the user needs
echo -e "${YELLOW}Do you need SSH keys for:${NC}"
echo "1. Connecting to remote servers/VPS"
echo "2. Using Git with GitHub/GitLab"
echo "3. Development work with remote systems"
echo "4. I'm not sure / just learning"
echo ""

read -p "Enter your choice (1-4): " use_case

case $use_case in
    1|2|3|4)
        echo "Great! Let's set up SSH keys for you."
        ;;
    *)
        echo -e "${YELLOW}If you don't need to connect to remote servers, you don't need SSH keys.${NC}"
        echo "Your desktop security is already excellent without them!"
        exit 0
        ;;
esac

echo ""

# Check existing keys
if check_existing_keys; then
    # Generate new keys
    if generate_ssh_key; then
        setup_ssh_agent
        show_usage_instructions
        
        echo -e "${GREEN}🎯 SSH Key Setup Complete!${NC}"
        echo "=========================="
        echo ""
        echo "Your SSH keys are ready to use for:"
        echo "  ✅ Remote server access"
        echo "  ✅ Git repository authentication"
        echo "  ✅ Development environments"
        echo ""
        echo "Next steps:"
        echo "1. Copy your public key to any servers you need to access"
        echo "2. Add your public key to GitHub/GitLab if using Git"
        echo "3. Test connections to make sure everything works"
    fi
else
    echo "Using your existing SSH keys."
    setup_ssh_agent
    show_usage_instructions
fi
