#!/bin/bash
# Fixed SSH Hardening Script
# Addresses the SSH configuration issues found

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 Fixing SSH Hardening Issues${NC}"
echo "==============================="
echo ""

# Function to confirm actions
confirm_action() {
    local message="$1"
    echo -e "${YELLOW}⚠️  $message${NC}"
    read -p "Do you want to proceed? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        return 0
    else
        return 1
    fi
}

# 1. Fix SSH privilege separation directory
fix_ssh_directory() {
    echo -e "${BLUE}📁 Fixing SSH privilege separation directory${NC}"
    echo "--------------------------------------------"
    
    if [[ ! -d /run/sshd ]]; then
        echo "Creating missing /run/sshd directory..."
        sudo mkdir -p /run/sshd
        sudo chmod 755 /run/sshd
        echo -e "${GREEN}✅ Created /run/sshd directory${NC}"
    else
        echo -e "${GREEN}✅ /run/sshd directory already exists${NC}"
    fi
    
    # Make it persistent across reboots
    if ! grep -q "/run/sshd" /etc/tmpfiles.d/sshd.conf 2>/dev/null; then
        echo "Making directory persistent across reboots..."
        sudo tee /etc/tmpfiles.d/sshd.conf << 'EOF'
# SSH privilege separation directory
d /run/sshd 0755 root root -
EOF
        echo -e "${GREEN}✅ SSH directory will persist across reboots${NC}"
    fi
    echo ""
}

# 2. Check current SSH configuration
check_current_ssh() {
    echo -e "${BLUE}🔍 Checking current SSH configuration${NC}"
    echo "------------------------------------"
    
    echo "Current SSH daemon status:"
    systemctl status sshd --no-pager -l
    echo ""
    
    echo "Current SSH configuration test:"
    if sudo sshd -t; then
        echo -e "${GREEN}✅ Current SSH configuration is valid${NC}"
    else
        echo -e "${RED}❌ Current SSH configuration has issues${NC}"
        echo "Let's check what's wrong..."
        sudo sshd -T 2>&1 | head -10
    fi
    echo ""
}

# 3. Apply compatible SSH hardening
apply_compatible_hardening() {
    echo -e "${BLUE}🔐 Applying Compatible SSH Hardening${NC}"
    echo "-----------------------------------"
    
    if confirm_action "Apply SSH security hardening (compatible version)?"; then
        echo "Creating compatible SSH hardening configuration..."
        
        # Remove any existing hardening config
        sudo rm -f /etc/ssh/sshd_config.d/99-security-hardening.conf
        
        # Create a more compatible hardening config
        sudo tee /etc/ssh/sshd_config.d/99-security-hardening.conf << 'EOF'
# SSH Security Hardening Configuration
# Compatible with Ubuntu/Debian systems

# Disable root login
PermitRootLogin no

# Disable password authentication (use keys only)
PasswordAuthentication no
ChallengeResponseAuthentication no
UsePAM yes

# Enable public key authentication
PubkeyAuthentication yes

# Limit authentication attempts
MaxAuthTries 3

# Session timeouts
ClientAliveInterval 300
ClientAliveCountMax 2

# Limit concurrent sessions
MaxSessions 4

# Disable X11 forwarding for security
X11Forwarding no

# Disable TCP forwarding
AllowTcpForwarding no

# Disable gateway ports
GatewayPorts no

# Disable tunneling
PermitTunnel no

# Use strong ciphers only
Ciphers <EMAIL>,<EMAIL>,<EMAIL>,aes256-ctr,aes192-ctr,aes128-ctr

# Use strong MACs
MACs <EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha2-512

# Use strong key exchange algorithms
KexAlgorithms <EMAIL>,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512

# Disable empty passwords
PermitEmptyPasswords no

# Log more information
LogLevel VERBOSE
EOF
        
        echo "Testing SSH configuration..."
        if sudo sshd -t; then
            echo -e "${GREEN}✅ SSH configuration is valid${NC}"
            
            if confirm_action "Restart SSH service to apply changes?"; then
                echo "Restarting SSH service..."
                sudo systemctl restart sshd
                
                if systemctl is-active --quiet sshd; then
                    echo -e "${GREEN}✅ SSH service restarted successfully${NC}"
                    echo ""
                    echo "SSH hardening applied successfully!"
                    echo "New security settings:"
                    echo "  • Root login: DISABLED"
                    echo "  • Password auth: DISABLED (keys only)"
                    echo "  • Max auth tries: 3"
                    echo "  • Session timeout: 5 minutes"
                    echo "  • X11 forwarding: DISABLED"
                    echo "  • TCP forwarding: DISABLED"
                    echo "  • Strong crypto: ENABLED"
                else
                    echo -e "${RED}❌ SSH service failed to start${NC}"
                    echo "Rolling back configuration..."
                    sudo rm /etc/ssh/sshd_config.d/99-security-hardening.conf
                    sudo systemctl restart sshd
                fi
            else
                echo -e "${YELLOW}⏭️  SSH restart skipped - configuration saved but not active${NC}"
            fi
        else
            echo -e "${RED}❌ SSH configuration has errors${NC}"
            echo "Configuration errors:"
            sudo sshd -t
            echo ""
            echo "Removing problematic configuration..."
            sudo rm /etc/ssh/sshd_config.d/99-security-hardening.conf
        fi
    else
        echo -e "${YELLOW}⏭️  SSH hardening skipped${NC}"
    fi
    echo ""
}

# 4. Verify SSH keys are set up
verify_ssh_keys() {
    echo -e "${BLUE}🔑 Verifying SSH Key Setup${NC}"
    echo "-------------------------"
    
    if [[ -f ~/.ssh/authorized_keys ]]; then
        key_count=$(wc -l < ~/.ssh/authorized_keys)
        echo -e "${GREEN}✅ SSH authorized_keys found ($key_count keys)${NC}"
        
        echo "Your SSH public keys:"
        while read -r line; do
            if [[ -n "$line" && ! "$line" =~ ^# ]]; then
                key_type=$(echo "$line" | awk '{print $1}')
                key_comment=$(echo "$line" | awk '{print $3}')
                echo "  • $key_type key: $key_comment"
            fi
        done < ~/.ssh/authorized_keys
    else
        echo -e "${YELLOW}⚠️  No SSH authorized_keys found${NC}"
        echo ""
        echo "IMPORTANT: You need to set up SSH keys before disabling password auth!"
        echo ""
        echo "To set up SSH keys:"
        echo "1. Generate a key pair: ssh-keygen -t ed25519 -C '<EMAIL>'"
        echo "2. Copy public key to server: ssh-copy-id user@server"
        echo "3. Test key-based login before applying hardening"
        echo ""
        
        if confirm_action "Do you want to skip SSH hardening until keys are set up?"; then
            echo -e "${YELLOW}⏭️  SSH hardening skipped - set up keys first${NC}"
            return 1
        fi
    fi
    echo ""
    return 0
}

# 5. Test SSH connection
test_ssh_connection() {
    echo -e "${BLUE}🧪 Testing SSH Configuration${NC}"
    echo "----------------------------"
    
    echo "Testing SSH daemon configuration:"
    if sudo sshd -T >/dev/null 2>&1; then
        echo -e "${GREEN}✅ SSH configuration syntax is correct${NC}"
    else
        echo -e "${RED}❌ SSH configuration has syntax errors${NC}"
        sudo sshd -T 2>&1 | head -5
    fi
    
    echo ""
    echo "Current SSH service status:"
    systemctl status sshd --no-pager -l | head -10
    
    echo ""
    echo -e "${YELLOW}IMPORTANT:${NC}"
    echo "Before closing this terminal, test SSH access in a new terminal:"
    echo "  ssh -o PreferredAuthentications=publickey $USER@localhost"
    echo ""
    echo "If you can't connect, you can restore the original config with:"
    echo "  sudo rm /etc/ssh/sshd_config.d/99-security-hardening.conf"
    echo "  sudo systemctl restart sshd"
}

# Main execution
echo "This script will fix the SSH hardening issues and apply compatible security settings."
echo ""

# Run fixes
fix_ssh_directory
check_current_ssh

# Verify SSH keys before hardening
if verify_ssh_keys; then
    apply_compatible_hardening
    test_ssh_connection
else
    echo -e "${YELLOW}SSH hardening skipped - please set up SSH keys first${NC}"
fi

echo ""
echo -e "${GREEN}🎯 SSH Hardening Fix Complete!${NC}"
echo "==============================="
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Test SSH access in a new terminal"
echo "2. If successful, your SSH is now hardened"
echo "3. If issues, restore with the rollback commands shown above"
echo "4. Continue with firewall configuration"
