#!/bin/bash
# Authentication System Complexity Audit Script
# Analyzes authentication system complexity and identifies issues

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Output file
OUTPUT_FILE="$HOME/.cache/auth-complexity-audit-$(date +%Y%m%d-%H%M%S).log"
mkdir -p "$(dirname "$OUTPUT_FILE")"

echo "🔍 Authentication System Complexity Audit"
echo "=========================================="
echo "Output will be saved to: $OUTPUT_FILE"
echo ""

# Function to log and display
log_and_display() {
    echo -e "$1" | tee -a "$OUTPUT_FILE"
}

# Function to run command and capture output
run_audit_command() {
    local description="$1"
    local command="$2"
    
    log_and_display "${BLUE}## $description${NC}"
    log_and_display "Command: $command"
    log_and_display "---"
    
    if eval "$command" >> "$OUTPUT_FILE" 2>&1; then
        log_and_display "${GREEN}✅ Success${NC}"
    else
        log_and_display "${RED}❌ Failed or not available${NC}"
    fi
    log_and_display ""
}

# 1. Keyring Daemon Analysis
log_and_display "${YELLOW}🔐 KEYRING DAEMON ANALYSIS${NC}"
log_and_display "=========================="

run_audit_command "Keyring Daemon Processes" "ps aux | grep gnome-keyring-daemon | grep -v grep"

run_audit_command "Keyring Service Status" "systemctl --user status gnome-keyring-daemon --no-pager"

run_audit_command "Keyring Files" "ls -la ~/.local/share/keyrings/"

run_audit_command "Keyring Control Directory" "ls -la /run/user/$(id -u)/keyring/"

# 2. Biometric Authentication Analysis
log_and_display "${YELLOW}👆 BIOMETRIC AUTHENTICATION ANALYSIS${NC}"
log_and_display "===================================="

run_audit_command "Fingerprint Service Status" "systemctl status fprintd --no-pager"

run_audit_command "Enrolled Fingerprints" "fprintd-list $USER"

run_audit_command "Fingerprint Configuration" "sudo cat /etc/fprintd.conf"

run_audit_command "Legacy Biometric Systems" "ls -la /etc/biometric-auth/ 2>/dev/null || echo 'Not found'"

# 3. TPM Analysis
log_and_display "${YELLOW}🔒 TPM ANALYSIS${NC}"
log_and_display "==============="

run_audit_command "TPM Devices" "ls -la /dev/tpm* 2>/dev/null || echo 'No TPM devices found'"

run_audit_command "TPM Capabilities" "sudo tpm2_getcap properties-fixed 2>/dev/null | head -10 || echo 'TPM tools not available'"

run_audit_command "TPM Configuration" "sudo cat /etc/tpm2-tss/fapi-config.json 2>/dev/null || echo 'Config not found'"

run_audit_command "TPM-related Packages" "dpkg -l | grep tpm | grep -v '^rc'"

# 4. PAM Configuration Analysis
log_and_display "${YELLOW}🔑 PAM CONFIGURATION ANALYSIS${NC}"
log_and_display "============================="

run_audit_command "PAM Common Auth" "sudo cat /etc/pam.d/common-auth"

run_audit_command "PAM Common Session" "sudo cat /etc/pam.d/common-session"

run_audit_command "PAM Common Password" "sudo cat /etc/pam.d/common-password"

# 5. Authentication Flow Analysis
log_and_display "${YELLOW}🔄 AUTHENTICATION FLOW ANALYSIS${NC}"
log_and_display "==============================="

run_audit_command "Authentication Logs (last 50 lines)" "sudo tail -50 /var/log/auth.log | grep -E '(pam_|fprintd|keyring)' || echo 'No recent auth events'"

run_audit_command "Keyring Daemon Logs" "journalctl --user -u gnome-keyring-daemon --no-pager -n 20 || echo 'No logs available'"

run_audit_command "Fingerprint Daemon Logs" "journalctl -u fprintd --no-pager -n 20 || echo 'No logs available'"

# 6. Security Assessment
log_and_display "${YELLOW}🛡️ SECURITY ASSESSMENT${NC}"
log_and_display "======================"

# Count issues
keyring_processes=$(ps aux | grep gnome-keyring-daemon | grep -v grep | wc -l)
tpm_available=$(ls /dev/tpm* 2>/dev/null | wc -l)
legacy_biometric=$(ls /etc/biometric-auth/ 2>/dev/null | wc -l)

log_and_display "Issue Summary:"
log_and_display "- Keyring daemon processes: $keyring_processes"
if [ "$keyring_processes" -gt 2 ]; then
    log_and_display "  ${RED}⚠️  Multiple keyring processes detected${NC}"
fi

log_and_display "- TPM devices available: $tpm_available"
if [ "$tpm_available" -gt 0 ]; then
    log_and_display "  ${YELLOW}💡 TPM available but may not be integrated${NC}"
fi

log_and_display "- Legacy biometric files: $legacy_biometric"
if [ "$legacy_biometric" -gt 0 ]; then
    log_and_display "  ${YELLOW}⚠️  Legacy biometric system detected${NC}"
fi

# 7. Recommendations
log_and_display "${YELLOW}💡 RECOMMENDATIONS${NC}"
log_and_display "=================="

log_and_display "Based on the audit findings:"
log_and_display ""

if [ "$keyring_processes" -gt 2 ]; then
    log_and_display "${RED}HIGH PRIORITY:${NC} Clean up multiple keyring daemon processes"
    log_and_display "  - Kill orphaned processes: pkill -f 'gnome-keyring-daemon --print-environment'"
    log_and_display "  - Restart keyring service: systemctl --user restart gnome-keyring-daemon"
    log_and_display ""
fi

if [ "$tpm_available" -gt 0 ]; then
    log_and_display "${YELLOW}MEDIUM PRIORITY:${NC} Integrate TPM with keyring system"
    log_and_display "  - Consider using TPM for hardware-backed key storage"
    log_and_display "  - Evaluate clevis-tpm2 for automatic keyring unlock"
    log_and_display ""
fi

if [ "$legacy_biometric" -gt 0 ]; then
    log_and_display "${YELLOW}MEDIUM PRIORITY:${NC} Remove legacy biometric system"
    log_and_display "  - Remove /etc/biometric-auth/ directory"
    log_and_display "  - Uninstall unused biometric packages"
    log_and_display ""
fi

log_and_display "${GREEN}✅ Audit completed successfully${NC}"
log_and_display "Full report saved to: $OUTPUT_FILE"
log_and_display ""
log_and_display "Next steps:"
log_and_display "1. Review the detailed report"
log_and_display "2. Address high-priority issues first"
log_and_display "3. Plan TPM integration for enhanced security"
log_and_display "4. Consider running this audit monthly"
