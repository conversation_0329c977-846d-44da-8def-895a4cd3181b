# Scripts Integrated into system-cli

These scripts have been successfully integrated into the `system-cli` Python CLI tool. **Use the CLI commands instead of running these scripts directly.**

## Integration Mapping

### Authentication Commands (`system-cli auth`)

| Script | CLI Command | Description |
|--------|-------------|-------------|
| `auth-flow-simple.sh` | `system-cli auth flow-simple` | Simple authentication flow management |
| `auth-flow-standardization.sh` | `system-cli auth standardize` | Standardize authentication flow |
| `authentication-complexity-audit.sh` | `system-cli auth complexity-audit` | Authentication complexity audit |

**Examples:**
```bash
# Setup simple authentication flow
system-cli auth flow-simple --setup

# Standardize authentication
system-cli auth standardize --setup

# Run authentication audit
system-cli auth complexity-audit --detailed
```

### Security Commands (`system-cli security`)

| Script | CLI Command | Description |
|--------|-------------|-------------|
| `fix-ssh-hardening-corrected.sh` | `system-cli security ssh-harden` | Harden SSH configuration |
| `fix-ssh-hardening.sh` | *(deprecated)* | Old SSH hardening script |

**Examples:**
```bash
# Harden SSH configuration
system-cli security ssh-harden

# Run authentication audit (also available here)
system-cli security auth-audit
```

### System Management Commands (`system-cli system`)

| Script | CLI Command | Description |
|--------|-------------|-------------|
| `power-management-check.py` | `system-cli system power-check` | Check power management configuration |
| `power-management-summary.py` | `system-cli system power-summary` | Generate power management summary |
| `optimize-etc-default-services.sh` | `system-cli system optimize-services` | Optimize /etc/default services |
| `optimize-etc-default.sh` | `system-cli system optimize-defaults` | Optimize /etc/default configuration |

**Examples:**
```bash
# Check power management
system-cli system power-check

# Generate power summary
system-cli system power-summary

# Optimize system services
system-cli system optimize-services
```

### Monitoring Commands (`system-cli monitor`)

| Script | CLI Command | Description |
|--------|-------------|-------------|
| `power-monitor.py` | `system-cli monitor power` | Monitor power usage |
| `configure-email-alerts.sh` | `system-cli monitor email-setup` | Configure email alerts |
| `cron-security-wrapper.sh` | `system-cli monitor cron-wrapper` | Run commands with cron security wrapper |

**Examples:**
```bash
# Monitor power usage
system-cli monitor power

# Setup email alerts
system-cli monitor email-setup setup --email <EMAIL>

# Run command with security wrapper
system-cli monitor cron-wrapper "backup-command"
```

### Setup Commands (`system-cli setup`)

| Script | CLI Command | Description |
|--------|-------------|-------------|
| `setup-ssh-keys.sh` | `system-cli setup ssh-keys-interactive` | Interactive SSH key setup |
| `phase2-completion.sh` | `system-cli setup phase2-complete` | Complete phase 2 implementation |
| `phase2-implementation.sh` | `system-cli setup phase2-implement` | Implement phase 2 setup |
| `phase3-pam-optimization.sh` | `system-cli setup phase3-pam` | Optimize PAM configuration |

**Examples:**
```bash
# Interactive SSH key setup
system-cli setup ssh-keys-interactive

# Phase implementations
system-cli setup phase2-implement
system-cli setup phase2-complete
system-cli setup phase3-pam
```

### Keyring Commands (`system-cli keyring`)

| Script | CLI Command | Description |
|--------|-------------|-------------|
| `simple-tpm-setup.sh` | `system-cli keyring tpm-setup-advanced --method basic` | Basic TPM setup |
| `tpm-integration-improved.sh` | `system-cli keyring tpm-setup-advanced --method improved` | Improved TPM integration |
| `tmp-keyring-integration.sh` | `system-cli keyring tpm-setup` | Standard TPM setup |
| `tpm-setup-fixed.sh` | `system-cli keyring tpm-setup-advanced --method fixed` | Fixed TPM setup |
| `tpm-setup-simple.sh` | `system-cli keyring tpm-setup-advanced --method simple` | Simple TPM setup |
| `tpm-setup-with-permissions.sh` | `system-cli keyring tpm-setup-advanced --permissions` | TPM setup with permissions |
| `tpm-simple-no-pcr.sh` | `system-cli keyring tpm-setup-advanced --method no-pcr` | TPM setup without PCR |

**Examples:**
```bash
# Standard TPM setup
system-cli keyring tpm-setup

# Advanced TPM setup with different methods
system-cli keyring tpm-setup-advanced --method improved
system-cli keyring tpm-setup-advanced --method simple
system-cli keyring tpm-setup-advanced --method basic
system-cli keyring tpm-setup-advanced --permissions
```

### Maintenance Commands (`system-cli maintenance`)

| Script | CLI Command | Description |
|--------|-------------|-------------|
| `config-cleanup.sh` | `system-cli maintenance config-cleanup` | Clean up configuration files |

**Examples:**
```bash
# Clean up configuration files
system-cli maintenance config-cleanup
```

## Benefits of CLI Integration

### 🎯 Consistent Interface
- All commands follow the same pattern: `system-cli <category> <command> [options]`
- Unified help system: `system-cli --help`, `system-cli auth --help`
- Consistent option naming and behavior

### 🛡️ Better Error Handling
- Rich error messages with context
- Progress indicators for long-running operations
- Graceful failure handling

### 📚 Built-in Documentation
- Comprehensive help for all commands
- Examples and usage information
- Auto-generated documentation

### 🧪 Testing & Validation
- Input validation and type checking
- Integrated testing framework
- Dry-run options where applicable

### 🎨 Rich Output
- Colored output and formatting
- Tables and panels for structured data
- Progress bars and status indicators

## Migration Examples

### Before (Script-based)
```bash
# Multiple different interfaces
./scripts/auth-flow-simple.sh setup
./scripts/power-management-check.py
./scripts/tpm-integration-improved.sh setup
./scripts/fix-ssh-hardening-corrected.sh
```

### After (CLI-based)
```bash
# Consistent interface
system-cli auth flow-simple --setup
system-cli system power-check
system-cli keyring tpm-setup-advanced --method improved
system-cli security ssh-harden
```

## Emergency Usage

If you need to use the original scripts (not recommended):

```bash
# Make sure they're executable
chmod +x scripts/archived/integrated-into-system-cli/*.sh

# Run directly (not recommended)
./scripts/archived/integrated-into-system-cli/script-name.sh
```

**Note:** The CLI commands are the preferred and supported method. These archived scripts are kept for reference only.
