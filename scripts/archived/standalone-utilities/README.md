# Standalone Utility Scripts

These scripts provide alternative implementations or specialized functionality that remains available as standalone utilities.

## Available Utilities

### 🔐 secure-keyring-manager.sh

**Purpose**: Alternative keyring manager with GUI support and enhanced features.

**Features:**
- GUI password prompts using zenity
- Session timeout management  
- Status caching for performance
- Alternative to `system-cli keyring` commands
- Enhanced error handling and user feedback

**Usage:**
```bash
# Run the secure keyring manager
./scripts/archived/standalone-utilities/secure-keyring-manager.sh

# Make executable if needed
chmod +x scripts/archived/standalone-utilities/secure-keyring-manager.sh
```

**When to Use:**
- When you prefer GUI password prompts over terminal prompts
- For automated scripts that need timeout management
- When you need the enhanced status caching features
- As a fallback if the main CLI has issues

**Comparison with CLI:**

| Feature | secure-keyring-manager.sh | system-cli keyring |
|---------|---------------------------|-------------------|
| Interface | GUI (zenity) + CLI | CLI only |
| Session Management | Advanced timeout handling | Basic session handling |
| Status Caching | Yes | No |
| Progress Indicators | Basic | Rich progress bars |
| Error Handling | Good | Excellent |
| Integration | Standalone | Part of comprehensive CLI |
| Maintenance | Manual | Automated testing |

## Usage Guidelines

### When to Use Standalone Utilities

1. **GUI Requirements**: When you need graphical password prompts
2. **Specialized Features**: When you need specific features not in the CLI
3. **Legacy Compatibility**: When maintaining older workflows
4. **Emergency Fallback**: When the main CLI is unavailable

### When to Use CLI Commands

1. **General Use**: For most day-to-day operations
2. **Automation**: For scripts and automated workflows  
3. **Consistency**: When using other system-cli commands
4. **Support**: For actively maintained and tested functionality

## Installation & Setup

### Prerequisites

For `secure-keyring-manager.sh`:
- zenity (for GUI prompts)
- GNOME keyring
- Standard Unix utilities (ps, grep, etc.)

### Installation

```bash
# Install zenity if not present
sudo apt install zenity

# Make script executable
chmod +x scripts/archived/standalone-utilities/secure-keyring-manager.sh

# Optional: Create symlink for easier access
ln -s $(pwd)/scripts/archived/standalone-utilities/secure-keyring-manager.sh ~/.local/bin/secure-keyring-manager
```

## Examples

### Using secure-keyring-manager.sh

```bash
# Basic usage - will show GUI prompts
./scripts/archived/standalone-utilities/secure-keyring-manager.sh

# Check if it's working
echo $?  # Should return 0 for success
```

### Integration with Other Tools

```bash
# Use in a script that needs GUI prompts
#!/bin/bash
if ./scripts/archived/standalone-utilities/secure-keyring-manager.sh; then
    echo "Keyring unlocked successfully"
    # Continue with operations that need keyring access
else
    echo "Failed to unlock keyring"
    exit 1
fi
```

## Maintenance

### Updates
- These utilities are maintained separately from the main CLI
- Check for updates manually or create your own update mechanism
- Consider migrating to CLI equivalents when possible

### Support
- Limited support compared to integrated CLI commands
- Community-driven maintenance
- Use GitHub issues for bug reports

### Migration Path
- Gradually migrate workflows to use `system-cli` commands
- Keep standalone utilities as fallback options
- Document any custom modifications you make

## Contributing

If you improve these standalone utilities:

1. Test thoroughly in your environment
2. Document changes in comments
3. Consider if the improvement should be added to the main CLI
4. Share improvements via GitHub issues or pull requests

## Security Considerations

- These scripts run with your user privileges
- GUI prompts may be visible to other users on the system
- Session management features should be reviewed for your security requirements
- Consider the security implications of any modifications you make

## Troubleshooting

### Common Issues

**zenity not found:**
```bash
sudo apt install zenity
```

**Permission denied:**
```bash
chmod +x scripts/archived/standalone-utilities/secure-keyring-manager.sh
```

**GUI not working in SSH:**
- Ensure X11 forwarding is enabled: `ssh -X user@host`
- Or use the CLI commands instead: `system-cli keyring`

**Script not found:**
- Use full path: `./scripts/archived/standalone-utilities/secure-keyring-manager.sh`
- Or add to PATH: `export PATH=$PATH:$(pwd)/scripts/archived/standalone-utilities`
