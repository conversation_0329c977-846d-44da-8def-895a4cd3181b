#!/bin/bash
# Secure Keyring Manager for Fingerprint Authentication
# Maintains keyring security while providing better UX

KEYRING_STATUS_FILE="$HOME/.cache/keyring-unlocked"
KEYRING_TIMEOUT=3600  # 1 hour timeout

check_keyring_status() {
    # Check if keyring is already unlocked
    if gnome-keyring-daemon --print-environment | grep -q "GNOME_KEYRING_CONTROL"; then
        # Check if our status file exists and is recent
        if [[ -f "$KEYRING_STATUS_FILE" ]]; then
            local last_unlock=$(stat -c %Y "$KEYRING_STATUS_FILE" 2>/dev/null || echo 0)
            local current_time=$(date +%s)
            local time_diff=$((current_time - last_unlock))
            
            if [[ $time_diff -lt $KEYRING_TIMEOUT ]]; then
                return 0  # Keyring is unlocked and recent
            fi
        fi
    fi
    return 1  # Keyring needs unlocking
}

unlock_keyring_secure() {
    echo "🔐 Keyring access required for secure credential storage."
    echo "Please enter your password to unlock the keyring:"
    
    # Use zenity for GUI password prompt if available
    if command -v zenity >/dev/null 2>&1; then
        local password=$(zenity --password --title="Unlock Keyring" \
                        --text="Enter your password to unlock the secure keyring:")
        if [[ -n "$password" ]]; then
            echo "$password" | gnome-keyring-daemon --unlock >/dev/null 2>&1
            if [[ $? -eq 0 ]]; then
                touch "$KEYRING_STATUS_FILE"
                echo "✅ Keyring unlocked successfully."
                return 0
            fi
        fi
    else
        # Fallback to command line
        echo -n "Password: "
        read -s password
        echo
        echo "$password" | gnome-keyring-daemon --unlock >/dev/null 2>&1
        if [[ $? -eq 0 ]]; then
            touch "$KEYRING_STATUS_FILE"
            echo "✅ Keyring unlocked successfully."
            return 0
        fi
    fi
    
    echo "❌ Failed to unlock keyring."
    return 1
}

# Main logic
if ! check_keyring_status; then
    unlock_keyring_secure
fi
