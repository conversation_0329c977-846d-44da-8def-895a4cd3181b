#!/usr/bin/env python3
"""
Migration helper script to transition from Makefile to dev.py CLI.
This script helps users understand the mapping between Makefile targets and dev CLI commands.
"""

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

console = Console()
app = typer.Typer(help="Migration helper for transitioning from Makefile to dev.py CLI")

# Mapping of Makefile targets to dev CLI commands
MAKEFILE_TO_DEV_CLI = {
    # Installation targets
    "install": "python3 dev.py install base",
    "install-dev": "python3 dev.py install dev", 
    "install-all": "python3 dev.py install all",
    
    # Testing targets
    "test": "python3 dev.py test all",
    "test-fast": "python3 dev.py test fast",
    "test-unit": "python3 dev.py test unit",
    "test-integration": "python3 dev.py test integration",
    "test-e2e": "python3 dev.py test e2e",
    
    # Linting and formatting targets
    "lint": "python3 dev.py lint all",
    "format": "python3 dev.py lint format",
    "type-check": "python3 dev.py lint type-check",
    "security": "python3 dev.py lint security",
    
    # Build targets
    "build": "python3 dev.py build dist",
    "clean": "python3 dev.py build clean",
    "check": "python3 dev.py build check",
    
    # Documentation targets
    "docs": "python3 dev.py docs build",
    "serve-docs": "python3 dev.py docs serve",
    
    # CLI testing targets
    "cli-test": "python3 dev.py cli test",
    "cli-demo": "python3 dev.py cli demo",
    
    # Development workflow targets
    "dev-setup": "python3 dev.py dev-setup",
    "pre-commit": "python3 dev.py pre-commit",
    
    # Version targets
    "version-patch": "python3 dev.py version patch",
    "version-minor": "python3 dev.py version minor", 
    "version-major": "python3 dev.py version major",
    
    # Quality assurance
    "qa": "python3 dev.py qa",
    "dev-cycle": "python3 dev.py dev-cycle",
    
    # Coverage targets
    "coverage-report": "python3 dev.py coverage report",
    "coverage-html": "python3 dev.py coverage html",
    
    # Dependency management
    "deps-update": "python3 dev.py deps update",
    "deps-check": "python3 dev.py deps check",
    "requirements": "python3 dev.py deps requirements",
    
    # Utility targets
    "env-info": "python3 dev.py env-info",
    "verify-install": "python3 dev.py verify-install",
    "benchmark": "python3 dev.py benchmark",
}


@app.command("mapping")
def show_mapping():
    """Show complete mapping from Makefile targets to dev CLI commands."""
    table = Table(title="Makefile to Dev CLI Migration Guide")
    table.add_column("Makefile Target", style="red", width=20)
    table.add_column("Dev CLI Command", style="green", width=40)
    table.add_column("Category", style="blue", width=15)
    
    categories = {
        "install": "Installation",
        "test": "Testing", 
        "lint": "Code Quality",
        "build": "Build",
        "docs": "Documentation",
        "cli": "CLI Testing",
        "dev": "Development",
        "version": "Versioning",
        "qa": "Quality Assurance",
        "coverage": "Coverage",
        "deps": "Dependencies",
        "env": "Environment",
        "verify": "Verification",
        "benchmark": "Performance",
    }
    
    for make_target, dev_command in MAKEFILE_TO_DEV_CLI.items():
        # Determine category
        category = "Other"
        for key, cat in categories.items():
            if key in make_target:
                category = cat
                break
        
        table.add_row(f"make {make_target}", dev_command, category)
    
    console.print(table)


@app.command("lookup")
def lookup_command(makefile_target: str):
    """Look up the dev CLI equivalent for a specific Makefile target."""
    if makefile_target in MAKEFILE_TO_DEV_CLI:
        dev_command = MAKEFILE_TO_DEV_CLI[makefile_target]
        console.print(Panel.fit(
            f"[red]make {makefile_target}[/red] → [green]{dev_command}[/green]",
            title="Command Translation"
        ))
    else:
        console.print(f"[red]❌ Makefile target '{makefile_target}' not found[/red]")
        console.print("\n[yellow]Available targets:[/yellow]")
        for target in sorted(MAKEFILE_TO_DEV_CLI.keys()):
            console.print(f"  - {target}")


@app.command("categories")
def show_categories():
    """Show commands organized by category."""
    categories = {}
    
    for make_target, dev_command in MAKEFILE_TO_DEV_CLI.items():
        if make_target.startswith("install"):
            category = "Installation"
        elif make_target.startswith("test"):
            category = "Testing"
        elif make_target in ["lint", "format", "type-check", "security"]:
            category = "Code Quality"
        elif make_target in ["build", "clean", "check"]:
            category = "Build & Distribution"
        elif make_target.startswith("docs") or "docs" in make_target:
            category = "Documentation"
        elif make_target.startswith("cli"):
            category = "CLI Testing"
        elif make_target.startswith("version"):
            category = "Version Management"
        elif make_target.startswith("coverage"):
            category = "Coverage Reporting"
        elif make_target.startswith("deps"):
            category = "Dependency Management"
        else:
            category = "Development Workflow"
        
        if category not in categories:
            categories[category] = []
        categories[category].append((make_target, dev_command))
    
    for category, commands in categories.items():
        console.print(f"\n[bold blue]{category}[/bold blue]")
        for make_target, dev_command in commands:
            console.print(f"  [red]make {make_target:<20}[/red] → [green]{dev_command}[/green]")


@app.command("quick-start")
def quick_start():
    """Show quick start guide for common commands."""
    console.print(Panel.fit(
        "[bold blue]Quick Start Guide[/bold blue]\n\n"
        "Most commonly used commands:",
        title="Dev CLI Quick Start"
    ))
    
    common_commands = [
        ("install-dev", "Set up development environment"),
        ("test", "Run tests with coverage"),
        ("lint", "Run all code quality checks"),
        ("format", "Format code"),
        ("build", "Build distribution packages"),
        ("clean", "Clean build artifacts"),
        ("qa", "Run comprehensive quality checks"),
    ]
    
    table = Table()
    table.add_column("Old Command", style="red")
    table.add_column("New Command", style="green")
    table.add_column("Description", style="blue")
    
    for make_target, description in common_commands:
        dev_command = MAKEFILE_TO_DEV_CLI[make_target]
        table.add_row(f"make {make_target}", dev_command, description)
    
    console.print(table)
    
    console.print("\n[yellow]💡 Tips:[/yellow]")
    console.print("  • Use [cyan]python3 dev.py --help[/cyan] to see all available commands")
    console.print("  • Use [cyan]python3 dev.py <group> --help[/cyan] for group-specific help")
    console.print("  • The dev CLI provides better error messages and colored output")


@app.command("install-deps")
def install_deps():
    """Install dependencies needed for the dev CLI."""
    import subprocess
    
    console.print("[blue]Installing dev CLI dependencies...[/blue]")
    
    try:
        subprocess.run(["pip", "install", "-r", "dev-requirements.txt"], check=True)
        console.print("[green]✅ Dependencies installed successfully[/green]")
    except subprocess.CalledProcessError:
        console.print("[red]❌ Failed to install dependencies[/red]")
        console.print("[yellow]Try manually: pip install typer rich[/yellow]")
    except FileNotFoundError:
        console.print("[yellow]⚠️  dev-requirements.txt not found[/yellow]")
        console.print("[blue]Installing manually...[/blue]")
        try:
            subprocess.run(["pip", "install", "typer", "rich"], check=True)
            console.print("[green]✅ Dependencies installed successfully[/green]")
        except subprocess.CalledProcessError:
            console.print("[red]❌ Failed to install dependencies[/red]")


@app.callback()
def main():
    """
    Migration helper for transitioning from Makefile to dev.py CLI.
    
    This tool helps you understand how to replace Makefile commands with the new dev CLI.
    """
    pass


if __name__ == "__main__":
    app()
