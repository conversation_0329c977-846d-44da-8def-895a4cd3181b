#!/bin/bash
# Enhanced TPM-Keyring Integration with Robust Fallback Logic
# Provides multiple authentication methods with graceful degradation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
CLEVIS_CONFIG_FILE="$TPM_KEYRING_DIR/clevis-config.json"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"
FALLBACK_CONFIG_FILE="$TPM_KEYRING_DIR/fallback-config.json"
LOG_FILE="$HOME/.cache/tpm-keyring-enhanced.log"
EMERGENCY_SCRIPT="$TPM_KEYRING_DIR/emergency-recovery.sh"

# PCR values to bind to (system state)
DEFAULT_PCRS="0,1,7"

# Fallback configuration
UNLOCK_TIMEOUT=10
MAX_RETRY_ATTEMPTS=3
FALLBACK_METHODS=("tpm" "fingerprint" "password")

# Functions
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
    log_message "INFO: $1"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
    log_message "SUCCESS: $1"
}

show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    log_message "WARNING: $1"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
    log_message "ERROR: $1"
}

show_info() {
    echo -e "${CYAN}💡 $1${NC}"
}

check_prerequisites() {
    show_status "Checking enhanced prerequisites..."
    
    local missing_deps=()
    
    # Check required commands
    for cmd in tpm2_getcap clevis secret-tool gnome-keyring-daemon systemctl; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_deps+=("$cmd")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        show_error "Missing dependencies: ${missing_deps[*]}"
        echo "Install with: sudo apt install tpm2-tools clevis libsecret-tools gnome-keyring"
        return 1
    fi
    
    # Check TPM device
    if [ ! -e /dev/tpm0 ] && [ ! -e /dev/tpmrm0 ]; then
        show_error "No TPM device found"
        return 1
    fi
    
    # Check user groups
    if ! groups | grep -q tss; then
        show_warning "User not in 'tss' group. Adding..."
        sudo usermod -a -G tss "$USER"
        show_info "Please log out and back in for group changes to take effect"
    fi
    
    # Test TPM access
    if ! tpm2_getcap properties-fixed >/dev/null 2>&1; then
        show_warning "TPM access test failed, but continuing..."
    fi
    
    show_success "Enhanced prerequisites check completed"
    return 0
}

setup_directories() {
    show_status "Setting up enhanced directory structure..."
    
    mkdir -p "$TPM_KEYRING_DIR"
    chmod 700 "$TPM_KEYRING_DIR"
    
    mkdir -p "$(dirname "$LOG_FILE")"
    
    show_success "Directory structure created"
}

create_fallback_config() {
    show_status "Creating fallback configuration..."
    
    cat > "$FALLBACK_CONFIG_FILE" << EOF
{
    "version": "1.0",
    "unlock_methods": ["tpm", "fingerprint", "password"],
    "fallback_enabled": true,
    "emergency_password_prompt": true,
    "tpm_timeout": $UNLOCK_TIMEOUT,
    "max_retry_attempts": $MAX_RETRY_ATTEMPTS,
    "pcr_values": "$DEFAULT_PCRS",
    "created": "$(date -Iseconds)",
    "emergency_recovery_script": "$EMERGENCY_SCRIPT"
}
EOF
    
    chmod 600 "$FALLBACK_CONFIG_FILE"
    show_success "Fallback configuration created"
}

get_keyring_password() {
    show_status "Getting keyring password with validation..."
    
    local attempts=0
    local max_attempts=3
    
    while [ $attempts -lt $max_attempts ]; do
        echo -n "Enter your keyring password: "
        read -s password
        echo
        
        if [ -z "$password" ]; then
            show_error "Password cannot be empty"
            ((attempts++))
            continue
        fi
        
        # Test password by trying to unlock keyring
        show_status "Validating password..."
        if echo "$password" | timeout 10 gnome-keyring-daemon --unlock >/dev/null 2>&1; then
            show_success "Password validated successfully"
            echo "$password"
            return 0
        else
            show_error "Invalid password or keyring unlock failed"
            ((attempts++))
            if [ $attempts -lt $max_attempts ]; then
                show_info "Please try again ($((max_attempts - attempts)) attempts remaining)"
            fi
        fi
    done
    
    show_error "Maximum password attempts exceeded"
    return 1
}

encrypt_password_with_tpm() {
    local password="$1"
    show_status "Encrypting password with TPM (PCRs: $DEFAULT_PCRS)..."
    
    # Create clevis configuration
    local clevis_config="{\"pcr_ids\":\"$DEFAULT_PCRS\"}"
    echo "$clevis_config" > "$CLEVIS_CONFIG_FILE"
    chmod 600 "$CLEVIS_CONFIG_FILE"
    
    # Encrypt password with multiple attempts
    local attempts=0
    local max_attempts=3
    
    while [ $attempts -lt $max_attempts ]; do
        if echo "$password" | clevis encrypt tpm2 "$clevis_config" > "$KEYRING_PASSWORD_FILE" 2>/dev/null; then
            chmod 600 "$KEYRING_PASSWORD_FILE"
            show_success "Password encrypted with TPM successfully"
            
            # Test decryption immediately
            if clevis decrypt < "$KEYRING_PASSWORD_FILE" >/dev/null 2>&1; then
                show_success "TPM decryption test passed"
                return 0
            else
                show_warning "TPM decryption test failed, retrying..."
            fi
        fi
        
        ((attempts++))
        if [ $attempts -lt $max_attempts ]; then
            show_warning "TPM encryption attempt $attempts failed, retrying..."
            sleep 2
        fi
    done
    
    show_error "Failed to encrypt password with TPM after $max_attempts attempts"
    return 1
}

create_enhanced_unlock_script() {
    show_status "Creating enhanced unlock script with fallback logic..."
    
    local unlock_script="$TPM_KEYRING_DIR/tpm-unlock-keyring-enhanced.sh"
    
    cat > "$unlock_script" << 'EOF'
#!/bin/bash
# Enhanced TPM Keyring Unlock with Robust Fallback Logic

set -euo pipefail

# Configuration
TPM_KEYRING_DIR="$HOME/.config/tpm-keyring"
KEYRING_PASSWORD_FILE="$TPM_KEYRING_DIR/keyring-password.enc"
FALLBACK_CONFIG_FILE="$TPM_KEYRING_DIR/fallback-config.json"
LOG_FILE="$HOME/.cache/tpm-keyring-enhanced.log"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - UNLOCK: $1" >> "$LOG_FILE" 2>/dev/null || true
}

show_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
    log_message "INFO: $1"
}

show_success() {
    echo -e "${GREEN}✅ $1${NC}"
    log_message "SUCCESS: $1"
}

show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    log_message "WARNING: $1"
}

show_error() {
    echo -e "${RED}❌ $1${NC}"
    log_message "ERROR: $1"
}

check_keyring_status() {
    # Check if keyring is already unlocked
    if secret-tool search keyring login >/dev/null 2>&1; then
        return 0  # Already unlocked
    fi
    return 1  # Locked
}

tpm_unlock() {
    show_status "Attempting TPM unlock..."
    
    if [ ! -f "$KEYRING_PASSWORD_FILE" ]; then
        show_warning "TPM encrypted password file not found"
        return 1
    fi
    
    # Try to decrypt password with timeout
    local password
    if password=$(timeout 10 clevis decrypt < "$KEYRING_PASSWORD_FILE" 2>/dev/null); then
        if [ -n "$password" ]; then
            # Try to unlock keyring
            if echo "$password" | timeout 10 gnome-keyring-daemon --unlock >/dev/null 2>&1; then
                show_success "TPM unlock successful"
                return 0
            else
                show_warning "TPM decryption succeeded but keyring unlock failed"
            fi
        else
            show_warning "TPM decryption returned empty password"
        fi
    else
        show_warning "TPM decryption failed or timed out"
    fi
    
    return 1
}

fingerprint_unlock() {
    show_status "Attempting fingerprint unlock..."
    
    # Check if fingerprint authentication is available
    if ! command -v fprintd-verify >/dev/null 2>&1; then
        show_warning "Fingerprint authentication not available"
        return 1
    fi
    
    # This is a placeholder - actual implementation would integrate with PAM
    show_warning "Fingerprint unlock not yet implemented in this script"
    return 1
}

password_unlock() {
    show_status "Requesting manual password unlock..."
    
    local attempts=0
    local max_attempts=3
    
    while [ $attempts -lt $max_attempts ]; do
        echo -n "Enter keyring password: "
        read -s password
        echo
        
        if [ -n "$password" ]; then
            if echo "$password" | timeout 10 gnome-keyring-daemon --unlock >/dev/null 2>&1; then
                show_success "Password unlock successful"
                return 0
            else
                show_error "Invalid password"
            fi
        else
            show_error "Password cannot be empty"
        fi
        
        ((attempts++))
        if [ $attempts -lt $max_attempts ]; then
            echo "Please try again ($((max_attempts - attempts)) attempts remaining)"
        fi
    done
    
    show_error "Maximum password attempts exceeded"
    return 1
}

main() {
    local method="${1:-auto}"
    
    log_message "Enhanced unlock started with method: $method"
    
    # Check if already unlocked
    if check_keyring_status; then
        show_success "Keyring is already unlocked"
        return 0
    fi
    
    case "$method" in
        "tpm")
            tmp_unlock && return 0
            ;;
        "fingerprint")
            fingerprint_unlock && return 0
            ;;
        "password")
            password_unlock && return 0
            ;;
        "auto"|*)
            # Try all methods in sequence
            show_status "Starting automatic unlock sequence..."
            
            # Method 1: TPM
            if tpm_unlock; then
                return 0
            fi
            
            # Method 2: Fingerprint (if available)
            if fingerprint_unlock; then
                return 0
            fi
            
            # Method 3: Password (always available)
            if password_unlock; then
                return 0
            fi
            
            show_error "All unlock methods failed"
            return 1
            ;;
    esac
    
    show_error "Unlock method '$method' failed"
    return 1
}

# Run main function
main "$@"
EOF
    
    chmod 755 "$unlock_script"
    show_success "Enhanced unlock script created"
}

create_emergency_recovery_script() {
    show_status "Creating emergency recovery script..."

    cat > "$EMERGENCY_SCRIPT" << 'EOF'
#!/bin/bash
# Emergency TPM Recovery Script
# Use this if TPM integration completely fails

set -euo pipefail

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

show_status() { echo -e "${BLUE}ℹ️  $1${NC}"; }
show_success() { echo -e "${GREEN}✅ $1${NC}"; }
show_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
show_error() { echo -e "${RED}❌ $1${NC}"; }

emergency_recovery() {
    echo -e "${RED}🚨 EMERGENCY TPM RECOVERY MODE 🚨${NC}"
    echo "This will disable TPM integration and restore normal keyring operation"
    echo

    read -p "Continue with emergency recovery? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "Recovery cancelled"
        exit 0
    fi

    show_status "Step 1: Stopping TPM services..."
    systemctl --user stop tpm-keyring-unlock.service 2>/dev/null || true
    sudo systemctl stop tcsd 2>/dev/null || true

    show_status "Step 2: Disabling TPM autostart..."
    systemctl --user disable tpm-keyring-unlock.service 2>/dev/null || true
    rm -f ~/.config/autostart/tpm-keyring-unlock.desktop 2>/dev/null || true

    show_status "Step 3: Killing TPM processes..."
    pkill -f "tpm-unlock-keyring" 2>/dev/null || true
    pkill -f "clevis" 2>/dev/null || true

    show_status "Step 4: Restarting keyring daemon..."
    systemctl --user restart gnome-keyring-daemon

    show_status "Step 5: Manual keyring unlock..."
    echo "Please enter your keyring password:"
    gnome-keyring-daemon --unlock

    show_success "Emergency recovery completed!"
    echo
    echo "Your keyring should now work normally with password authentication."
    echo "To re-enable TPM integration later, run: tpm-enhanced-setup.sh setup"
}

disable_tpm_integration() {
    show_status "Disabling TPM integration (non-emergency)..."

    # Stop services gracefully
    systemctl --user stop tpm-keyring-unlock.service 2>/dev/null || true
    systemctl --user disable tmp-keyring-unlock.service 2>/dev/null || true

    # Remove autostart
    rm -f ~/.config/autostart/tpm-keyring-unlock.desktop 2>/dev/null || true

    # Move TPM config to backup
    if [ -d ~/.config/tpm-keyring ]; then
        mv ~/.config/tpm-keyring ~/.config/tpm-keyring.disabled.$(date +%Y%m%d_%H%M%S)
        show_success "TPM configuration backed up and disabled"
    fi

    show_success "TPM integration disabled successfully"
}

test_fallback_methods() {
    show_status "Testing fallback authentication methods..."

    echo "Testing method 1: Direct password unlock..."
    if gnome-keyring-daemon --unlock; then
        show_success "Password unlock: WORKING"
    else
        show_error "Password unlock: FAILED"
    fi

    echo
    echo "Testing method 2: Secret-tool access..."
    if secret-tool search keyring login >/dev/null 2>&1; then
        show_success "Secret-tool access: WORKING"
    else
        show_warning "Secret-tool access: No items found (normal if keyring is empty)"
    fi

    echo
    echo "Testing method 3: Keyring daemon status..."
    if systemctl --user is-active gnome-keyring-daemon >/dev/null 2>&1; then
        show_success "Keyring daemon: RUNNING"
    else
        show_error "Keyring daemon: NOT RUNNING"
    fi
}

show_usage() {
    echo "Emergency TPM Recovery Script"
    echo "============================"
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  emergency    - Emergency recovery (disables TPM, restores normal operation)"
    echo "  disable      - Gracefully disable TPM integration"
    echo "  test         - Test fallback authentication methods"
    echo "  status       - Show current TPM integration status"
    echo
}

case "${1:-help}" in
    "emergency")
        emergency_recovery
        ;;
    "disable")
        disable_tpm_integration
        ;;
    "test")
        test_fallback_methods
        ;;
    "status")
        echo "TPM Integration Status:"
        echo "======================"
        systemctl --user status tpm-keyring-unlock.service 2>/dev/null || echo "TPM service: Not configured"
        [ -f ~/.config/tpm-keyring/keyring-password.enc ] && echo "TPM encrypted password: Present" || echo "TPM encrypted password: Missing"
        ;;
    *)
        show_usage
        ;;
esac
EOF

    chmod 755 "$EMERGENCY_SCRIPT"
    show_success "Emergency recovery script created at: $EMERGENCY_SCRIPT"
}

create_systemd_service() {
    show_status "Creating enhanced systemd service..."

    local service_file="$HOME/.config/systemd/user/tpm-keyring-unlock-enhanced.service"
    mkdir -p "$(dirname "$service_file")"

    cat > "$service_file" << EOF
[Unit]
Description=Enhanced TPM Keyring Unlock with Fallback
After=graphical-session.target
Wants=graphical-session.target

[Service]
Type=oneshot
ExecStart=$TPM_KEYRING_DIR/tpm-unlock-keyring-enhanced.sh auto
RemainAfterExit=yes
TimeoutStartSec=30
Restart=on-failure
RestartSec=5

[Install]
WantedBy=default.target
EOF

    # Enable the service
    systemctl --user daemon-reload
    systemctl --user enable tpm-keyring-unlock-enhanced.service

    show_success "Enhanced systemd service created and enabled"
}

setup_enhanced_tpm() {
    show_status "Starting enhanced TPM-keyring integration setup..."

    check_prerequisites || exit 1
    setup_directories
    create_fallback_config

    # Get and encrypt keyring password
    if password=$(get_keyring_password); then
        if encrypt_password_with_tpm "$password"; then
            create_enhanced_unlock_script
            create_emergency_recovery_script
            create_systemd_service

            show_success "Enhanced TPM-keyring integration setup complete!"
            echo
            echo -e "${GREEN}🎉 Setup Summary:${NC}"
            echo "✅ TPM encryption configured with PCRs: $DEFAULT_PCRS"
            echo "✅ Fallback authentication methods enabled"
            echo "✅ Emergency recovery script created"
            echo "✅ Enhanced unlock script with timeout protection"
            echo "✅ Systemd service configured"
            echo
            echo -e "${BLUE}📋 Next Steps:${NC}"
            echo "1. Test the integration: $0 test"
            echo "2. Emergency recovery: $EMERGENCY_SCRIPT emergency"
            echo "3. Monitor logs: tail -f $LOG_FILE"
            echo
            echo -e "${YELLOW}⚠️  Important:${NC}"
            echo "• Your keyring can ALWAYS be unlocked with your password"
            echo "• Emergency recovery script is available if TPM fails"
            echo "• Multiple fallback methods ensure you won't get locked out"
        else
            show_error "Failed to encrypt password with TPM"
            exit 1
        fi
    else
        show_error "Failed to get valid keyring password"
        exit 1
    fi
}

test_enhanced_integration() {
    show_status "Testing enhanced TPM integration..."

    echo "Test 1: TPM unlock script..."
    if "$TPM_KEYRING_DIR/tpm-unlock-keyring-enhanced.sh" tpm; then
        show_success "TPM unlock: PASSED"
    else
        show_warning "TPM unlock: FAILED (fallback available)"
    fi

    echo
    echo "Test 2: Emergency recovery script..."
    if [ -x "$EMERGENCY_SCRIPT" ]; then
        show_success "Emergency recovery script: AVAILABLE"
        "$EMERGENCY_SCRIPT" test
    else
        show_error "Emergency recovery script: MISSING"
    fi

    echo
    echo "Test 3: Fallback configuration..."
    if [ -f "$FALLBACK_CONFIG_FILE" ]; then
        show_success "Fallback config: PRESENT"
        echo "Configuration:"
        cat "$FALLBACK_CONFIG_FILE" | jq . 2>/dev/null || cat "$FALLBACK_CONFIG_FILE"
    else
        show_error "Fallback config: MISSING"
    fi
}

show_usage() {
    echo "Enhanced TPM-Keyring Integration Script"
    echo "======================================"
    echo
    echo "This script provides robust TPM integration with multiple fallback methods"
    echo "to ensure you never get locked out of your keyring."
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  setup     - Setup enhanced TPM integration with fallback logic"
    echo "  test      - Test the enhanced integration and fallback methods"
    echo "  status    - Show detailed status of TPM integration"
    echo "  remove    - Remove TPM integration (keeps emergency recovery)"
    echo
    echo "Features:"
    echo "  • Multiple unlock methods: TPM → Fingerprint → Password"
    echo "  • Emergency recovery script for complete TPM failure"
    echo "  • Timeout protection to prevent hanging"
    echo "  • Comprehensive logging and error handling"
    echo "  • Password fallback always available"
    echo
}

# Main execution
case "${1:-help}" in
    "setup")
        setup_enhanced_tpm
        ;;
    "test")
        test_enhanced_integration
        ;;
    "status")
        echo "Enhanced TPM Integration Status"
        echo "==============================="
        echo
        [ -f "$FALLBACK_CONFIG_FILE" ] && echo "✅ Fallback config: Present" || echo "❌ Fallback config: Missing"
        [ -f "$KEYRING_PASSWORD_FILE" ] && echo "✅ TPM encrypted password: Present" || echo "❌ TPM encrypted password: Missing"
        [ -x "$EMERGENCY_SCRIPT" ] && echo "✅ Emergency recovery: Available" || echo "❌ Emergency recovery: Missing"
        systemctl --user is-enabled tpm-keyring-unlock-enhanced.service >/dev/null 2>&1 && echo "✅ Systemd service: Enabled" || echo "❌ Systemd service: Disabled"
        ;;
    "remove")
        show_status "Removing TPM integration..."
        systemctl --user stop tpm-keyring-unlock-enhanced.service 2>/dev/null || true
        systemctl --user disable tpm-keyring-unlock-enhanced.service 2>/dev/null || true
        mv "$TPM_KEYRING_DIR" "$TPM_KEYRING_DIR.removed.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true
        show_success "TPM integration removed (emergency recovery preserved)"
        ;;
    *)
        show_usage
        ;;
esac
