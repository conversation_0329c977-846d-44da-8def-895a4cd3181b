site_name: System CLI Documentation
site_description: Comprehensive Linux system administration CLI
site_author: <PERSON>
site_url: https://github.com/bcherrington/cursor-system

repo_name: bcherrington/cursor-system
repo_url: https://github.com/bcherrington/cursor-system

theme:
  name: material
  palette:
    - scheme: default
      primary: blue
      accent: blue
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - scheme: slate
      primary: blue
      accent: blue
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  features:
    - navigation.tabs
    - navigation.sections
    - navigation.expand
    - navigation.top
    - search.highlight
    - search.share
    - content.code.copy

nav:
  - Home: index.md
  - System Overview: system-overview.md
  - Dashboard: dashboard.md
  - Getting Started:
    - Installation: getting-started/installation.md
    - Quick Start: getting-started/quick-start.md
  - User Guides:
    - System CLI: user-guides/system-cli.md
    - Authentication: user-guides/authentication.md
    - Monitoring: user-guides/monitoring.md
  - Technical Reference:
    - Hardware Specifications: technical-reference/hardware-specifications.md
    - Software Specifications: technical-reference/software-specifications.md
    - System Configurations: technical-reference/configurations.md
    - Security Audit: technical-reference/security-audit.md
  - Development:
    - Contributing: development/contributing.md
    - Development CLI: development/dev-cli.md
    - Architecture: development/architecture.md
    - API Reference: development/api-reference.md
  - Planning:
    - Current Recommendations: planning/current-recommendations.md
    - Legacy Recommendations: planning/legacy-recommendations.md
  - Project History:
    - Documentation Reorganization: project-history/documentation-reorganization.md
    - Structure Implementation: project-history/structure-implementation.md
    - Makefile Migration: project-history/makefile-migration.md
    - Python Conversion: project-history/python-conversion.md
    - Script Organization: project-history/script-organization.md

plugins:
  - search
  - mkdocstrings:
      handlers:
        python:
          options:
            docstring_style: google
            show_source: true
            show_root_heading: true
            show_root_toc_entry: false

markdown_extensions:
  - admonition
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.tabbed:
      alternate_style: true
  - attr_list
  - md_in_html
  - toc:
      permalink: true

extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/bcherrington/cursor-system
